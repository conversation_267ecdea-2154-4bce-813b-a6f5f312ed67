#include "BSTTool.h"

#define STB_IMAGE_WRITE_IMPLEMENTATION
#include "stb_image_write.h"
 
#define STB_IMAGE_IMPLEMENTATION
#include "stb_image.h"

#include "exif.h"

#include <time.h>
#include <string>
#include <cmath>

#if __ARM_NEON
#include <arm_neon.h>
#define CNN_ARM_NEON_PROCESSING
#endif

#if !defined(ANDROID) && !defined(WIN32) && !defined(MSVC)
#include <sys/time.h>
#include <unistd.h>
#endif

#include "kanna_rotate.h"
#include <iostream>

using namespace BstCE;

namespace BstCE
{
namespace BSTTool
{
double GetTimeMS()
{
#if !defined(ANDROID) && !defined(WIN32) && !defined(MSVC)
    struct timeval tv;
    gettimeofday(&tv, NULL);

    double ms = tv.tv_sec * 1000 + tv.tv_usec / 1000.0;    
#else
    double ms = 0.0f;
#endif
    return ms;
}

void Gray2C3(const unsigned char *source, int w, int h, unsigned char *dest) 
{
    int sta = 0;
    int count = w * h;
#ifdef __ARM_NEON
    int countD8 = (int)count / 8;
    if (countD8 > 0) {
        for (int i = 0; i < countD8; ++i) {
            auto gray = vld1_u8(source + 8 * i);

            uint8x8x3_t rgba;
            rgba.val[0] = gray;
            rgba.val[1] = gray;
            rgba.val[2] = gray;
            vst3_u8(dest + 24 * i, rgba);
        }
        sta = countD8 * 8;
    }
#endif
    for (int i = sta; i < count; ++i) {
        dest[3 * i + 0] = source[i];
        dest[3 * i + 1] = source[i];
        dest[3 * i + 2] = source[i];
    }
}

void Gray2C3_CHW(const unsigned char *source, int w, int h, unsigned char *dest) 
{
    int count = w * h;
    for(int i = 0;i < count;i++)
    {
        dest[i]             = source[i];
        dest[i + count]     = source[i];
        dest[i + 2 * count] = source[i];
    }
}

void BGR2Gray(const unsigned char *source, int w, int h, unsigned char *dest)
{
    // coeffs for r g b = 0.299f, 0.587f, 0.114f
    // const unsigned char Y_shift = 8;//14
    // const unsigned char R2Y = 77;
    // const unsigned char G2Y = 150;
    // const unsigned char B2Y = 29;

    // unsigned char* ptr = gray;
    // int size = w * h;
    // int remain = size;
    // for (; remain>0; remain--)
    // {
    //     *ptr = (bgr[2] * R2Y + bgr[1] * G2Y + bgr[0] * B2Y) >> Y_shift;

    //     bgr += 3;
    //     ptr++;
    // }

    int sta = 0;
    int count = w * h;
#ifdef __ARM_NEON
    int countD8 = (int)count / 8;
    if (countD8 > 0) {
        auto rC = vdup_n_u8(19);
        auto gC = vdup_n_u8(38);
        auto bC = vdup_n_u8(7);
        for (int i = 0; i < countD8; ++i) {
            auto rgb   = vld3_u8(source + 24 * i);
            auto res   = vmull_u8(rC, rgb.val[2]) + vmull_u8(gC, rgb.val[1]) + vmull_u8(bC, rgb.val[0]);
            auto resU8 = vshrn_n_u16(res, 6);
            vst1_u8(dest + 8 * i, resU8);
        }
        sta = countD8 * 8;
    }
#endif

    for (int i = sta; i < count; ++i) {
        int r = source[3 * i + 2];
        int g = source[3 * i + 1];
        int b = source[3 * i + 0];

        int y = (19 * r + 38 * g + 7 * b) >> 6;

        dest[i] = y;
    }
}

void BGRA2Gray(const unsigned char *source, int w, int h, unsigned char *dest)
{
    // coeffs for r g b = 0.299f, 0.587f, 0.114f
    // const unsigned char Y_shift = 8;//14
    // const unsigned char R2Y = 77;
    // const unsigned char G2Y = 150;
    // const unsigned char B2Y = 29;

    // unsigned char* ptr = gray;
    // int size = w * h;
    // int remain = size;
    // for (; remain>0; remain--)
    // {
    //     *ptr = (bgr[2] * R2Y + bgr[1] * G2Y + bgr[0] * B2Y) >> Y_shift;

    //     bgr += 3;
    //     ptr++;
    // }

    int sta = 0;
    int count = w * h;
#ifdef __ARM_NEON
    int countD8 = (int)count / 8;
    if (countD8 > 0) {
        auto rC = vdup_n_u8(19);
        auto gC = vdup_n_u8(38);
        auto bC = vdup_n_u8(7);
        for (int i = 0; i < countD8; ++i) {
            auto rgb   = vld3_u8(source + 24 * i);
            auto res   = vmull_u8(rC, rgb.val[2]) + vmull_u8(gC, rgb.val[1]) + vmull_u8(bC, rgb.val[0]);
            auto resU8 = vshrn_n_u16(res, 6);
            vst1_u8(dest + 8 * i, resU8);
        }
        sta = countD8 * 8;
    }
#endif

    for (int i = sta; i < count; ++i) {
        int r = source[4 * i + 2];
        int g = source[4 * i + 1];
        int b = source[4 * i + 0];

        int y = (19 * r + 38 * g + 7 * b) >> 6;

        dest[i] = y;
    }
}

void RGB2Gray(const unsigned char *source, int w, int h, unsigned char *dest)
{
    // coeffs for r g b = 0.299f, 0.587f, 0.114f
    // const unsigned char Y_shift = 8;//14
    // const unsigned char R2Y = 77;
    // const unsigned char G2Y = 150;
    // const unsigned char B2Y = 29;

    // unsigned char* ptr = gray;
    // int size = w * h;
    // int remain = size;
    // for (; remain>0; remain--)
    // {
    //     *ptr = (rgb[0] * R2Y + rgb[1] * G2Y + rgb[2] * B2Y) >> Y_shift;

    //     rgb += 3;
    //     ptr++;
    // }

    int sta = 0;
    int count = w * h;
#ifdef __ARM_NEON
    int countD8 = (int)count / 8;
    if (countD8 > 0) {
        auto rC = vdup_n_u8(19);
        auto gC = vdup_n_u8(38);
        auto bC = vdup_n_u8(7);
        for (int i = 0; i < countD8; ++i) {
            auto rgb   = vld3_u8(source + 24 * i);
            auto res   = vmull_u8(rC, rgb.val[0]) + vmull_u8(gC, rgb.val[1]) + vmull_u8(bC, rgb.val[2]);
            auto resU8 = vshrn_n_u16(res, 6);
            vst1_u8(dest + 8 * i, resU8);
        }
        sta = countD8 * 8;
    }
#endif
    for (int i = sta; i < count; ++i) {
        int r = source[3 * i + 0];
        int g = source[3 * i + 1];
        int b = source[3 * i + 2];

        int y = (19 * r + 38 * g + 7 * b) >> 6;

        dest[i] = y;
    }  
}

void RGB2BGR(const unsigned char *source, int w, int h, unsigned char *dest)
{
    // int size = w * h;
    // int remain = size;
    // for(;remain>0;remain--)
    // {
    //     *bgr = rgb[2];
    //     *(bgr+1) = rgb[1];
    //     *(bgr+2) = rgb[0];
    //     bgr += 3;
    //     rgb += 3;
    // }

    int sta = 0;
    int count = w * h;
#ifdef __ARM_NEON
    int countD8 = (int)count / 8;
    if (countD8 > 0) {
        for (int i = 0; i < countD8; ++i) {
            uint8x8x3_t rgba = vld3_u8(source + 24 * i);
            uint8x8x3_t bgr;
            bgr.val[0] = rgba.val[2];
            bgr.val[1] = rgba.val[1];
            bgr.val[2] = rgba.val[0];
            vst3_u8(dest + 24 * i, bgr);
        }
        sta = countD8 * 8;
    }
#endif
    for (int i = sta; i < count; ++i) {
        dest[3 * i + 0] = source[3 * i + 2];
        dest[3 * i + 1] = source[3 * i + 1];
        dest[3 * i + 2] = source[3 * i + 0];
    }
}

void BGR2RGB(const unsigned char *bgr, int w, int h, unsigned char *rgb)
{
    RGB2BGR(bgr, w, h, rgb);
}

void RGBA2Gray(const unsigned char *source, int w, int h, unsigned char *dest)
{
   int sta = 0;
   int count = w * h;
#ifdef __ARM_NEON
    int countD8 = (int)count / 8;
    if (countD8 > 0) {
        auto rC = vdup_n_u8(19);
        auto gC = vdup_n_u8(38);
        auto bC = vdup_n_u8(7);
        for (int i = 0; i < countD8; ++i) {
            auto rgb   = vld4_u8(source + 32 * i);
            auto res   = vmull_u8(rC, rgb.val[0]) + vmull_u8(gC, rgb.val[1]) + vmull_u8(bC, rgb.val[2]);
            auto resU8 = vshrn_n_u16(res, 6);
            vst1_u8(dest + 8 * i, resU8);
        }
        sta = countD8 * 8;
    }
#endif
    for (int i = sta; i < count; ++i) {
        int r = source[4 * i + 0];
        int g = source[4 * i + 1];
        int b = source[4 * i + 2];

        int y = (19 * r + 38 * g + 7 * b) >> 6;

        dest[i] = y;
    }
}

void ABGR2Gray(const unsigned char *source, int w, int h, unsigned char *dest)
{
    int sta = 0;
    int count = w * h;
#ifdef __ARM_NEON
    int countD8 = (int)count / 8;
    if (countD8 > 0) {
        auto rC = vdup_n_u8(19);
        auto gC = vdup_n_u8(38);
        auto bC = vdup_n_u8(7);
        for (int i = 0; i < countD8; ++i) {
            auto rgb   = vld4_u8(source + 32 * i);
            auto res   = vmull_u8(rC, rgb.val[3]) + vmull_u8(gC, rgb.val[2]) + vmull_u8(bC, rgb.val[1]);
            auto resU8 = vshrn_n_u16(res, 6);
            vst1_u8(dest + 8 * i, resU8);
        }
        sta = countD8 * 8;
    }
#endif
    for (int i = sta; i < count; ++i) {
        int r = source[4 * i + 3];
        int g = source[4 * i + 2];
        int b = source[4 * i + 1];

        int y = (19 * r + 38 * g + 7 * b) >> 6;

        dest[i] = y;
    }
}

void C1ToFloatC1(const unsigned char* source, int w, int h, float* dest, float mean, float normal)
{
    int count = w * h;
#ifdef __ARM_NEON
    unsigned long size  = count >> 4;
    float32x4_t cache   = vdupq_n_f32(0);
    float32x4_t _mean   = vdupq_n_f32(-mean);
    float32x4_t _normal = vdupq_n_f32(normal);
    for (int i = 0; i < size; i++, source += 16) {
        uint8x16_t v = vld1q_u8(source);
        int16x8_t vl = vreinterpretq_s16_u16(vmovl_u8(vget_low_u8(v)));  // 0..7
        int16x8_t vh = vreinterpretq_s16_u16(vmovl_u8(vget_high_u8(v))); // 8..15
        // unpack to 32 bits
        float32x4_t vll = vcvtq_f32_s32(vmovl_s16(vget_low_s16(vl))); // 0..3
        cache           = vaddq_f32(_mean, vll);
        cache           = vmulq_f32(cache, _normal);
        vst1q_f32(dest, cache);
        dest += 4;
        float32x4_t vlh = vcvtq_f32_s32(vmovl_s16(vget_high_s16(vl))); // 4..7
        cache           = vaddq_f32(_mean, vlh);
        cache           = vmulq_f32(cache, _normal);
        vst1q_f32(dest, cache);
        dest += 4;
        float32x4_t vhl = vcvtq_f32_s32(vmovl_s16(vget_low_s16(vh))); // 8..11
        cache           = vaddq_f32(_mean, vhl);
        cache           = vmulq_f32(cache, _normal);
        vst1q_f32(dest, cache);
        dest += 4;
        float32x4_t vhh = vcvtq_f32_s32(vmovl_s16(vget_high_s16(vh))); // 12..15
        cache           = vaddq_f32(_mean, vhh);
        cache           = vmulq_f32(cache, _normal);
        vst1q_f32(dest, cache);
        dest += 4;
    }
    int left = count & 15;
    if (left == 0) {
        return;
    }
    for (int i = 0; i < left; ++i, ++dest, ++source) {
        *dest = normal * (*source - mean);
    }
#else
    for (int i = 0; i < count; ++i) {
        dest[i] = normal * (source[i] - mean);
    }
#endif    
}

void C1ToFloatC3_CHW(const unsigned char* source, int w, int h, float* dest, float mean, float normal)
{
    int count = w * h;
    float tmp = 0;
    for(int i = 0;i < count;i++)
    {
        tmp = (source[i] - mean) * normal;
        dest[i]             = tmp;
        dest[i + count]     = tmp;
        dest[i + 2 * count] = tmp;
    }
}



void C3ToFloatC3_CHW(const unsigned char* source, int w, int h, float* dest, const float* mean, const float* normal)
{
    int count = w * h;
    int off = 0;
    for(int i = 0;i < count;i++)
    {
        off = 3 * i;
        dest[i]             = (source[off] - mean[0]) * normal[0];
        dest[i + count]     = (source[off + 1] - mean[1]) * normal[1];
        dest[i + 2 * count] = (source[off + 2] - mean[2]) * normal[2];
    }
}

void C3ToFloatC3_CHW_BGR2RGB(const unsigned char* source, int w, int h, float* dest, const float* mean, const float* normal)
{
    int count = w * h;
    int off = 0;
    for(int i = 0;i < count;i++)
    {
        off = 3 * i;
        dest[i]             = (source[off + 2] - mean[0]) * normal[0];
        dest[i + count]     = (source[off + 1] - mean[1]) * normal[1];
        dest[i + 2 * count] = (source[off] - mean[2]) * normal[2];
    }
}

void HWC2CHW_C3(const unsigned char* source, int w, int h, unsigned char* dest)
{
    int count = w * h;
    int off = 0;
    for(int i = 0;i < count;i++)
    {
        off = 3 * i;
        dest[i]             = source[off];
        dest[i + count]     = source[off + 1];
        dest[i + 2 * count] = source[off + 2] ;
    }
}

void HWC2CHW_and_UC2F_C3(const unsigned char* source, int w, int h, float* dest)
{
    int count = w * h;
    int off = 0;
    for(int i = 0;i < count;i++)
    {
        off = 3 * i;
        dest[i]             = (float)source[off];
        dest[i + count]     = (float)source[off + 1];
        dest[i + 2 * count] = (float)source[off + 2] ;
    }
}

void C3ToFloatC3(const unsigned char* source, int w, int h, float* dest, const float* mean, const float* normal)
{
    int count = w * h;
#ifdef __ARM_NEON
    int size              = (int)count / 16;
    float32x4x3_t cachell = {vmovq_n_f32(0), vmovq_n_f32(0), vmovq_n_f32(0)};
    float32x4x3_t cachelh = {vmovq_n_f32(0), vmovq_n_f32(0), vmovq_n_f32(0)};
    float32x4x3_t cachehl = {vmovq_n_f32(0), vmovq_n_f32(0), vmovq_n_f32(0)};
    float32x4x3_t cachehh = {vmovq_n_f32(0), vmovq_n_f32(0), vmovq_n_f32(0)};
    float32x4x3_t _mean;
    float32x4x3_t _normal;
    for (int c = 0; c < 3; c++) {
        _mean.val[c]   = vmovq_n_f32(-mean[c]);
        _normal.val[c] = vmovq_n_f32(normal[c]);
    }
    for (int i = 0; i < size; i++) {
        uint8x16x3_t v = vld3q_u8(source + 16 * 3 * i);
        int c          = 0;
        {
            int16x8_t vl = vreinterpretq_s16_u16(vmovl_u8(vget_low_u8(v.val[c]))); // 0..7
            // unpack to 32 bits
            float32x4_t vll = vcvtq_f32_s32(vmovl_s16(vget_low_s16(vl))); // 0..3
            cachell.val[c]  = vaddq_f32(_mean.val[c], vll);
            cachell.val[c]  = vmulq_f32(cachell.val[c], _normal.val[c]);

            float32x4_t vlh = vcvtq_f32_s32(vmovl_s16(vget_high_s16(vl))); // 4..7
            cachelh.val[c]  = vaddq_f32(_mean.val[c], vlh);
            cachelh.val[c]  = vmulq_f32(cachelh.val[c], _normal.val[c]);

            int16x8_t vh = vreinterpretq_s16_u16(vmovl_u8(vget_high_u8(v.val[c]))); // 8..15
            // unpack to 32 bits
            float32x4_t vhl = vcvtq_f32_s32(vmovl_s16(vget_low_s16(vh))); // 8..11
            cachehl.val[c]  = vaddq_f32(_mean.val[c], vhl);
            cachehl.val[c]  = vmulq_f32(cachehl.val[c], _normal.val[c]);

            float32x4_t vhh = vcvtq_f32_s32(vmovl_s16(vget_high_s16(vh))); // 12..15
            cachehh.val[c]  = vaddq_f32(_mean.val[c], vhh);
            cachehh.val[c]  = vmulq_f32(cachehh.val[c], _normal.val[c]);
        }
        c = 1;
        {
            int16x8_t vl = vreinterpretq_s16_u16(vmovl_u8(vget_low_u8(v.val[c]))); // 0..7
            // unpack to 32 bits
            float32x4_t vll = vcvtq_f32_s32(vmovl_s16(vget_low_s16(vl))); // 0..3
            cachell.val[c]  = vaddq_f32(_mean.val[c], vll);
            cachell.val[c]  = vmulq_f32(cachell.val[c], _normal.val[c]);

            float32x4_t vlh = vcvtq_f32_s32(vmovl_s16(vget_high_s16(vl))); // 4..7
            cachelh.val[c]  = vaddq_f32(_mean.val[c], vlh);
            cachelh.val[c]  = vmulq_f32(cachelh.val[c], _normal.val[c]);

            int16x8_t vh = vreinterpretq_s16_u16(vmovl_u8(vget_high_u8(v.val[c]))); // 8..15
            // unpack to 32 bits
            float32x4_t vhl = vcvtq_f32_s32(vmovl_s16(vget_low_s16(vh))); // 8..11
            cachehl.val[c]  = vaddq_f32(_mean.val[c], vhl);
            cachehl.val[c]  = vmulq_f32(cachehl.val[c], _normal.val[c]);

            float32x4_t vhh = vcvtq_f32_s32(vmovl_s16(vget_high_s16(vh))); // 12..15
            cachehh.val[c]  = vaddq_f32(_mean.val[c], vhh);
            cachehh.val[c]  = vmulq_f32(cachehh.val[c], _normal.val[c]);
        }
        c = 2;
        {
            int16x8_t vl = vreinterpretq_s16_u16(vmovl_u8(vget_low_u8(v.val[c]))); // 0..7
            // unpack to 32 bits
            float32x4_t vll = vcvtq_f32_s32(vmovl_s16(vget_low_s16(vl))); // 0..3
            cachell.val[c]  = vaddq_f32(_mean.val[c], vll);
            cachell.val[c]  = vmulq_f32(cachell.val[c], _normal.val[c]);

            float32x4_t vlh = vcvtq_f32_s32(vmovl_s16(vget_high_s16(vl))); // 4..7
            cachelh.val[c]  = vaddq_f32(_mean.val[c], vlh);
            cachelh.val[c]  = vmulq_f32(cachelh.val[c], _normal.val[c]);

            int16x8_t vh = vreinterpretq_s16_u16(vmovl_u8(vget_high_u8(v.val[c]))); // 8..15
            // unpack to 32 bits
            float32x4_t vhl = vcvtq_f32_s32(vmovl_s16(vget_low_s16(vh))); // 8..11
            cachehl.val[c]  = vaddq_f32(_mean.val[c], vhl);
            cachehl.val[c]  = vmulq_f32(cachehl.val[c], _normal.val[c]);

            float32x4_t vhh = vcvtq_f32_s32(vmovl_s16(vget_high_s16(vh))); // 12..15
            cachehh.val[c]  = vaddq_f32(_mean.val[c], vhh);
            cachehh.val[c]  = vmulq_f32(cachehh.val[c], _normal.val[c]);
        }
        vst3q_f32(dest + 48 * i + 0 * 3, cachell);
        vst3q_f32(dest + 48 * i + 4 * 3, cachelh);
        vst3q_f32(dest + 48 * i + 8 * 3, cachehl);
        vst3q_f32(dest + 48 * i + 12 * 3, cachehh);
    }

    int remain = size * 16;
    for (int i = remain; i < count; i++) {
        dest[3 * i + 0] = normal[0] * (source[3 * i + 0] - mean[0]);
        dest[3 * i + 1] = normal[1] * (source[3 * i + 1] - mean[1]);
        dest[3 * i + 2] = normal[2] * (source[3 * i + 2] - mean[2]);
    }
#else
    for (int i = 0; i < count; ++i) {
        dest[3 * i + 0] = normal[0] * (source[3 * i + 0] - mean[0]);
        dest[3 * i + 1] = normal[1] * (source[3 * i + 1] - mean[1]);
        dest[3 * i + 2] = normal[2] * (source[3 * i + 2] - mean[2]);
    }
#endif
}

void RotateC1(unsigned char *src, int srcw, int srch, int direction, unsigned char *dst, int &dstw, int &dsth)
{
    if (direction == 0)
    {
        dstw = srcw;
        dsth = srch;
        memcpy(dst, src, srcw * srch);
    }

    else if (direction == 1 || direction == 90)
    {
        dstw = srch;
        dsth = srcw;
        unsigned char *pdst = dst;
        unsigned char *psrc = NULL;
        for (int y = 0; y < dsth; y++)
        {
            for (int x = 0; x < dstw; x++)
            {
                psrc = src + (srch - x - 1) * srcw + y;
                *pdst = *psrc;
                pdst++;
            }
        }
    }
    else if (direction == 2 || direction == 180)
    {
        dstw = srcw;
        dsth = srch;
        unsigned char *psrc = src + (srcw * srch - 1);
        unsigned char *pdst = dst;
        for (int i = 0; i < srcw * srch; ++i)
        {
            *pdst = *psrc;
            pdst++;
            psrc--;
        }
    }
    else if (direction == 3 || direction == 270)
    {
        dstw = srch;
        dsth = srcw;
        unsigned char *pdst = dst;
        unsigned char *psrc = NULL;
        for (int y = 0; y < dsth; ++y)
        {
            for (int x = 0; x < dstw; ++x)
            {
                psrc = src + (x * srcw + srcw - y - 1);
                *pdst = *psrc;
                pdst++;
            }
        }
    }
}

void RotateC3(unsigned char *src, int srcw, int srch, int direction, unsigned char *dst, int &dstw, int &dsth)
{
    if (direction == 0)
    {
        dstw = srcw;
        dsth = srch;
        memcpy(dst, src, srcw * srch * 3);
    }
    else if (direction == 1 || direction == 90)
    {
        dstw = srch;
        dsth = srcw;
        unsigned char *pdst = dst;
        unsigned char *psrc = NULL;
        for (int y = 0; y < dsth; ++y)
        {
            for (int x = 0; x < dstw; ++x)
            {
                psrc = src + ((srch - x - 1) * srcw + y) * 3;
                *pdst = *psrc;
                *(pdst + 1) = *(psrc + 1);
                *(pdst + 2) = *(psrc + 2);
                pdst += 3;
            }
        }
    }
    else if (direction == 2 || direction == 180)
    {
        dstw = srcw;
        dsth = srch;
        unsigned char *psrc = src + (srcw * srch - 1) * 3;
        unsigned char *pdst = dst;
        for (int i = 0; i < srcw * srch; ++i)
        {
            *pdst = *psrc;
            *(pdst + 1) = *(psrc + 1);
            *(pdst + 2) = *(psrc + 2);
            pdst += 3;
            psrc -= 3;
        }
    }
    else if (direction == 3 || direction == 270)
    {
        dstw = srch;
        dsth = srcw;
        unsigned char *pdst = dst;
        unsigned char *psrc = NULL;
        for (int y = 0; y < dsth; ++y)
        {
            //psrc = src + (src_width-y-1)*3;
            for (int x = 0; x < dstw; ++x)
            {
                psrc = src + (x * srcw + srcw - y - 1) * 3;
                *pdst = *psrc;
                *(pdst + 1) = *(psrc + 1);
                *(pdst + 2) = *(psrc + 2);
                pdst += 3;
                //psrc += src_width*3;
            }
        }
    }
}

void KannaRotateC1(unsigned char *src, int srcw, int srch, int direction, unsigned char *dst, int &dstw, int &dsth)
{
    if (direction == 0)
    {
        dstw = srcw;
        dsth = srch;
        memcpy(dst, src, srcw * srch);
    }
    else if (direction == 1 || direction == 90)
    {
        //顺时针旋转90度
        dstw = srch;
        dsth = srcw;
        kanna_rotate_c1_no_padding(src, srcw, srch, dst, dstw, dsth, 6);
    }
    else if (direction == 2 || direction == 180)
    {
        //顺时针旋转180度
        dstw = srcw;
        dsth = srch;
        kanna_rotate_c1_no_padding(src, srcw, srch, dst, dstw, dsth, 3);
    }
    else if (direction == 3 || direction == 270)
    {
        //顺时针旋转270度
        dstw = srch;
        dsth = srcw;
        kanna_rotate_c1_no_padding(src, srcw, srch, dst, dstw, dsth, 8);
    }    
}

void KannaRotateC3(unsigned char *src, int srcw, int srch, int direction, unsigned char *dst, int &dstw, int &dsth)
{
    if (direction == 0)
    {
        dstw = srcw;
        dsth = srch;
        memcpy(dst, src, srcw * srch * 3);
    }
    else if (direction == 1 || direction == 90)
    {
        //顺时针旋转90度
        dstw = srch;
        dsth = srcw;
        kanna_rotate_c3_no_padding(src, srcw, srch, dst, dstw, dsth, 6);
    }
    else if (direction == 2 || direction == 180)
    {
        //顺时针旋转180度
        dstw = srcw;
        dsth = srch;
        kanna_rotate_c3_no_padding(src, srcw, srch, dst, dstw, dsth, 3);
    }
    else if (direction == 3 || direction == 270)
    {
        //顺时针旋转270度
        dstw = srch;
        dsth = srcw;
        kanna_rotate_c3_no_padding(src, srcw, srch, dst, dstw, dsth, 8);
    }  
}

void NCNNResizeBilinearC3(const unsigned char* src, int srcw, int srch, unsigned char* dst, int w, int h)
{
    const int INTER_RESIZE_COEF_BITS=11;
    const int INTER_RESIZE_COEF_SCALE=1 << INTER_RESIZE_COEF_BITS;
//     const int ONE=INTER_RESIZE_COEF_SCALE;
    int srcstride = srcw * 3;
    int stride = w * 3;

    double scale_x = (double)srcw / w;
    double scale_y = (double)srch / h;

    int* buf = new int[w + h + w + h];

    int* xofs = buf;//new int[w];
    int* yofs = buf + w;//new int[h];

    short* ialpha = (short*)(buf + w + h);//new short[w * 2];
    short* ibeta = (short*)(buf + w + h + w);//new short[h * 2];

    float fx;
    float fy;
    int sx;
    int sy;

#define SATURATE_CAST_SHORT(X) (short)::std::min(::std::max((int)(X + (X >= 0.f ? 0.5f : -0.5f)), SHRT_MIN), SHRT_MAX);

    for (int dx = 0; dx < w; dx++)
    {
        fx = (float)((dx + 0.5) * scale_x - 0.5);
        sx = static_cast<int>(floor(fx));
        fx -= sx;

        if (sx < 0)
        {
            sx = 0;
            fx = 0.f;
        }
        if (sx >= srcw - 1)
        {
            sx = srcw - 2;
            fx = 1.f;
        }

        xofs[dx] = sx*3;

        float a0 = (1.f - fx) * INTER_RESIZE_COEF_SCALE;
        float a1 =        fx  * INTER_RESIZE_COEF_SCALE;

        ialpha[dx*2    ] = SATURATE_CAST_SHORT(a0);
        ialpha[dx*2 + 1] = SATURATE_CAST_SHORT(a1);
    }

    for (int dy = 0; dy < h; dy++)
    {
        fy = (float)((dy + 0.5) * scale_y - 0.5);
        sy = static_cast<int>(floor(fy));
        fy -= sy;

        if (sy < 0)
        {
            sy = 0;
            fy = 0.f;
        }
        if (sy >= srch - 1)
        {
            sy = srch - 2;
            fy = 1.f;
        }

        yofs[dy] = sy;

        float b0 = (1.f - fy) * INTER_RESIZE_COEF_SCALE;
        float b1 =        fy  * INTER_RESIZE_COEF_SCALE;

        ibeta[dy*2    ] = SATURATE_CAST_SHORT(b0);
        ibeta[dy*2 + 1] = SATURATE_CAST_SHORT(b1);
    }

#undef SATURATE_CAST_SHORT

    // loop body
    // Mat rowsbuf0(w*3+1, (size_t)2u);
    // Mat rowsbuf1(w*3+1, (size_t)2u);
    void* rowsbuf0 = (void*)malloc(2 *(w*3 + 1));
    void* rowsbuf1 = (void*)malloc(2 *(w*3 + 1));
    memset(rowsbuf0, 0, 2 *(w*3 + 1));
    memset(rowsbuf1, 0, 2 *(w*3 + 1));

    short* rows0 = (short*)rowsbuf0;
    short* rows1 = (short*)rowsbuf1;

    int prev_sy1 = -2;

    for (int dy = 0; dy < h; dy++ )
    {
        int sy = yofs[dy];

        if (sy == prev_sy1)
        {
            // reuse all rows
        }
        else if (sy == prev_sy1 + 1)
        {
            // hresize one row
            short* rows0_old = rows0;
            rows0 = rows1;
            rows1 = rows0_old;
            const unsigned char *S1 = src + srcstride * (sy+1);

            const short* ialphap = ialpha;
            short* rows1p = rows1;
            for ( int dx = 0; dx < w; dx++ )
            {
                int sx = xofs[dx];
                short a0 = ialphap[0];
                short a1 = ialphap[1];

                const unsigned char* S1p = S1 + sx;
#if __ARM_NEON
                int16x4_t _a0 = vdup_n_s16(a0);
                int16x4_t _a1 = vdup_n_s16(a1);
                uint8x8_t _S1 = uint8x8_t();

                _S1 = vld1_lane_u8(S1p, _S1, 0);
                _S1 = vld1_lane_u8(S1p+1, _S1, 1);
                _S1 = vld1_lane_u8(S1p+2, _S1, 2);
                _S1 = vld1_lane_u8(S1p+3, _S1, 3);
                _S1 = vld1_lane_u8(S1p+4, _S1, 4);
                _S1 = vld1_lane_u8(S1p+5, _S1, 5);

                int16x8_t _S116 = vreinterpretq_s16_u16(vmovl_u8(_S1));
                int16x4_t _S1low = vget_low_s16(_S116);
                int16x4_t _S1high = vext_s16(_S1low, vget_high_s16(_S116), 3);
                int32x4_t _rows1 = vmull_s16(_S1low, _a0);
                _rows1 = vmlal_s16(_rows1, _S1high, _a1);
                int16x4_t _rows1_sr4 = vshrn_n_s32(_rows1, 4);
                vst1_s16(rows1p, _rows1_sr4);
#else
                rows1p[0] = (S1p[0]*a0 + S1p[3]*a1) >> 4;
                rows1p[1] = (S1p[1]*a0 + S1p[4]*a1) >> 4;
                rows1p[2] = (S1p[2]*a0 + S1p[5]*a1) >> 4;
#endif // __ARM_NEON

                ialphap += 2;
                rows1p += 3;
            }
        }
        else
        {
            // hresize two rows
            const unsigned char *S0 = src + srcstride * (sy);
            const unsigned char *S1 = src + srcstride * (sy+1);

            const short* ialphap = ialpha;
            short* rows0p = rows0;
            short* rows1p = rows1;
            for ( int dx = 0; dx < w; dx++ )
            {
                int sx = xofs[dx];
                short a0 = ialphap[0];
                short a1 = ialphap[1];

                const unsigned char* S0p = S0 + sx;
                const unsigned char* S1p = S1 + sx;
#if __ARM_NEON
                int16x4_t _a0 = vdup_n_s16(a0);
                int16x4_t _a1 = vdup_n_s16(a1);
                uint8x8_t _S0 = uint8x8_t();
                uint8x8_t _S1 = uint8x8_t();

                _S0 = vld1_lane_u8(S0p, _S0, 0);
                _S0 = vld1_lane_u8(S0p+1, _S0, 1);
                _S0 = vld1_lane_u8(S0p+2, _S0, 2);
                _S0 = vld1_lane_u8(S0p+3, _S0, 3);
                _S0 = vld1_lane_u8(S0p+4, _S0, 4);
                _S0 = vld1_lane_u8(S0p+5, _S0, 5);

                _S1 = vld1_lane_u8(S1p, _S1, 0);
                _S1 = vld1_lane_u8(S1p+1, _S1, 1);
                _S1 = vld1_lane_u8(S1p+2, _S1, 2);
                _S1 = vld1_lane_u8(S1p+3, _S1, 3);
                _S1 = vld1_lane_u8(S1p+4, _S1, 4);
                _S1 = vld1_lane_u8(S1p+5, _S1, 5);

                int16x8_t _S016 = vreinterpretq_s16_u16(vmovl_u8(_S0));
                int16x8_t _S116 = vreinterpretq_s16_u16(vmovl_u8(_S1));
                int16x4_t _S0low = vget_low_s16(_S016);
                int16x4_t _S1low = vget_low_s16(_S116);
                int16x4_t _S0high = vext_s16(_S0low, vget_high_s16(_S016), 3);
                int16x4_t _S1high = vext_s16(_S1low, vget_high_s16(_S116), 3);
                int32x4_t _rows0 = vmull_s16(_S0low, _a0);
                int32x4_t _rows1 = vmull_s16(_S1low, _a0);
                _rows0 = vmlal_s16(_rows0, _S0high, _a1);
                _rows1 = vmlal_s16(_rows1, _S1high, _a1);
                int16x4_t _rows0_sr4 = vshrn_n_s32(_rows0, 4);
                int16x4_t _rows1_sr4 = vshrn_n_s32(_rows1, 4);
                vst1_s16(rows0p, _rows0_sr4);
                vst1_s16(rows1p, _rows1_sr4);
#else
                rows0p[0] = (S0p[0]*a0 + S0p[3]*a1) >> 4;
                rows0p[1] = (S0p[1]*a0 + S0p[4]*a1) >> 4;
                rows0p[2] = (S0p[2]*a0 + S0p[5]*a1) >> 4;
                rows1p[0] = (S1p[0]*a0 + S1p[3]*a1) >> 4;
                rows1p[1] = (S1p[1]*a0 + S1p[4]*a1) >> 4;
                rows1p[2] = (S1p[2]*a0 + S1p[5]*a1) >> 4;
#endif // __ARM_NEON

                ialphap += 2;
                rows0p += 3;
                rows1p += 3;
            }
        }

        prev_sy1 = sy;

        // vresize
        short b0 = ibeta[0];
        short b1 = ibeta[1];

        short* rows0p = rows0;
        short* rows1p = rows1;
        unsigned char* Dp = dst + stride * (dy);

#if __ARM_NEON
        int nn = (w * 3) >> 3;
#else
        int nn = 0;
#endif
        int remain = (w * 3) - (nn << 3);

#if __ARM_NEON
#if __aarch64__
        int16x4_t _b0 = vdup_n_s16(b0);
        int16x4_t _b1 = vdup_n_s16(b1);
        int32x4_t _v2 = vdupq_n_s32(2);
        for (; nn>0; nn--)
        {
            int16x4_t _rows0p_sr4 = vld1_s16(rows0p);
            int16x4_t _rows1p_sr4 = vld1_s16(rows1p);
            int16x4_t _rows0p_1_sr4 = vld1_s16(rows0p+4);
            int16x4_t _rows1p_1_sr4 = vld1_s16(rows1p+4);

            int32x4_t _rows0p_sr4_mb0 = vmull_s16(_rows0p_sr4, _b0);
            int32x4_t _rows1p_sr4_mb1 = vmull_s16(_rows1p_sr4, _b1);
            int32x4_t _rows0p_1_sr4_mb0 = vmull_s16(_rows0p_1_sr4, _b0);
            int32x4_t _rows1p_1_sr4_mb1 = vmull_s16(_rows1p_1_sr4, _b1);

            int32x4_t _acc = _v2;
            _acc = vsraq_n_s32(_acc, _rows0p_sr4_mb0, 16);
            _acc = vsraq_n_s32(_acc, _rows1p_sr4_mb1, 16);

            int32x4_t _acc_1 = _v2;
            _acc_1 = vsraq_n_s32(_acc_1, _rows0p_1_sr4_mb0, 16);
            _acc_1 = vsraq_n_s32(_acc_1, _rows1p_1_sr4_mb1, 16);

            int16x4_t _acc16 = vshrn_n_s32(_acc, 2);
            int16x4_t _acc16_1 = vshrn_n_s32(_acc_1, 2);

            uint8x8_t _D = vqmovun_s16(vcombine_s16(_acc16, _acc16_1));

            vst1_u8(Dp, _D);

            Dp += 8;
            rows0p += 8;
            rows1p += 8;
        }
#else
        if (nn > 0)
        {
        asm volatile(
            "vdup.s16   d16, %8         \n"
            "mov        r4, #2          \n"
            "vdup.s16   d17, %9         \n"
            "vdup.s32   q12, r4         \n"
            "pld        [%0, #128]      \n"
            "vld1.s16   {d2-d3}, [%0 :128]!\n"
            "pld        [%1, #128]      \n"
            "vld1.s16   {d6-d7}, [%1 :128]!\n"
            "0:                         \n"
            "vmull.s16  q0, d2, d16     \n"
            "vmull.s16  q1, d3, d16     \n"
            "vorr.s32   q10, q12, q12   \n"
            "vorr.s32   q11, q12, q12   \n"
            "vmull.s16  q2, d6, d17     \n"
            "vmull.s16  q3, d7, d17     \n"
            "vsra.s32   q10, q0, #16    \n"
            "vsra.s32   q11, q1, #16    \n"
            "pld        [%0, #128]      \n"
            "vld1.s16   {d2-d3}, [%0 :128]!\n"
            "vsra.s32   q10, q2, #16    \n"
            "vsra.s32   q11, q3, #16    \n"
            "pld        [%1, #128]      \n"
            "vld1.s16   {d6-d7}, [%1 :128]!\n"
            "vshrn.s32  d20, q10, #2    \n"
            "vshrn.s32  d21, q11, #2    \n"
            "vqmovun.s16 d20, q10        \n"
            "vst1.8     {d20}, [%2]!    \n"
            "subs       %3, #1          \n"
            "bne        0b              \n"
            "sub        %0, #16         \n"
            "sub        %1, #16         \n"
            : "=r"(rows0p), // %0
              "=r"(rows1p), // %1
              "=r"(Dp),     // %2
              "=r"(nn)      // %3
            : "0"(rows0p),
              "1"(rows1p),
              "2"(Dp),
              "3"(nn),
              "r"(b0),      // %8
              "r"(b1)       // %9
            : "cc", "memory", "r4", "q0", "q1", "q2", "q3", "q8", "q9", "q10", "q11", "q12"
        );
        }
#endif // __aarch64__
#endif // __ARM_NEON
        for ( ; remain; --remain )
        {
//             D[x] = (rows0[x]*b0 + rows1[x]*b1) >> INTER_RESIZE_COEF_BITS;
            *Dp++ = (unsigned char)(( (short)((b0 * (short)(*rows0p++)) >> 16) + (short)((b1 * (short)(*rows1p++)) >> 16) + 2)>>2);
        }

        ibeta += 2;
    }

    delete[] buf;
    free(rowsbuf0);
    free(rowsbuf1);
}

//Parameter definition for bilinear image downsampling
#define QRBAR_SHIFTBITS    8
#define QRBAR_ROUND0(x)  (x>>QRBAR_SHIFTBITS)
#define QRBAR_ROUND1(x)  (ROUND0(x))+1
#ifndef QRBAR_CLIP
#define QRBAR_CLIP(x) ( x<0 ? 0 : (x>255 ? 255 : x) )
#endif
void ResizeBilinearC1(unsigned char * pSrcImg, int srcWidth, int srcHeight, unsigned char * pDesImg, int desWidth, int desHeight)
{
    int i, j;
	int nRateW, nRateH;
	//int y;
	unsigned short *coord_x, *coord_y;
	unsigned char *bi_coef_x, *bi_coef_y;
	unsigned char *sub_bi_coef_x, *sub_bi_coef_y;
	unsigned char *pImg_left_top, *pImg_left_down;
	unsigned char *pImg_right_top, *pImg_right_down;
	int float_value;
	const short std_1_value = (1 << QRBAR_SHIFTBITS);
	const short std_and_value = ((1 << QRBAR_SHIFTBITS) - 1);
	int res_x0, res_x1;
	int res_xy;
	unsigned char *pSrc, *pSrc1;
	
	pImg_left_top = (unsigned char *)malloc(sizeof(char)*desWidth);
	pImg_left_down = (unsigned char *)malloc(sizeof(char)*desWidth);
	pImg_right_top = (unsigned char *)malloc(sizeof(char)*desWidth);
	pImg_right_down = (unsigned char *)malloc(sizeof(char)*desWidth);
	
	coord_x = (unsigned short *)malloc(sizeof(short)*desWidth);
	coord_y = (unsigned short *)malloc(sizeof(short)*desHeight);
	bi_coef_x = (unsigned char *)malloc(sizeof(char)*desWidth);
	bi_coef_y = (unsigned char *)malloc(sizeof(char)*desHeight);
	sub_bi_coef_x = (unsigned char *)malloc(sizeof(char)*desWidth);
	sub_bi_coef_y = (unsigned char *)malloc(sizeof(char)*desHeight);
	
	if (coord_x == NULL || coord_y == NULL || bi_coef_x == NULL || bi_coef_y == NULL || sub_bi_coef_x == NULL || sub_bi_coef_y == NULL ||
		pImg_left_top == NULL || pImg_left_down == NULL || pImg_right_top == NULL || pImg_right_down == NULL)
		return;
	
	nRateW = (srcWidth << QRBAR_SHIFTBITS) / (desWidth);
	nRateH = (srcHeight << QRBAR_SHIFTBITS) / (desHeight);
	
	for (i = 0; i < desHeight; ++i)
	{
		float_value = i * nRateH;
		bi_coef_y[i] = float_value & std_and_value;
		if (!bi_coef_y[i])
			bi_coef_y[i] = 1;
		sub_bi_coef_y[i] = std_1_value - bi_coef_y[i];
		coord_y[i] = QRBAR_ROUND0(float_value);
		if (coord_y[i] > srcHeight - 2)
			coord_y[i] = srcHeight - 2;
	}
	
	for (i = 0; i < desWidth; ++i)
	{
		float_value = i * nRateW;
		bi_coef_x[i] = float_value & std_and_value;
		if (!bi_coef_x[i])
			bi_coef_x[i] = 1;
		sub_bi_coef_x[i] = std_1_value - bi_coef_x[i];
		coord_x[i] = QRBAR_ROUND0(float_value);
		if (coord_x[i] > srcWidth - 2)
		coord_x[i] = srcWidth - 2;
	}
	
	for (i = 0; i < desHeight; i++)
	{
		pSrc = pSrcImg + coord_y[i] * srcWidth;
		// y = coord_y[i];
		for (j = 0; j < desWidth; j++)
		{
			pSrc1 = pSrc;
			pSrc1 += coord_x[j];
			pImg_left_top[j] = *pSrc1;
			pImg_right_top[j] = *(pSrc1 + 1);
			pSrc1 += srcWidth;
			pImg_left_down[j] = *pSrc1;
			pImg_right_down[j] = *(pSrc1 + 1);
		}
	
		#ifdef CNN_ARM_NEON_PROCESSING
        int destWidthTmp = desWidth - 7;

		unsigned char *img1, *img2, *img3, *img4;
		unsigned char *outimg;
		unsigned char *coef1, *coef2, *coef3, *coef4;
		unsigned char *coef_x1, *coef_x2;
		uint8x8_t value_y1, value_y2;
	
		value_y1 = vdup_n_u8(sub_bi_coef_y[i]);
		value_y2 = vdup_n_u8(bi_coef_y[i]);
		img1 = pImg_left_top;
		img2 = pImg_right_top;
		img3 = pImg_left_down;
		img4 = pImg_right_down;
		outimg = pDesImg + i*desWidth;
		coef_x1 = sub_bi_coef_x;
		coef_x2 = bi_coef_x;
		for (j = 0; j < destWidthTmp; j += 8)
		{
			uint8x8_t v1, v2, v3, v4;
			uint8x8_t value_x1, value_x2;
			uint8x8_t bires_x0, bires_x1, bires_xy;
			uint16x8_t multi_value;
	
			v1 = vld1_u8(img1);
			v2 = vld1_u8(img2);
			v3 = vld1_u8(img3);
			v4 = vld1_u8(img4);
	
			value_x1 = vld1_u8(coef_x1);
			value_x2 = vld1_u8(coef_x2);
			multi_value = vmull_u8(v1, value_x1);
			multi_value = vmlal_u8(multi_value, v2, value_x2);
			bires_x0 = vshrn_n_u16(multi_value, QRBAR_SHIFTBITS);
			multi_value = vmull_u8(v3, value_x1);
			multi_value = vmlal_u8(multi_value, v4, value_x2);
			bires_x1 = vshrn_n_u16(multi_value, QRBAR_SHIFTBITS);
			multi_value = vmull_u8(bires_x0, value_y1);
			multi_value = vmlal_u8(multi_value, bires_x1, value_y2);
			bires_xy = vshrn_n_u16(multi_value, QRBAR_SHIFTBITS);
			vst1_u8(outimg, bires_xy);
	
			outimg += 8;
			img1 += 8;
			img2 += 8;
			img3 += 8;
			img4 += 8;
			coef_x1 += 8;
			coef_x2 += 8;
		}
		for (; j < desWidth; j++)
		{
			res_x0 = (((*img1)*sub_bi_coef_x[j] + (*img2)*bi_coef_x[j]) >> QRBAR_SHIFTBITS);
			res_x1 = (((*img3)*sub_bi_coef_x[j] + (*img4)*bi_coef_x[j]) >> QRBAR_SHIFTBITS);
			res_xy = res_x0*sub_bi_coef_y[i] + res_x1*bi_coef_y[i];
			*outimg = (res_xy >> QRBAR_SHIFTBITS);
			outimg++;
			img1++;
			img2++;
			img3++;
			img4++;
		}
		#else
		for (j = 0; j < desWidth; j++)
		{
			res_x0 = ((pImg_left_top[j] * sub_bi_coef_x[j] + pImg_right_top[j] * bi_coef_x[j]) >> QRBAR_SHIFTBITS);
			res_x1 = ((pImg_left_down[j] * sub_bi_coef_x[j] + pImg_right_down[j] * bi_coef_x[j]) >> QRBAR_SHIFTBITS);
			res_xy = res_x0*sub_bi_coef_y[i] + res_x1*bi_coef_y[i];
			pDesImg[i*desWidth + j] = (res_xy >> QRBAR_SHIFTBITS);
		}
		#endif
	}
	
	free(coord_x);
	free(coord_y);
	free(bi_coef_x);
	free(bi_coef_y);
	free(sub_bi_coef_x);
	free(sub_bi_coef_y);
	free(pImg_left_top);
	free(pImg_left_down);
	free(pImg_right_top);
	free(pImg_right_down);
}

void NCNNResizeBilinearC1(unsigned char* src, int srcw, int srch, unsigned char* dst, int w, int h)
{
    const int INTER_RESIZE_COEF_BITS=11;
    const int INTER_RESIZE_COEF_SCALE=1 << INTER_RESIZE_COEF_BITS;
//     const int ONE=INTER_RESIZE_COEF_SCALE;

    int srcstride = srcw;
    int stride = w;

    double scale_x = (double)srcw / w;
    double scale_y = (double)srch / h;

    int* buf = new int[w + h + w + h];

    int* xofs = buf;//new int[w];
    int* yofs = buf + w;//new int[h];

    short* ialpha = (short*)(buf + w + h);//new short[w * 2];
    short* ibeta = (short*)(buf + w + h + w);//new short[h * 2];

    float fx;
    float fy;
    int sx;
    int sy;

#define SATURATE_CAST_SHORT(X) (short)::std::min(::std::max((int)(X + (X >= 0.f ? 0.5f : -0.5f)), SHRT_MIN), SHRT_MAX);

    for (int dx = 0; dx < w; dx++)
    {
        fx = (float)((dx + 0.5) * scale_x - 0.5);
        sx = static_cast<int>(floor(fx));
        fx -= sx;

        if (sx < 0)
        {
            sx = 0;
            fx = 0.f;
        }
        if (sx >= srcw - 1)
        {
            sx = srcw - 2;
            fx = 1.f;
        }

        xofs[dx] = sx;

        float a0 = (1.f - fx) * INTER_RESIZE_COEF_SCALE;
        float a1 =        fx  * INTER_RESIZE_COEF_SCALE;

        ialpha[dx*2    ] = SATURATE_CAST_SHORT(a0);
        ialpha[dx*2 + 1] = SATURATE_CAST_SHORT(a1);
    }

    for (int dy = 0; dy < h; dy++)
    {
        fy = (float)((dy + 0.5) * scale_y - 0.5);
        sy = static_cast<int>(floor(fy));
        fy -= sy;

        if (sy < 0)
        {
            sy = 0;
            fy = 0.f;
        }
        if (sy >= srch - 1)
        {
            sy = srch - 2;
            fy = 1.f;
        }

        yofs[dy] = sy;

        float b0 = (1.f - fy) * INTER_RESIZE_COEF_SCALE;
        float b1 =        fy  * INTER_RESIZE_COEF_SCALE;

        ibeta[dy*2    ] = SATURATE_CAST_SHORT(b0);
        ibeta[dy*2 + 1] = SATURATE_CAST_SHORT(b1);
    }

#undef SATURATE_CAST_SHORT

    // loop body
    //Mat rowsbuf0(w, (size_t)2u);
    //Mat rowsbuf1(w, (size_t)2u);
    void* rowsbuf0 = (void*)malloc(2 * w);
    void* rowsbuf1 = (void*)malloc(2 * w);
    memset(rowsbuf0, 0, 2 * w);
    memset(rowsbuf1, 0, 2 * w);
    
    short* rows0 = (short*)rowsbuf0;
    short* rows1 = (short*)rowsbuf1;

    int prev_sy1 = -2;

    for (int dy = 0; dy < h; dy++ )
    {
        int sy = yofs[dy];

        if (sy == prev_sy1)
        {
            // reuse all rows
        }
        else if (sy == prev_sy1 + 1)
        {
            // hresize one row
            short* rows0_old = rows0;
            rows0 = rows1;
            rows1 = rows0_old;
            const unsigned char *S1 = src + srcstride * (sy+1);

            const short* ialphap = ialpha;
            short* rows1p = rows1;
            for ( int dx = 0; dx < w; dx++ )
            {
                int sx = xofs[dx];
                short a0 = ialphap[0];
                short a1 = ialphap[1];

                const unsigned char* S1p = S1 + sx;
                rows1p[dx] = (S1p[0]*a0 + S1p[1]*a1) >> 4;

                ialphap += 2;
            }
        }
        else
        {
            // hresize two rows
            const unsigned char *S0 = src + srcstride * (sy);
            const unsigned char *S1 = src + srcstride * (sy+1);

            const short* ialphap = ialpha;
            short* rows0p = rows0;
            short* rows1p = rows1;
            for ( int dx = 0; dx < w; dx++ )
            {
                int sx = xofs[dx];
                short a0 = ialphap[0];
                short a1 = ialphap[1];

                const unsigned char* S0p = S0 + sx;
                const unsigned char* S1p = S1 + sx;
                rows0p[dx] = (S0p[0]*a0 + S0p[1]*a1) >> 4;
                rows1p[dx] = (S1p[0]*a0 + S1p[1]*a1) >> 4;

                ialphap += 2;
            }
        }

        prev_sy1 = sy;

        // vresize
        short b0 = ibeta[0];
        short b1 = ibeta[1];

        short* rows0p = rows0;
        short* rows1p = rows1;
        unsigned char* Dp = dst + stride * (dy);

#if __ARM_NEON
        int nn = w >> 3;
#else
        int nn = 0;
#endif
        int remain = w - (nn << 3);

#if __ARM_NEON
#if __aarch64__
        int16x4_t _b0 = vdup_n_s16(b0);
        int16x4_t _b1 = vdup_n_s16(b1);
        int32x4_t _v2 = vdupq_n_s32(2);
        for (; nn>0; nn--)
        {
            int16x4_t _rows0p_sr4 = vld1_s16(rows0p);
            int16x4_t _rows1p_sr4 = vld1_s16(rows1p);
            int16x4_t _rows0p_1_sr4 = vld1_s16(rows0p+4);
            int16x4_t _rows1p_1_sr4 = vld1_s16(rows1p+4);

            int32x4_t _rows0p_sr4_mb0 = vmull_s16(_rows0p_sr4, _b0);
            int32x4_t _rows1p_sr4_mb1 = vmull_s16(_rows1p_sr4, _b1);
            int32x4_t _rows0p_1_sr4_mb0 = vmull_s16(_rows0p_1_sr4, _b0);
            int32x4_t _rows1p_1_sr4_mb1 = vmull_s16(_rows1p_1_sr4, _b1);

            int32x4_t _acc = _v2;
            _acc = vsraq_n_s32(_acc, _rows0p_sr4_mb0, 16);
            _acc = vsraq_n_s32(_acc, _rows1p_sr4_mb1, 16);

            int32x4_t _acc_1 = _v2;
            _acc_1 = vsraq_n_s32(_acc_1, _rows0p_1_sr4_mb0, 16);
            _acc_1 = vsraq_n_s32(_acc_1, _rows1p_1_sr4_mb1, 16);

            int16x4_t _acc16 = vshrn_n_s32(_acc, 2);
            int16x4_t _acc16_1 = vshrn_n_s32(_acc_1, 2);

            uint8x8_t _D = vqmovun_s16(vcombine_s16(_acc16, _acc16_1));

            vst1_u8(Dp, _D);

            Dp += 8;
            rows0p += 8;
            rows1p += 8;
        }
#else
        if (nn > 0)
        {
        asm volatile(
            "vdup.s16   d16, %8         \n"
            "mov        r4, #2          \n"
            "vdup.s16   d17, %9         \n"
            "vdup.s32   q12, r4         \n"
            "pld        [%0, #128]      \n"
            "vld1.s16   {d2-d3}, [%0 :128]!\n"
            "pld        [%1, #128]      \n"
            "vld1.s16   {d6-d7}, [%1 :128]!\n"
            "0:                         \n"
            "vmull.s16  q0, d2, d16     \n"
            "vmull.s16  q1, d3, d16     \n"
            "vorr.s32   q10, q12, q12   \n"
            "vorr.s32   q11, q12, q12   \n"
            "vmull.s16  q2, d6, d17     \n"
            "vmull.s16  q3, d7, d17     \n"
            "vsra.s32   q10, q0, #16    \n"
            "vsra.s32   q11, q1, #16    \n"
            "pld        [%0, #128]      \n"
            "vld1.s16   {d2-d3}, [%0 :128]!\n"
            "vsra.s32   q10, q2, #16    \n"
            "vsra.s32   q11, q3, #16    \n"
            "pld        [%1, #128]      \n"
            "vld1.s16   {d6-d7}, [%1 :128]!\n"
            "vshrn.s32  d20, q10, #2    \n"
            "vshrn.s32  d21, q11, #2    \n"
            "vqmovun.s16 d20, q10        \n"
            "vst1.8     {d20}, [%2]!    \n"
            "subs       %3, #1          \n"
            "bne        0b              \n"
            "sub        %0, #16         \n"
            "sub        %1, #16         \n"
            : "=r"(rows0p), // %0
              "=r"(rows1p), // %1
              "=r"(Dp),     // %2
              "=r"(nn)      // %3
            : "0"(rows0p),
              "1"(rows1p),
              "2"(Dp),
              "3"(nn),
              "r"(b0),      // %8
              "r"(b1)       // %9
            : "cc", "memory", "r4", "q0", "q1", "q2", "q3", "q8", "q9", "q10", "q11", "q12"
        );
        }
#endif // __aarch64__
#endif // __ARM_NEON
        for ( ; remain; --remain )
        {
//             D[x] = (rows0[x]*b0 + rows1[x]*b1) >> INTER_RESIZE_COEF_BITS;
            *Dp++ = (unsigned char)(( (short)((b0 * (short)(*rows0p++)) >> 16) + (short)((b1 * (short)(*rows1p++)) >> 16) + 2)>>2);
        }

        ibeta += 2;
    }

    delete[] buf;
    free(rowsbuf0);
    free(rowsbuf1);
}

void CropImageC1(const unsigned char *src, int srcw, int srch, unsigned char *dst, int rx, int ry, int rw, int rh)
{
    const unsigned char* p_row_src = src + ry * srcw + rx;
    unsigned char* p_row_dst = dst;
    for(int i = 0;i < rh;i++)
    {
        memcpy(p_row_dst, p_row_src, rw);

        p_row_src += srcw;
        p_row_dst += rw;
    }
}

void CropImageC3(const unsigned char *src, int srcw, int srch, unsigned char *dst, int rx, int ry, int rw, int rh)
{
    const unsigned char* p_row_src = src + (ry * srcw + rx) * 3;
    unsigned char* p_row_dst = dst;    
    int srcstride = srcw * 3;
    int dststride = rw * 3;
    for(int i = 0;i < rh;i++)
    {
        memcpy(p_row_dst, p_row_src, rw * 3);
        p_row_src += srcstride;
        p_row_dst += dststride;
    }
}

void CropImageYUV420(const unsigned char *src, int srcw, int srch, unsigned char *dst, int rx, int ry, int rw, int rh)
{
    const unsigned char *p_src_y = src + (ry * srcw + rx);
    unsigned char *p_dst= dst;
    // 拷贝Y分量
    for(int i = 0; i < rh; ++i)
    {
        memcpy(p_dst, p_src_y, rw);
        p_src_y += srcw;
        p_dst += rw;
    }
    
    int srcw_uv = srcw / 2;
    int srch_uv = srch / 2;

    int rx_uv = rx / 2;
    int ry_uv = ry / 2;
    int rw_uv = static_cast<int>(ceil(rw*1.0 / 2));
    int rh_uv = static_cast<int>(ceil(rh*1.0 / 2));
    // 拷贝U分量
    const unsigned char *p_src_u = src + (srcw * srch) + (ry_uv * srcw_uv + rx_uv);
    for(int i = 0; i < rh_uv; ++i)
    {
        memcpy(p_dst, p_src_u, rw_uv);
        p_src_u += srcw_uv;
        p_dst += rw_uv;
    }
    // 拷贝V分量
    const unsigned char *p_src_v = src + (srcw * srch) + (srcw_uv * srch_uv) + (ry_uv * srcw_uv + rx_uv);
    for(int i = 0; i < rh_uv; ++i)
    {
        memcpy(p_dst, p_src_v, rw_uv);
        p_src_v += srcw_uv;
        p_dst += rw_uv;
    }
}

void DrawPoint(unsigned char *src, int w, int h, int c, int dx, int dy, int size, int r, int g, int b)
{
    for (int d = -size; d < size; d++)
    {
        for (int f = -size; f < size; f++)
        {
            int x = (dx + d);
            int y = (dy + f);
            if (x > (w - 1) || x < 0)
            {
                x = x > (w - 1) ? w-1 : 0;
            }
            if (y > (h - 1) || y < 0)
            {
                y = y > (h - 1) ? h-1 : 0;
            }
            *(src + (y * w * c) + (x * c) + 0) = r;
            if (c == 3)
            {
                *(src + (y * w * c) + (x * c) + 1) = g;
                *(src + (y * w * c) + (x * c) + 2) = b;
            }
        }
    }
}

void DrawRect(unsigned char *src, int w, int h, int c, int x1, int y1, int x2, int y2, int size, int r, int g, int b)
{
    for (int y = y1; y < y2; y++)
    {
        DrawPoint(src, w, h, c, x1, y, size, r, g, b);
        DrawPoint(src, w, h, c, x2, y, size, r, g, b);
    }

    for (int x = x1; x < x2; x++)
    {
        DrawPoint(src, w, h, c, x, y1, size, r, g, b);
        DrawPoint(src, w, h, c, x, y2, size, r, g, b);
    }
}

unsigned char *GetRGBImage(const std::string &name, int &w, int &h, int &c)
{
    c = 3;
    unsigned char *img_data_ptr = stbi_load(name.c_str(), &w, &h, &c, 0);
    if (img_data_ptr == nullptr)
    {
        return img_data_ptr;
    }
    // int size_img = w * h;

    return img_data_ptr;   
}

unsigned char *GetBGRImage(const std::string &name, int &w, int &h, int &c)
{
    c = 3;
    unsigned char *img_data_ptr = stbi_load(name.c_str(), &w, &h, &c, 0);
    if (img_data_ptr == nullptr)
    {
        return img_data_ptr;
    }
    int size_img = w * h;
    // rgb to bgr
    if( c == 3)
    {
        for (int j = 0; j < size_img; j++)
        {
            unsigned char tmp = img_data_ptr[j * 3 + 0];
            img_data_ptr[j * 3 + 0] = img_data_ptr[j * 3 + 2];
            img_data_ptr[j * 3 + 2] = tmp;
        }        
    }
    return img_data_ptr;    
}

void DestroyBGRImage(unsigned char *ptr)
{
    if(ptr != NULL)
    {
        stbi_image_free(ptr);
    }
}

void DestroyRGBImage(unsigned char *ptr)
{
    if(ptr != NULL)
    {
        stbi_image_free(ptr);
    }
}

void SaveImagePNG(const std::string &name, unsigned char *img, int w, int h, int c)
{
    std::string outname = name + ".png";
    stbi_write_png(outname.c_str(), w, h, c, img, 0);
}

void DownSampleImageHalfScale(unsigned char *down_2_image, int down_wd, int down_ht, int prev_down_wd, int prev_down_ht, int down_move)
{
	const int down_move_2 = 2;//down_move*2;
	int i,j,y,n;
	int value;
	unsigned char *p1, *p2;
	int cur_down_ht = (prev_down_ht>>down_move);
	int cur_down_wd = (prev_down_wd>>down_move);
	//int delta_wd = down_wd - cur_down_wd;
	//int delta_ht = down_ht - cur_down_ht;
	const int mean_value = 128;

	n = 0;
	for(i = 0;i < cur_down_ht; ++i)
	{
#ifdef CNN_ARM_NEON_PROCESSING
		unsigned char *pImageRes;
		int search_8_end = cur_down_wd/8*8;
		y = i*2;
		p1 = down_2_image + y*prev_down_wd;
		p2 = p1 + prev_down_wd;
		pImageRes = down_2_image + i*down_wd;
		for(j = 0;j < search_8_end; j += 8)
		{
			uint8x16_t v1 = vld1q_u8(p1);
			uint8x16_t v2 = vld1q_u8(p2);
			uint16x8_t sum_res1, sum_res2;
			uint8x8_t res;

			sum_res1 = vpaddlq_u8(v1);
			sum_res2 = vpaddlq_u8(v2);
			sum_res1 = vaddq_u16(sum_res1, sum_res2);
			res = vshrn_n_u16(sum_res1, down_move_2);
			vst1_u8(pImageRes, res);
			//value = *p1 + *(p1 + 1) + *p2 + *(p2 + 1);
			//down_2_image[n] = (value>>down_move_2);

			p1 += 16;
			p2 += 16;
			pImageRes += 8;
		}
		for(;j < cur_down_wd; ++j)
		{
			value = *p1 + *(p1 + 1) + *p2 + *(p2 + 1);
			*pImageRes = (value>>down_move_2);
			p1 += 2;
			p2 += 2;
			pImageRes ++;
		}
		for(j = cur_down_wd;j < down_wd; ++j)
		{
			*pImageRes = mean_value;
			pImageRes ++;
		}
#else
		y = i*2;
		p1 = down_2_image + y*prev_down_wd;
		p2 = p1 + prev_down_wd;
		n = i*down_wd;
		for(j = 0;j < cur_down_wd; ++j)
		{
			value = *p1 + *(p1 + 1) + *p2 + *(p2 + 1);
			down_2_image[n] = (value>>down_move_2);
			p1 += 2;
			p2 += 2;
			n ++;
		}
		for(j = cur_down_wd;j < down_wd; ++j)
		{
			down_2_image[n] = mean_value;
			n ++;
		}
#endif

	}
	for(i = cur_down_ht;i < down_ht; ++i)
		memset(down_2_image + i*down_wd, mean_value, sizeof(char)*down_wd);
}

int getOrientation(const char *photoPath){
    FILE *fp = fopen(photoPath, "rb");
    if (!fp) {
        printf("Can't open file.\n");
        return -1;
    }
    fseek(fp, 0, SEEK_END);
    unsigned long fsize = ftell(fp);
    rewind(fp);
    unsigned char *buf = new unsigned char[fsize];
    if (fread(buf, 1, fsize, fp) != fsize) {
        printf("Can't read file.\n");
        delete[] buf;
        return -2;
    }
    fclose(fp);

    // Parse EXIF
    easyexif::EXIFInfo result;
    int code = result.parseFrom(buf, fsize);
    delete[] buf;
    if (code) {
        printf("Error parsing EXIF: code %d\n", code);
        return -3;
    }
    int ratio = -3;
    switch (result.Orientation){
        case 1:
            ratio = 0;
            break;
        case 6:
            ratio = 90;

            break;
        case 3:
            ratio = 180;

            break;
        case 8:
            ratio = 270;

            break;
    }
    return ratio;
}

}
}

