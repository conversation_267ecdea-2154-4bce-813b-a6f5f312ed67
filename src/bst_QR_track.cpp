#include "bst_QR_track.h"
#include "bstQRCode.h"
#include "bstCaffeProto.hpp"
#include "DES.h"
#include "BSTDes.h"
#include "../Util/TimeStatistic.h"
#include "BSTTool.h"
#include <float.h>
#include <cmath>
#ifdef __ANDROID__
#include "sys/system_properties.h"
#include <string.h>
#endif
#include "check.h"
#include "bst_err_code.h"
#if _MSC_VER>=1900
#include <stdio.h> 
_ACRTIMP_ALT FILE* __cdecl __acrt_iob_func(unsigned);
#ifdef __cplusplus 
extern "C"
#endif
FILE * __cdecl __iob_func(unsigned i) {
    return __acrt_iob_func(i);
}
#endif
#ifdef ANDROID
#include "lib_yuv/include/libyuv.h"
#endif
using namespace std;
using namespace cv;
#include "../Util/MemPool.h"

BstSingleQRTracker::BstSingleQRTracker(){

}

BstSingleQRTracker::~BstSingleQRTracker(){
#ifdef FASTCV
    if (preImgPyr){
        fcvPyramidDelete_v2(preImgPyr, 3, 1);
        delete[]preImgPyr;
    }
#endif
    if (m_PreOriImg) MemPool::Deallocate(m_PreOriImg);
    if (m_PreImg) MemPool::Deallocate(m_PreImg);
    if (m_PreFeatPts) MemPool::Deallocate(m_PreFeatPts);
    if (m_PredictFeatPts) MemPool::Deallocate(m_PredictFeatPts);
    m_PreOriImg = nullptr;
    m_PreImg = nullptr;
    m_PreFeatPts = nullptr;
    m_PredictFeatPts = nullptr;

}

int BstSingleQRTracker::setPreFeatPts(BSTRect &qr_rect){
    m_PreFeatPts[0] = (qr_rect.left+qr_rect.right)/2; // center point
    m_PreFeatPts[1] = (qr_rect.top+qr_rect.bottom)/2;
    m_PreFeatPts[2] = qr_rect.left; // left-top point
    m_PreFeatPts[3] = qr_rect.top;
    m_PreFeatPts[4] = qr_rect.right; // right-top point
    m_PreFeatPts[5] = qr_rect.top;
    m_PreFeatPts[6] = qr_rect.right; // right-bottom point
    m_PreFeatPts[7] = qr_rect.bottom;
    m_PreFeatPts[8] = qr_rect.left; // left-bottom point
    m_PreFeatPts[9] = qr_rect.bottom;

    return BST_QR_OK;
}

int BstSingleQRTracker::setPreFeatPts(std::vector<PointXY> &corner_points){
    if (corner_points.size() == 0){
        return BST_QR_NOT_FOUND;
    }
    float xmin = corner_points[0].x/4, ymin = corner_points[0].y/4, xmax = corner_points[0].x/4, ymax = corner_points[0].y/4;
    for (int i = 0; i < (int)corner_points.size(); i++){
        // 2-3 left-bottom, 4-5 left-top, 6-7 right-top
        m_PreFeatPts[2*(i+1)+0] = corner_points[i].x/4;
        m_PreFeatPts[2*(i+1)+1] = corner_points[i].y/4;

        if (i >= 0 && i <= 3){
            xmin = std::min(corner_points[i].x/4, xmin);
            ymin = std::min(corner_points[i].y/4, ymin);
            xmax = std::max(corner_points[i].x/4, xmax);
            ymax = std::max(corner_points[i].y/4, ymax);
        }
    }
    m_PreFeatPts[0] = (xmin + xmax) / 2;
    m_PreFeatPts[1] = (ymin + ymax) / 2;

    return BST_QR_OK;
}

int BstSingleQRTracker::createNewTrackingQR(CGrayImage &img, CGrayImage &ori_img, DecodeResult &decode_result, int maxIDNum){
    int w = img.GetWidth();
    int h = img.GetHeight();
    int c = 1;
    int ori_w = ori_img.GetWidth();
    int ori_h = ori_img.GetHeight();
    // t0 frame assign
    if (m_PreImg){
        MemPool::Deallocate(m_PreImg);
        m_PreImg = nullptr;
    }

    m_PreImg = (unsigned char *)MemPool::Allocate(w * h * c);
    memcpy(m_PreImg, img.GetImageData(), w * h * c);
    m_PreW = w;
    m_PreH = h;
    m_PreC = c;
    if (m_PreOriImg){
        MemPool::Deallocate(m_PreOriImg);
        m_PreOriImg = nullptr;
    }
    m_PreOriImg = (unsigned char*)MemPool::Allocate(ori_w * ori_h);
    memcpy(m_PreOriImg, ori_img.GetImageData(), ori_w * ori_h);
    m_PreOriW = ori_w;
    m_PreOriH = ori_h;
    m_PreOriC = 1;

    // Maximum external rectangular box
    float xmin = w - 1, ymin = h - 1, xmax = 0, ymax = 0;
    if(decode_result.corner_points.size()<4) return BST_QR_NOT_FOUND;
    for (int p = 0; p < 4; p++){
        xmin = std::min(decode_result.corner_points[p].x/4, xmin);
        ymin = std::min(decode_result.corner_points[p].y/4, ymin);
        xmax = std::max(decode_result.corner_points[p].x/4, xmax);
        ymax = std::max(decode_result.corner_points[p].y/4, ymax);
    }
    BSTRect qr_rect(xmin, ymin, xmax, ymax);

    // init MOSSE tracker
    m_FrameFixNum = 0;

    // init
    mIsNewTracker = true;
    // init module_size
    m_ModuleSize = decode_result.module_size / decode_result.decode_scale;
    m_PreGLobalMatchInfo.min_diff_score = 0;
    m_PreGLobalMatchInfo.slow_change_continue_num = 0;
    for (int i = 0; i < 9; i++){
        m_PreGLobalMatchInfo.block_min_diff_score.push_back(0.0);
    }

    // LK optical feature point
    if (m_PreFeatPts){
        MemPool::Deallocate(m_PreFeatPts);
        m_PreFeatPts = nullptr;
    }
    m_PreFeatPts = (float *)MemPool::Allocate(2*FEAT_LEN*sizeof(float));
    // t0 frame optical-feature-point assign
    if (setPreFeatPts(decode_result.corner_points) != BST_QR_OK){
        return BST_QR_NOT_FOUND;
    }

    // t1 frame optical-feature-point init
    if (m_PredictFeatPts){
        MemPool::Deallocate(m_PredictFeatPts);
        m_PredictFeatPts = nullptr;
    }
    m_PredictFeatPts = (float*)MemPool::Allocate(2 * FEAT_LEN * sizeof(float));
    for (int i = 0; i < 2*FEAT_LEN; i++){
        m_PredictFeatPts[i] = m_PreFeatPts[i];
    }

    // t0 frame track box assign
    m_PreInfo.ct_x = (qr_rect.left+qr_rect.right)/2;
    m_PreInfo.ct_y = (qr_rect.top+qr_rect.bottom)/2;
    m_PreInfo.rect = qr_rect;
    m_PreInfo.track_id = maxIDNum;
    m_PreInfo.decode_text = decode_result.decode_text;

    // t1 frame track box assign
    m_CurInfo = m_PreInfo;

    m_TrackID = maxIDNum;

    return BST_QR_OK;
}

int BstSingleQRTracker::track(CGrayImage& img, CGrayImage &ori_img, const params &mParams){
    int w = img.GetWidth();
    int h = img.GetHeight();
    int c = 1;

    if (img.GetImageData()==nullptr || c != 1) {
        return BST_QR_INVALID_ARGUMENT;
    }

    m_TrackingContinueNum += 1;

    int w_down4 = w;
    int h_down4 = h;
    unsigned char* preimg_down4 = m_PreImg;
    unsigned char* img_down4 = img.GetImageData();
    TimeUtils time_utils;
    time_utils.reset();
#ifdef FASTCV
    fcvPyramidLevel_v2 *imgPyr = new fcvPyramidLevel_v2[3];
    memset(imgPyr, 0, 3 * sizeof(fcvPyramidLevel_v2));
    if (m_ConfirmIsQR && mParams.opticalFlow){
        if (preImgPyr == NULL){
            preImgPyr = new fcvPyramidLevel_v2[3];
            fcvPyramidAllocate_v3(preImgPyr, w_down4, h_down4, w_down4 * 4, 4, 32, 3, FASTCV_PYRAMID_SCALE_HALF, 0);
            fcvPyramidCreateu8_v3(preimg_down4, w_down4, h_down4, w_down4, 3, FASTCV_PYRAMID_SCALE_HALF, preImgPyr);
        }

        fcvPyramidAllocate_v3(imgPyr, w_down4, h_down4, w_down4 * 4, 4, 32, 3, FASTCV_PYRAMID_SCALE_HALF, 0);
        fcvPyramidCreateu8_v3(img_down4, w_down4, h_down4, w_down4, 3, FASTCV_PYRAMID_SCALE_HALF, imgPyr);

        float featXY_in[2*FEAT_LEN] = {0};
        for (int i = 0; i < 2*FEAT_LEN; i++){
            featXY_in[i] = m_PreFeatPts[i];
        }
        float featXY_out[2*FEAT_LEN] = {0};

        int32_t status[FEAT_LEN] = {0};
        float featXY_estimate[2*FEAT_LEN];
        fcvTrackLKOpticalFlowu8_v3(preimg_down4, img_down4, w_down4, h_down4, 0, 
                                preImgPyr, imgPyr, featXY_in, featXY_estimate, featXY_out, 
                                status, FEAT_LEN, 29, 29, 3, FASTCV_TERM_CRITERIA_BOTH, 5, 0.03, 0);

        for (int i = 0; i < FEAT_LEN; i++){
            if (status[i] != 1){
                fcvPyramidDelete_v2(imgPyr, 3, 1);
                if (imgPyr){
                    delete[] imgPyr;
                    imgPyr = NULL;
                }
                return BST_QR_NOT_FOUND;
            }
        }

        bool updateTrackResult = true;
        
        {
            float cp_move_dist = std::sqrt(std::pow(featXY_in[0]-featXY_out[0], 2) + std::pow(featXY_in[1]-featXY_out[1], 2));
            int tmp_size = w > h ? h : w;
            float cp_move_ratio = cp_move_dist/tmp_size*100;
            LOGI("center point move ratio %f", cp_move_ratio);
            if (cp_move_ratio < 1){
                m_QRCenterMoveSlowContinueNum += 1;
                m_QRCenterMoveFastContinueNum = 0;
            }else{
                m_UpdateTrackCoordinateInfo = 0;
            }

            if (m_QRCenterMoveSlowContinueNum > 10){
                m_UpdateTrackCoordinateInfo = 1;
                m_QRCenterMoveSlowContinueNum = 0;
            }

            float pre_x1 = w-1, pre_y1 = h-1, pre_x3 = 0, pre_y3 = 0;
            for (int i = 0; i < 5; i++){
                pre_x1 = std::min(featXY_in[2*i], pre_x1);
                pre_y1 = std::min(featXY_in[2*i+1], pre_y1);
                pre_x3 = std::max(featXY_in[2*i], pre_x3);
                pre_y3 = std::max(featXY_in[2*i+1], pre_y3);
            }
            float pre_qr_w = pre_x3 - pre_x1;
            float pre_qr_h = pre_y3 - pre_y1;
            float cur_x1 = w-1, cur_y1 = h-1, cur_x3 = 0, cur_y3 = 0;
            for (int i = 0; i < 5; i++){
                cur_x1 = std::min(featXY_out[2*i], cur_x1);
                cur_y1 = std::min(featXY_out[2*i+1], cur_y1);
                cur_x3 = std::max(featXY_out[2*i], cur_x3);
                cur_y3 = std::max(featXY_out[2*i+1], cur_y3);
            }
            float cur_qr_w = cur_x3 - cur_x1;
            float cur_qr_h = cur_y3 - cur_y1;
            float cur_w_h_ratio = cur_qr_w*1.0/cur_qr_h;

            float w_change_ratio = std::abs((cur_qr_w - pre_qr_w) * 1.0 / pre_qr_w);
            float h_change_ratio = std::abs((cur_qr_h - pre_qr_h) * 1.0 / pre_qr_h);
            LOGI("w_change_ratio %f h_change_ratio %f \n", w_change_ratio, h_change_ratio);
            if (w_change_ratio > 0.2 || 
                h_change_ratio > 0.2 || 
                cur_w_h_ratio > 2.0 || 
                cur_w_h_ratio < 1/2.0){
                updateTrackResult = false;
            }
        }

        if (updateTrackResult){
            for (int i = 0; i < 2*FEAT_LEN; i++){
                m_PredictFeatPts[i] = featXY_out[i];
            }
        }

        fcvPyramidDelete_v2(preImgPyr, 3, 1);
        if (preImgPyr){
            delete[] preImgPyr;
            preImgPyr = NULL;
        }
    }
#else
    for (int i = 0; i < 2*FEAT_LEN; i++){
        m_PredictFeatPts[i] = m_PreFeatPts[i];
    }
#endif
    // t1 frame track box assign 
    m_CurInfo.track_id = m_PreInfo.track_id;
    float xmin = w-1, ymin = h-1, xmax = 0, ymax = 0;
    for (int i = 0; i < 5; i++){
        xmin = std::min(m_PredictFeatPts[2*i], xmin);
        ymin = std::min(m_PredictFeatPts[2*i+1], ymin);
        xmax = std::max(m_PredictFeatPts[2*i], xmax);
        ymax = std::max(m_PredictFeatPts[2*i+1], ymax);
    }
    LOGI("process time = %f ms (of) \n", time_utils.get_time());
    
    m_CurInfo.ct_x = m_PredictFeatPts[0];
    m_CurInfo.ct_y = m_PredictFeatPts[1];
    m_CurInfo.rect.left   = xmin;
    m_CurInfo.rect.top    = ymin;
    m_CurInfo.rect.right  = xmax;
    m_CurInfo.rect.bottom = ymax;
    m_CurInfo.decode_text = m_PreInfo.decode_text;

    // update MOSSE tracker
    this->m_OpenMosseTrack = true;

    this->m_OpenGlobalMatch = false;

    int pre_qr_w = m_PreInfo.rect.right - m_PreInfo.rect.left;
    int pre_qr_h = m_PreInfo.rect.bottom - m_PreInfo.rect.top;
    int cur_qr_w = m_CurInfo.rect.right - m_CurInfo.rect.left;
    int cur_qr_h = m_CurInfo.rect.bottom - m_CurInfo.rect.top;
    m_QRChangeInfo.w_change_ratio = std::abs((cur_qr_w - pre_qr_w) * 1.0 / pre_qr_w);
    m_QRChangeInfo.h_change_ratio = std::abs((cur_qr_h - pre_qr_h) * 1.0 / pre_qr_h);
    m_QRChangeInfo.ct_x_change_ratio = std::abs((m_CurInfo.ct_x - m_PreInfo.ct_x) / pre_qr_w);
    m_QRChangeInfo.ct_y_change_ratio = std::abs((m_CurInfo.ct_y - m_PreInfo.ct_y) / pre_qr_h);

    // whether is new tracker
    mIsNewTracker = false;

    // 1:1:3:1:1 verify 
    time_utils.reset();
    bool open_11311verify = true;

    // update t0=t1 frame
    memcpy(m_PreImg, img.GetImageData(), w* h* c);
    m_PreW = w;
    m_PreH = h;
    m_PreC = c;

    memcpy(m_PreOriImg, ori_img.GetImageData(), ori_img.GetWidth()* ori_img.GetHeight());
    m_PreOriW = ori_img.GetWidth();
    m_PreOriH = ori_img.GetHeight();
    m_PreOriC = 1;

    // update t0=t1 frame optical-feature-point
    for (int i = 0; i < 2*FEAT_LEN; i++){
        m_PreFeatPts[i] = m_PredictFeatPts[i];
    }
    // update t0=t1 frame track box
    m_PreInfo = m_CurInfo;

    if(m_ConfirmIsQR){
    #ifdef FASTCV
        preImgPyr = imgPyr;
    #endif
    }else{
#ifdef FASTCV
        if (imgPyr){
            if(imgPyr->ptr) 
                fcvPyramidDelete_v2(imgPyr, 3, 1);
            delete[] imgPyr;
            imgPyr = NULL;
        }
#endif
    }

    return BST_QR_OK;
}

bool BstMultiQRTracker::load_config(const char* config_path) {
    if(config_path== nullptr){
        LOGE("init error cfpPath is null\n");
        return false;
    }

    char* strContent = nullptr;
    int strLen = 0;
    char version[128];
    int isEncode = false;
    mErrorCode =  DecodeCfgFile(config_path, nullptr, &strContent, &strLen, version, nullptr, &isEncode);
    if (mErrorCode != 0 || strContent==nullptr)
    {
        LOGE("decode cfg error !! code = %d", mErrorCode);
        return false;
    }
    LOGE("CFG Version : %s", version);
    if (isEncode == false)
    {
        LOGE("decode version !!!");
        return false;
    }
    memset(dumpPath, 0, sizeof(dumpPath));
    int ret = VerifyCfgInfo(strContent, strLen, dumpPath);
    if (ret) 
    {
        mErrorCode = ret;
        LOGE("check error !!");
        return false;
    }
    // if (bGlobalLog == false)
    // {
    //     if (check_manu == 1 || check_product == 1 || check_model == 1) {
    //         bGlobalLog = false;
    //     }
    //     else {
    //         LOGE("enable_log by check is N");
    //         bGlobalLog = true;
    //     }
    // }
    bool bret = mQRFlow.LoadParameterFromString(strContent);
    if (bret == false)
    {
        GetInteError(BST_ALG_CFG_PARAM_MATCH_ERR);
        LOGE("%s", strContent);
        mErrorCode = -15;
        LOGE("LoadParameter error !!");
        return false;
    }
    memcpy(&mParams, &mQRFlow.mParams, sizeof(mParams));
    LOGI("config: qrDetectType %d\n",mParams.qrDetectType);
    LOGI("config: qrDetectTfliteInferType %d\n",mParams.qrDetectTfliteInferType);
    LOGI("config: qrDetectSize %d\n",mParams.qrDetectSize);
    LOGI("config: slowDetectInterval %d\n",mParams.slowDetectInterval);
    LOGI("config: stableDetectInterval %d\n",mParams.stableDetectInterval);
    LOGI("config: detectScoreThd %d\n",mParams.detectScoreThd);
    LOGI("config: maxQRNum %d\n",mParams.maxQRNum);

    LOGI("config: openExtraDetect %d\n",mParams.openExtraDetect);
    LOGI("config: extraDetectType %d\n",mParams.extraDetectType);
    LOGI("config: extraDetectInterval %d\n",mParams.extraDetectInterval);
    LOGI("config: detectBoxAppearFirst %d\n",mParams.detectBoxAppearFirst);
    LOGI("config: extraDetectSize %d\n",mParams.extraDetectSize);

    LOGI("config: motionDetect %d\n",mParams.motionDetect);
    LOGI("config: qrcodeDetect %d\n",mParams.qrcodeDetect);
    LOGI("config: qrcodeDecode %d\n",mParams.qrcodeDecode);
    LOGI("config: opticalFlow %d\n",mParams.opticalFlow);
    LOGI("config: mosseResponse %d\n",mParams.mosseResponse);
    LOGI("config: globalMatch %d\n",mParams.globalMatch);
    LOGI("config: morphChange %d\n",mParams.morphChange);
    LOGI("config: verify11311Pattern %d\n",mParams.verify11311Pattern);

    LOGI("config: openDecodeVerify %d\n",mParams.openDecodeVerify);
    LOGI("config: decodeVerifyInterval %d\n",mParams.decodeVerifyInterval);
    LOGI("config: trackAreaDetectVerify %d\n",mParams.trackAreaDetectVerify);
    LOGI("config: decodeVerifyContinueFailThd %d\n",mParams.decodeVerifyContinueFailThd);

    LOGI("config: openMultiFrame11311Verify %d\n",mParams.openMultiFrame11311Verify);
    LOGI("config: thdFailNum11311Verify %d\n",mParams.thdFailNum11311Verify);
    LOGI("config: thdSuccessNum11311Verify %d\n",mParams.thdSuccessNum11311Verify);
    LOGI("config: openMultiFrameGlobalMatch %d\n",mParams.openMultiFrameGlobalMatch);
    LOGI("config: thdFailNumGlobalMatch %d\n",mParams.thdFailNumGlobalMatch);
    LOGI("config: thdSuccessNumGlobalMatch %d\n",mParams.thdSuccessNumGlobalMatch);

    LOGI("config: extraDimensionGuess %d\n",mParams.extraDimensionGuess);
    LOGI("config: smallQRDownScale %d\n",mParams.smallQRDownScale);
    LOGI("config: maxFinderPattern %d\n",mParams.maxFinderPattern);
    LOGI("config: numBinarizer %d\n",mParams.numBinarizer);
    LOGI("config: numMorph %d\n",mParams.numMorph);
    LOGI("config: bUseAsync %d\n",mParams.bUseAsync);
    LOGI("config: bUseAsyncDet %d\n",mParams.bUseAsyncDet);
    LOGI("config: qrDetectThread %d\n",mParams.qrDetectThread);
    LOGI("config: qrDetectThread384 %d\n",mParams.qrDetectThread384);
    LOGI("config: qrDetectThread128 %d\n",mParams.qrDetectThread128);
    LOGI("config: bUseAsyncDecode %d\n",mParams.bUseAsyncDecode);
    LOGI("config: decodeTimerOut %d\n",mParams.decodeTimerOut);
    mErrorCode = 0;
    return true;
}
#define detect_v3_tf_384_len 8748390683224893256
#define detect_v3_128_tf_1229_len 15183702577146443951
#define detect_v3_64_len 4945226521838330541
#define sr1201_len 712633527040985393

BstMultiQRTracker::BstMultiQRTracker(const char* config_path) : mBusy(false){
    mErrorCode = 0;
    std::string model_dir = string(config_path);
    bool valid_ret = load_config(config_path);
    if (!valid_ret){
        mBuildObject = false;
        mErrorCode = -15;
        return;
    }
#if !defined(WIN32)
    int pos = model_dir.find_last_of('/');
#else
    int pos = model_dir.find_last_of('\\');
#endif
    std::string dir;
    if (pos != std::string::npos)
    {
        dir = model_dir.substr(0, pos);
    }
    else
    {
        pos = model_dir.find_last_of('/');
        if (pos != std::string::npos)
        {
            dir = model_dir.substr(0, pos);
        }
    }
    m_dir = dir;
    uint64_t file_detect_buffer_len,file_sr_buffer_len,file_detect_buffer_tf_len, file_detect_len_tf64_len;
    bool detRet = ModelDecode((dir + "/detect_model.bin").c_str(), &detect_buffer, &detect_len,&file_detect_buffer_len);
    if (detRet == false || detect_v3_tf_384_len != file_detect_buffer_len)
    {
        LOGE("error detect_model.bin,file id = %llu", file_detect_buffer_len);
        GetInteError(BST_ALG_MODEL_CHECK_ERR);
        mErrorCode = -15;
        return;
    }
    bool srRet = ModelDecode((dir + "/sr_model.bin").c_str(), &sr_buffer, &sr_len,&file_sr_buffer_len);
    if (srRet == false || sr1201_len!= file_sr_buffer_len)
    {
        LOGE("error sr_model.bin,file id = %llu", file_sr_buffer_len);
        GetInteError(BST_ALG_MODEL_CHECK_ERR);
        mErrorCode = -15;
        return;
    }
    string strDetProroTxt = string(detProtoBuffer) + string(detProtoBuffer1) + string(detProtoBuffer2);
    string strSrProtoTxt = string(srProtoBuffer);
    m_strSRProroTxt = strSrProtoTxt;
    m_strDetProroTxt = strDetProroTxt;
    memcpy(&mParams_scanner_, &mParams, sizeof(mParams));
    memcpy(&mParams_gpu_scanner64_, &mParams, sizeof(mParams));
    memcpy(&mParams_gpu_scanner_, &mParams, sizeof(mParams));
    memcpy(&mParams_sync_scanner_, &mParams, sizeof(mParams));
    mParams_scanner_.qrDetectThread = mParams_scanner_.qrDetectThread384;
    scanner_ = new qrcode_imp::BstAIQRCode(
        m_strDetProroTxt.c_str(),
        m_strDetProroTxt.size(),
        detect_buffer,
        detect_len,
        m_strSRProroTxt.c_str(),
        m_strSRProroTxt.size(),
        sr_buffer,
        sr_len,
        mParams_scanner_,
        m_dir.c_str());
    if (scanner_->m_ErrorCode != 0)
    {
        LOGE("scanner_ error !!");
        GetInteError(BST_ALG_CPU_MODEL_LOAD_ERR);
        mErrorCode = -15;
        return;
    }
    scanner_->setDetectInputSize(mParams_scanner_.qrDetectSize);
    mParams_sync_scanner_.bUseAsyncDecode = false;
    mParams_sync_scanner_.qrDetectThread = mParams_scanner_.qrDetectThread384;
    sync_scanner_ = new qrcode_imp::BstAIQRCode(
        m_strDetProroTxt.c_str(),
        m_strDetProroTxt.size(),
        detect_buffer,
        detect_len,
        m_strSRProroTxt.c_str(),
        m_strSRProroTxt.size(),
        sr_buffer,
        sr_len,
        mParams_sync_scanner_,
        m_dir.c_str());
    if (sync_scanner_->m_ErrorCode != 0)
    {
        LOGE("scanner_ error !!");
        GetInteError(BST_ALG_CPU_MODEL_LOAD_ERR);
        mErrorCode = -15;
        return;
    }
    sync_scanner_->setDetectInputSize(mParams_sync_scanner_.qrDetectSize);
    mParams_gpu_scanner_.qrDetectType = true;
    mParams_gpu_scanner_.bUseAsyncDecode = 0;
    mParams_gpu_scanner_.qrDetectThread = mParams_gpu_scanner_.qrDetectThread384;
    detRet = ModelDecode((dir + "/detect_model_tf.bin").c_str(), &detect_buffer_tf, &detect_len_tf,&file_detect_buffer_tf_len);
    if (detRet == false || detect_v3_128_tf_1229_len!= file_detect_buffer_tf_len)
    {
        LOGE("error decode detect_model_tf.bin,file id = %llu", file_detect_buffer_tf_len);
        GetInteError(BST_ALG_MODEL_CHECK_ERR);
        mErrorCode = -15;
        return;
    }
  //  strDetProroTxt = string(detProtoBuffer) + string(detProtoBuffer1) + string(detProtoBuffer2);
  //  strSrProtoTxt = string(srProtoBuffer);
    mParams_gpu_scanner_.qrDetectThread = mParams_gpu_scanner_.qrDetectThread128;
    gpu_scanner_ = new qrcode_imp::BstAIQRCode(
        m_strDetProroTxt.c_str(),
        m_strDetProroTxt.size(),
        detect_buffer_tf,
        detect_len_tf,
        m_strSRProroTxt.c_str(),
        m_strSRProroTxt.size(),
        sr_buffer,
        sr_len,
        mParams_gpu_scanner_,
        m_dir.c_str());
    if (gpu_scanner_->m_ErrorCode != 0)
    {
        LOGE("scanner_ error !!");
        GetInteError(BST_ALG_CPU_MODEL_LOAD_ERR);
        mErrorCode = -15;
        return;
    }
    gpu_scanner_->setDetectInputSize(mParams_gpu_scanner_.extraDetectSize);

    mParams_gpu_scanner64_.qrDetectType = true;
    mParams_gpu_scanner64_.bUseAsyncDecode = 0;
    detRet = ModelDecode((dir + "/detect_v3_model_tf_64.bin").c_str(), &detect_buffer_tf64, &detect_len_tf64,&file_detect_len_tf64_len);
    if (detRet == false || detect_v3_64_len != file_detect_len_tf64_len)
    {
        LOGE("error decode detect_v3_model_tf_64.bin,file id = %llu", file_detect_len_tf64_len);
        GetInteError(BST_ALG_MODEL_CHECK_ERR);
        mErrorCode = -15;
        return;
    }
    //strDetProroTxt = string(detProtoBuffer) + string(detProtoBuffer1) + string(detProtoBuffer2);
    //strSrProtoTxt = string(srProtoBuffer);
    mParams_gpu_scanner64_.qrDetectThread = mParams_gpu_scanner64_.qrDetectThread;
    gpu_scanner64_ = new qrcode_imp::BstAIQRCode(
        m_strDetProroTxt.c_str(),
        m_strDetProroTxt.size(),
        detect_buffer_tf64,
        detect_len_tf64,
        m_strSRProroTxt.c_str(),
        m_strSRProroTxt.size(),
        sr_buffer,
        sr_len,
        mParams_gpu_scanner64_,
        m_dir.c_str());
    if (gpu_scanner64_->m_ErrorCode != 0)
    {
        LOGE("scanner_ error !!");
        GetInteError(BST_ALG_CPU_MODEL_LOAD_ERR);
        mErrorCode = -15;
        return;
    }
    gpu_scanner64_->setDetectInputSize(64);

    LOGI("model_dir: %s\n", model_dir.c_str());

    m_MoveDetect.Init(25, 45, 10);
    mIgnoreframeNum = 0;
#ifdef __ANDROID__
    char prop[50];
    memset(prop, 0, sizeof(prop));
    int ret = 0;
    ret = __system_property_get("persist.camera.algo.bst.qrcode.dump", prop);
    if (ret != 0)
    {
        string tmp_prop = prop;
        if (tmp_prop == "true")
        {
            mDump = true;
        }
        else
        {
            mDump = false;
        }
    }
#endif
}

BstMultiQRTracker::~BstMultiQRTracker()
{
    TimeUtils time_utils;
    time_utils.reset();
    if (scanner_)
    {
        delete scanner_;
        scanner_ = NULL;
    }
    LOGE("uninit scanner %f ms", time_utils.get_time());
    time_utils.reset();
    if (gpu_scanner_)
    {
        delete gpu_scanner_;
        gpu_scanner_ = NULL;
    }
    LOGE("uninit gpu_scanner_ %f ms", time_utils.get_time());
    time_utils.reset();
    if (gpu_scanner64_)
    {
        delete gpu_scanner64_;
        gpu_scanner64_ = NULL;
    }
    if (sync_scanner_)
    {
        delete sync_scanner_;
        sync_scanner_ = NULL;
    }
    LOGE("uninit gpu_scanner64_ %f ms", time_utils.get_time());
    auto it = pTrackers_.begin();
    for (;it != pTrackers_.end();)
    {
        delete (*it);
        it = pTrackers_.erase(it);
    }
    if(mCurDownImgInfo.img) MemPool::Deallocate(mCurDownImgInfo.img);
    mCurDownImgInfo.img = nullptr;
    if (detect_buffer) delete[]detect_buffer;
    if (detect_buffer_tf) delete[]detect_buffer_tf;
    if (detect_buffer_tf64) delete[]detect_buffer_tf64;
    if (sr_buffer) delete[]sr_buffer;
    //if (sr_buffer_1) delete[]sr_buffer_1;
    //if (sr_buffer_2) delete[]sr_buffer_2;
    detect_buffer = nullptr;
    detect_buffer_tf = nullptr;
    detect_buffer_tf64 = nullptr;
    sr_buffer = nullptr;
    //sr_buffer_1 = nullptr;
    //sr_buffer_2 = nullptr;
}

float CalIOU(const BSTRect &rect1, const BSTRect &rect2){
    float iou = 0;
    float x1_tmp = std::max(rect1.left, rect2.left);
    float y1_tmp = std::max(rect1.top, rect2.top);
    float x2_tmp = std::min(rect1.right, rect2.right);
    float y2_tmp = std::min(rect1.bottom, rect2.bottom);

    if (x1_tmp >= x2_tmp || y1_tmp >= y2_tmp){
        iou = 0;
    }else{
        float area1 = (rect1.right - rect1.left) * (rect1.bottom - rect1.top);
        float area2 = (rect2.right - rect2.left) * (rect2.bottom - rect2.top);
        float area_inner = (x2_tmp - x1_tmp) * (y2_tmp - y1_tmp);
        iou = area_inner * 1.0 / (area1 + area2 - area_inner + 1e-6);
    }
    return iou;
}

int BstMultiQRTracker::track(unsigned char *img, int w, int h, int c, std::vector<SingleQRInfo> &QR_infos){
    LOGI("frame num: %d \n", frameNum_);
    frameNum_++;

    if (scanner_ == NULL || gpu_scanner_ == NULL || gpu_scanner64_ == NULL || sync_scanner_==NULL){
        return BST_QR_INVALID_SCANNER;
    }

    TimeUtils time_utils;
    if (mIsFirstFrame){
        mCurDownImgInfo.w = int(w/4);
        mCurDownImgInfo.h = int(h/4);
        mCurDownImgInfo.img = (unsigned char *)MemPool::Allocate(mCurDownImgInfo.w*mCurDownImgInfo.h);
        mIsFirstFrame = false;
    }
#ifdef ANDROID
    lib_bst_yuv::ScalePlane(img, w, w, h, mCurDownImgInfo.img, mCurDownImgInfo.w, mCurDownImgInfo.w, mCurDownImgInfo.h, lib_bst_yuv::kFilterBilinear);
#else
    BstCE::BSTTool::NCNNResizeBilinearC1(img, w, h, mCurDownImgInfo.img, mCurDownImgInfo.w, mCurDownImgInfo.h);
#endif
    CGrayImage cori_img,cdown_img;
    cori_img.ShadowCreate(img, w, h);
    cdown_img.ShadowCreate(mCurDownImgInfo.img, mCurDownImgInfo.w, mCurDownImgInfo.h);
    // motion detect
    int moveStatus = 0; // stable

    if (mParams.motionDetect)
    {
        time_utils.reset();
        LOGI("open motion_detect!\n");
        moveStatus = m_MoveDetect.Detect(mCurDownImgInfo.img, mCurDownImgInfo.w, mCurDownImgInfo.h);
        double motion_detect_time = time_utils.get_time();
        LOGI("process time = %f ms (md) \n", motion_detect_time);
    }

    if (moveStatus == MOVE_QUICK && mParams.openQuickMoveClearTracker)
    {
        LOGI("motion_detect: move quick\n");
        m_SlowDetectCnt = 0;
        m_StableDetectCnt = 0;
        detect_noqr_cumulate_cnt_ = 0;
        detect_flag_ = false;

        // clear tracker
        auto it = pTrackers_.begin();
        for (;it != pTrackers_.end();)
        {
            delete (*it);
            it = pTrackers_.erase(it);
        }
        return BST_QR_NOT_FOUND;
    }

    // qr track
    int cur_track_num = 0;
    auto it = pTrackers_.begin();
    for (; it != pTrackers_.end();)
    {
        if ((*it)->track(cdown_img, cori_img, mParams) == BST_QR_OK)
        {
            // MOSSE reponse judge
            if ((*it)->m_OpenMosseTrack && (*it)->m_ConfirmIsQR && mParams.mosseResponse)
            {
                if ((*it)->m_FrameFixNum > 5 && (*it)->m_CurResponseScore < 10)
                {
                    LOGI("tracker verify failed (mosse response) \n");
                    delete (*it);
                    it = pTrackers_.erase(it);
                    continue;
                }
                if ((*it)->m_CurResponseScore > 10)
                {
                    (*it)->m_FrameFixNum++;
                }
                else if ((*it)->m_CurResponseScore < 10)
                {
                    (*it)->m_FrameFixNum = 0;
                }
            }

            // global match
            if ((*it)->m_OpenGlobalMatch && (*it)->m_ConfirmIsQR)
            {
                if ((*it)->m_CurGlobalMatchInfo.slow_change_continue_num > 5 && (*it)->m_CurGlobalMatchInfo.min_diff_score > 15)
                {
                    delete (*it);
                    it = pTrackers_.erase(it);
                    continue;
                }
                if ((*it)->m_CurGlobalMatchInfo.min_diff_score < 5)
                {
                    (*it)->m_CurGlobalMatchInfo.slow_change_continue_num++;
                }
                else
                {
                    (*it)->m_CurGlobalMatchInfo.slow_change_continue_num = 0;
                }
            }

            // continue multiframe global match
            if (mParams.openMultiFrameGlobalMatch)
            {
                if ((*it)->m_GlobalMatchDurationFailNum > mParams.thdFailNumGlobalMatch)
                {
                    LOGI("tracker verify failed (multiframe global match)\n");
                    delete (*it);
                    it = pTrackers_.erase(it);
                    continue;
                }

                if ((*it)->m_GlobalMatchContinueSuccessNum > mParams.thdSuccessNumGlobalMatch)
                {
                    (*it)->m_GlobalMatchDurationFailNum = 0;
                }
            }

            // continue multiframe 11311verify
            if (mParams.openMultiFrame11311Verify)
            {
                if ((*it)->m_11311VerifyDurationFailNum > mParams.thdFailNum11311Verify)
                {
                    LOGI("tracker verify failed (multiframe 11311verify decode)\n");
                    delete (*it);
                    it = pTrackers_.erase(it);
                    continue;
                }

                if ((*it)->m_11311VerifyContinueSuccessNum > mParams.thdSuccessNum11311Verify)
                {
                    (*it)->m_11311VerifyDurationFailNum = 0;
                }
            }

            // decode verify (change frame)
            if (mParams.openDecodeVerify){
                params decode_verify_params = mParams;
                decode_verify_params.extraDimensionGuess = 0;
                decode_verify_params.smallQRDownScale = 0;
                decode_verify_params.maxFinderPattern = 5;
                decode_verify_params.numBinarizer = 3;
                decode_verify_params.numMorph = 3;
                AsyncParam async_param((*it)->m_TrackID);
                if (!mParams.bUseAsyncDecode || 
                    (mParams.bUseAsyncDecode && (*it)->m_TrackingContinueNum % mParams.decodeVerifyInterval == 0 && cur_track_num == 0)){
                    time_utils.reset();

                    auto tmp_point = CMatrix(4, 2);
                    tmp_point[0][0] = (*it)->m_CurInfo.rect.left * DOWN_SCALE; // left-top
                    tmp_point[0][1] = (*it)->m_CurInfo.rect.top * DOWN_SCALE;
                    tmp_point[2][0] = (*it)->m_CurInfo.rect.right * DOWN_SCALE; // right-bottom
                    tmp_point[2][1] = (*it)->m_CurInfo.rect.bottom * DOWN_SCALE;
                    DecodeResult decode_result;

                   // CMatrix ctmp_point(tmp_point.rows, tmp_point.cols, (float *)tmp_point.data);
                    if (mParams.bUseAsyncDecode){
                    #if defined(ANDROID)|| defined(WIN32)
                        decode_result = scanner_->decode_v2_async(&cori_img, tmp_point, async_param, decode_verify_params);
                      //  decode_result = scanner_->decode_v2_async(ori_img, tmp_point, async_param, decode_verify_params);
                    #endif
                    }else{

                        decode_result = scanner_->decode_v2(&cori_img, tmp_point, decode_verify_params);
                    }

                    if (mParams.bUseAsyncDecode){
                        if ((decode_result.qrcode_version < 1 || decode_result.qrcode_version > 40) && decode_result.decode_score < 0.5){
                            (*it)->m_DecodeVerifyContinueFailNum += 1;
                            (*it)->m_DecodeVerifyContinueSuccessNum = 0;
                        }
                        if ((decode_result.qrcode_version >= 1 && decode_result.qrcode_version <= 40) && decode_result.decode_score > 0.5){
                            (*it)->m_DecodeVerifyContinueSuccessNum += 1;
                            (*it)->m_DecodeVerifyContinueFailNum = 0;
                        }
                        LOGI("m_DecodeVerifyContinueFailNum %d m_DecodeVerifyContinueSuccessNum %d", (*it)->m_DecodeVerifyContinueFailNum, (*it)->m_DecodeVerifyContinueSuccessNum);
                        if ((*it)->m_DecodeVerifyContinueFailNum >= mParams.decodeVerifyContinueFailThd){
                            LOGI("tracker verify failed (decode failed/not qrcode) \n");
                            delete (*it);
                            it = pTrackers_.erase(it);
                            continue;
                        }
                    }else{
                        if ((decode_result.qrcode_version < 1 || decode_result.qrcode_version > 40) && decode_result.decode_score < 0.5){
                            LOGI("tracker verify failed (decode failed/not qrcode) \n");
                            delete (*it);
                            it = pTrackers_.erase(it);
                            continue;
                        }

                        if (decode_result.decode_text.size() == 0 || decode_result.corner_points.size() == 0){
                            LOGI("tracker verify failed (decode failed/maybe is qrcode) \n");
                            continue;
                        }
                    }

                    // update tracker param
                    float xmin = w - 1, ymin = h - 1, xmax = 0, ymax = 0;
                    for (int p = 0; p < 4 && decode_result.corner_points.size() >= 4; p++){
                        xmin = std::min(decode_result.corner_points[p].x/DOWN_SCALE, xmin);
                        ymin = std::min(decode_result.corner_points[p].y/DOWN_SCALE, ymin);
                        xmax = std::max(decode_result.corner_points[p].x/DOWN_SCALE, xmax);
                        ymax = std::max(decode_result.corner_points[p].y/DOWN_SCALE, ymax);
                    }
                    BSTRect qr_rect(xmin, ymin, xmax, ymax);
                    // t0 frame optical-feature-point assign
                    float decode_and_track_iou = CalIOU(qr_rect, (*it)->m_CurInfo.rect);
                    LOGI("decode verify and track iou %f ", decode_and_track_iou);
                    bool use_track_expand_roi_det = true;
                    LOGI("track_id %d m_TrackID %d", decode_result.track_id, (*it)->m_TrackID);
                    if (mParams.bUseAsyncDecode){
                        if (decode_result.track_id == (*it)->m_TrackID){
                            float tmp_xmin, tmp_ymin, tmp_xmax, tmp_ymax;
                            if (use_track_expand_roi_det){
                                tmp_xmin = (*it)->m_CurInfo.rect.left;
                                tmp_ymin = (*it)->m_CurInfo.rect.top;
                                tmp_xmax = (*it)->m_CurInfo.rect.right;
                                tmp_ymax = (*it)->m_CurInfo.rect.bottom;
                            }else{
                                tmp_xmin = xmin;
                                tmp_ymin = ymin;
                                tmp_xmax = xmax;
                                tmp_ymax = ymax;
                            }

                            float detect_and_decode_iou = -1.0;
                            float detect_and_track_iou = -1.0;

                            float expand_ratio = 1.2;
                            float cur_roi_cx = (tmp_xmin + tmp_xmax) / 2.0;
                            float cur_roi_cy = (tmp_ymin + tmp_ymax) / 2.0;
                            float cur_roi_w = tmp_xmax - tmp_xmin;
                            float cur_roi_h = tmp_ymax - tmp_ymin;
                            float expand_cur_roi_left   = std::max((cur_roi_cx - cur_roi_w*expand_ratio/2.0)*DOWN_SCALE, 0.0);
                            float expand_cur_roi_top    = std::max((cur_roi_cy - cur_roi_h*expand_ratio/2.0)*DOWN_SCALE, 0.0);
                            float expand_cur_roi_right  = std::min((cur_roi_cx + cur_roi_w*expand_ratio/2.0)*DOWN_SCALE, w-1.0);
                            float expand_cur_roi_bottom = std::min((cur_roi_cy + cur_roi_h*expand_ratio/2.0)*DOWN_SCALE, h-1.0);
                            float expand_cur_roi_w = expand_cur_roi_right - expand_cur_roi_left;
                            float expand_cur_roi_h = expand_cur_roi_bottom - expand_cur_roi_top;
                            CGrayImage cexpand_cur_roi;
                            cexpand_cur_roi.Create(expand_cur_roi_w, expand_cur_roi_h);
                            LOGI("Crop ROI ..");
                            BstCE::BSTTool::CropImageC1(cori_img.GetImageData(), w, h, cexpand_cur_roi.GetImageData(), (int)expand_cur_roi_left, (int)expand_cur_roi_top, expand_cur_roi_w, expand_cur_roi_h);
                            vector<CMatrix> cverify_pts;
                            cverify_pts = gpu_scanner64_->detect(cexpand_cur_roi);
                            
                            LOGI("decode verify qrcode_version %d decode_score %f coordinate size %d detect size %d \n", decode_result.qrcode_version, decode_result.decode_score, int(decode_result.corner_points.size()), (int)cverify_pts.size());

                            if (cverify_pts.size() > 0){
                                float tmp_max_iou = -1.0;
                                int tmp_max_idx = 0;
                                if (use_track_expand_roi_det){
                                    for (int i = 0; i < (int)cverify_pts.size(); i++){
                                        BSTRect det_rect((cverify_pts[i][0][0] + expand_cur_roi_left) / DOWN_SCALE, (cverify_pts[i][0][1] + expand_cur_roi_top) / DOWN_SCALE, (cverify_pts[i][2][0] + expand_cur_roi_left) / DOWN_SCALE, (cverify_pts[i][2][1] + expand_cur_roi_top) / DOWN_SCALE);
                                        BSTRect trk_rect(tmp_xmin, tmp_ymin, tmp_xmax, tmp_ymax);
                                        float tmp_iou = CalIOU(det_rect, trk_rect);
                                        if (tmp_iou > tmp_max_iou){
                                            tmp_max_iou = tmp_iou;
                                            tmp_max_idx = i;
                                        }
                                    }
                                    detect_and_track_iou = tmp_max_iou;
                                    BSTRect det_rect(cverify_pts[tmp_max_idx][0][0] + expand_cur_roi_left, cverify_pts[tmp_max_idx][0][1] + expand_cur_roi_top, cverify_pts[tmp_max_idx][2][0] + expand_cur_roi_left, cverify_pts[tmp_max_idx][2][1] + expand_cur_roi_top);
                                    BSTRect dec_rect(xmin*DOWN_SCALE, ymin*DOWN_SCALE, xmax*DOWN_SCALE, ymax*DOWN_SCALE);
                                    detect_and_decode_iou = CalIOU(det_rect, dec_rect);
                                }else{
                                    for (int i = 0; i < (int)cverify_pts.size(); i++){
                                        BSTRect det_rect((cverify_pts[i][0][0] + expand_cur_roi_left) / DOWN_SCALE, (cverify_pts[i][0][1] + expand_cur_roi_top) / DOWN_SCALE, (cverify_pts[i][2][0] + expand_cur_roi_left) / DOWN_SCALE, (cverify_pts[i][2][1] + expand_cur_roi_top) / DOWN_SCALE);
                                        BSTRect dec_rect(tmp_xmin, tmp_ymin, tmp_xmax, tmp_ymax);
                                        float tmp_iou = CalIOU(det_rect, dec_rect);
                                        if (tmp_iou > tmp_max_iou){
                                            tmp_max_iou = tmp_iou;
                                            tmp_max_idx = i;
                                        }
                                    }
                                    detect_and_decode_iou = tmp_max_iou;
                                    BSTRect det_rect(cverify_pts[tmp_max_idx][0][0]+expand_cur_roi_left, cverify_pts[tmp_max_idx][0][1] +expand_cur_roi_top, cverify_pts[tmp_max_idx][2][0]+expand_cur_roi_left, cverify_pts[tmp_max_idx][2][1]+expand_cur_roi_top);
                                    BSTRect trk_rect((*it)->m_CurInfo.rect.left*DOWN_SCALE, (*it)->m_CurInfo.rect.top*DOWN_SCALE, (*it)->m_CurInfo.rect.right*DOWN_SCALE, (*it)->m_CurInfo.rect.bottom*DOWN_SCALE);
                                    detect_and_track_iou = CalIOU(det_rect, trk_rect);
                                }
                                
                                LOGI("detect size %d, %d detect and decode/track max iou %.2f/%.2f ", (int)cverify_pts.size(), tmp_max_idx, detect_and_decode_iou, detect_and_track_iou);
                            }

                            if (mParams.trackAreaDetectVerify){
                                if (detect_and_track_iou < 0.05){
                                    LOGI("tracker verify failed (small detect failed/no qrcode) \n");
                                    delete (*it);
                                    it = pTrackers_.erase(it);
                                    continue;
                                }
                            }

                            if ((detect_and_decode_iou > 0.75 && detect_and_track_iou < 0.50) ||
                                (detect_and_decode_iou > 0.80 && detect_and_track_iou < 0.55) ||
                                (detect_and_decode_iou > 0.85 && detect_and_track_iou < 0.60) ||
                                (detect_and_decode_iou > 0.90 && detect_and_track_iou < 0.75)){
                                if ((*it)->setPreFeatPts(decode_result.corner_points) != BST_QR_OK){
                                    delete (*it);
                                    it = pTrackers_.erase(it);
                                    continue;
                                }
                                // t1 frame optical-feature-point init
                                for (int i = 0; i < 2*FEAT_LEN; i++){
                                    (*it)->m_PredictFeatPts[i] = (*it)->m_PreFeatPts[i];
                                }
                                // t0 frame track box assign
                                (*it)->m_PreInfo.ct_x = (qr_rect.left+qr_rect.right)/2;
                                (*it)->m_PreInfo.ct_y = (qr_rect.top+qr_rect.bottom)/2;
                                (*it)->m_PreInfo.rect = qr_rect;
                            }
                        }
                    }else{
                        if ((*it)->setPreFeatPts(decode_result.corner_points) != BST_QR_OK){
                            delete (*it);
                            it = pTrackers_.erase(it);
                            continue;
                        }
                        // t1 frame optical-feature-point init
                        for (int i = 0; i < 2*FEAT_LEN; i++){
                            (*it)->m_PredictFeatPts[i] = (*it)->m_PreFeatPts[i];
                        }
                        // t0 frame track box assign
                        (*it)->m_PreInfo.ct_x = (qr_rect.left+qr_rect.right)/2;
                        (*it)->m_PreInfo.ct_y = (qr_rect.top+qr_rect.bottom)/2;
                        (*it)->m_PreInfo.rect = qr_rect;
                    }
                    
                    if (!mParams.bUseAsyncDecode || 
                        (mParams.bUseAsyncDecode && decode_result.track_id == (*it)->m_TrackID && decode_result.decode_text != "")){
                        (*it)->m_PreInfo.decode_text = decode_result.decode_text;
                    }

                    // t1 frame track box assign
                    (*it)->m_CurInfo = (*it)->m_PreInfo;

                    LOGI("%d process time = %f ms (dv)", cur_track_num, time_utils.get_time());
                }
            }

            it++;
            cur_track_num++;
        }else{
            LOGI("track failed, release this tracker! m_ConfirmIsQR = %d \n", (*it)->m_ConfirmIsQR);
            delete (*it);
            it = pTrackers_.erase(it);
        }
    }

    string det_type = "det_slow";
    scanner_->setFrameNumber(frameNum_);
    gpu_scanner_->setFrameNumber(frameNum_);
    vector<CMatrix> tmp_pts;
    // qr detect
    if (mParams.qrcodeDetect)
    {
        if (moveStatus == MOVE_SLOW || moveStatus == MOVE_QUICK)
        {
            LOGI("motion_detect: m_SlowDetectCnt %d, move slow\n", m_SlowDetectCnt);
            if (m_SlowDetectCnt % mParams.slowDetectInterval == 0)
            {
                tmp_pts = scanner_->detect(cori_img);
                det_type = "det_slow";
                detect_flag_ = true;
                if (tmp_pts.size() == 0)
                {
                    detect_noqr_cumulate_cnt_++; // detect no qr of continue number
                }
                else
                {
                    detect_noqr_cumulate_cnt_ = 0;
                }
            }
            else
            {
                detect_flag_ = false;
            }
            m_StableDetectCnt = 1;
            m_SlowDetectCnt++;
        }
        else if (moveStatus == MOVE_STABLE)
        {
            LOGI("motion_detect: m_StableDetectCnt %d, move stable\n", m_StableDetectCnt);
            if (m_StableDetectCnt % mParams.stableDetectInterval == 0){
                time_utils.reset();
                tmp_pts = scanner_->detect(cori_img);
                LOGI("process time = %f ms (qrd 20) \n", time_utils.get_time());
                det_type = "det_slow";
                detect_flag_ = true;
                if (tmp_pts.size() == 0){
                    detect_noqr_cumulate_cnt_++;
                }else{
                    detect_noqr_cumulate_cnt_ = 0;
                }
            }else{
                detect_flag_ = false;
            }

            if (pTrackers_.size() == 0){
                if (m_StableDetectCnt % mParams.extraDetectInterval == 0 && !detect_flag_ && mParams.openExtraDetect){
                    if (mParams.extraDetectType == 0){
                        tmp_pts = scanner_->detect(cori_img, 400);
                    }else if (mParams.extraDetectType == 1){
                        time_utils.reset();
                        tmp_pts = gpu_scanner_->detect(cori_img);
                        LOGI("process time = %f ms (qrd 3) \n", time_utils.get_time());
                    }
                    
                    if (mParams.detectBoxAppearFirst)
                    {
                        det_type = "det_fast";
                    }
                    else
                    {
                        det_type = "det_slow";
                    }
                }
            }
            m_SlowDetectCnt = 1;
            m_StableDetectCnt++;
        }
    }
    
    LOGI("detect num = %d (tracker num = %d)\n", (int)tmp_pts.size(), int(pTrackers_.size()));

    // match detect and track box
    int detect_num = tmp_pts.size();
    if (detect_num > 0){
        // detect and track rect match, create tracker for new detect
        std::vector<bool> match_flags(detect_num, false);
        for (int i = 0; i < detect_num; i++){
            auto pt1 = tmp_pts[i][0];// tmp_pts[i].at<Point2f>(0, 0);
            auto pt2 = tmp_pts[i][2];// tmp_pts[i].at<Point2f>(2, 0);
            BSTRect det_rect(pt1[0], pt1[1], pt2[0], pt2[1]);
            for (int j = 0; j < (int)pTrackers_.size(); j++){
                BSTRect track_rect(pTrackers_[j]->m_CurInfo.rect.left*4, pTrackers_[j]->m_CurInfo.rect.top*4, pTrackers_[j]->m_CurInfo.rect.right*4, pTrackers_[j]->m_CurInfo.rect.bottom*4);
                float iou = CalIOU(det_rect, track_rect);
                // iou > 0.3: the same rect
                if (iou > MATCH_IOU_THD){
                    match_flags[i] = true;
                    
                    break;
                }
            }
            if (!match_flags[i])
            {
                if (pTrackers_.size() < mParams.maxQRNum && mParams.qrcodeDecode)
                {
                    if (det_type == "det_slow")
                    {
                        time_utils.reset();
                        if (pTrackers_.size() > 0){
                            DecodeResult decode_result = sync_scanner_->decode_v2(&cori_img, tmp_pts[i], mParams_sync_scanner_);
                            decode_result.detect_score = tmp_pts[i][4][0];
                            if (decode_result.decode_text.size() == 0){
                                LOGI("create tracker failed! (decode failed)detect score = %f \n", decode_result.detect_score);
                                continue;
                            }
                            decode_result.confirm_qr_is_decoded = true;
                            BstSingleQRTracker* tracker = new BstSingleQRTracker();
                            tracker->m_ConfirmIsQR = true;
                           // CGrayImage cdown_img, cori_img;
                           // cdown_img.ShadowCreate(down_img.data, down_img.cols, down_img.rows);
                           // cori_img.ShadowCreate(down_img.data, down_img.cols, down_img.rows);
                            if (tracker->createNewTrackingQR(cdown_img, cori_img, decode_result, maxIDNum_) == BST_QR_OK){
                                LOGI("create tracker success (qr decode) \n");
                                pTrackers_.push_back(tracker);
                                maxIDNum_++;
                            }else{
                                delete tracker;
                            }
                        }else if (pTrackers_.size() == 0 && tmp_pts[i][4][0] > mParams.detectScoreThd / 100.0) {
                            params decode_verify_params = mParams_sync_scanner_;// mParams;
                            decode_verify_params.extraDimensionGuess = 0;
                            decode_verify_params.smallQRDownScale = 0;
                            decode_verify_params.maxFinderPattern = 5;
                            decode_verify_params.numBinarizer = 3;
                            decode_verify_params.numMorph = 3;
                            decode_verify_params.bUseAsyncDecode = 0;//zsj

                            DecodeResult decode_result = sync_scanner_->decode_v2(&cori_img, tmp_pts[i], decode_verify_params);
                            decode_result.detect_score = tmp_pts[i][4][0];
                            if (decode_result.decode_text.size() == 0){
                                LOGI("create tracker failed! (decode failed)detect score = %f \n", decode_result.detect_score);
                                LOGI("%d process time = %f ms (df)", i, time_utils.get_time());
                                continue;
                            }
                            decode_result.confirm_qr_is_decoded = true;
                            BstSingleQRTracker* tracker = new BstSingleQRTracker();
                            tracker->m_ConfirmIsQR = true;
                            if (tracker->createNewTrackingQR(cdown_img, cori_img, decode_result, maxIDNum_) == BST_QR_OK){
                                LOGI("create tracker success (qr decode) \n");
                                pTrackers_.push_back(tracker);
                                maxIDNum_++;

                                LOGI("%d process time = %f ms (ds)", i, time_utils.get_time());
                            }else{
                                delete tracker;
                            }
                        }
                        LOGI("%d process time = %f ms (nd, det score = %f)", i, time_utils.get_time(), tmp_pts[i][4][0]);
                    }
                }
            }
        }

        // remove invalid track rect
        std::vector<bool> valid_flags(pTrackers_.size(), false);
        for (int i = 0; i < (int)pTrackers_.size(); i++){
            // detect box verify
            if (pTrackers_[i]->mIsNewTracker){
                valid_flags[i] = true;
                continue;
            }
            BSTRect track_rect(pTrackers_[i]->m_CurInfo.rect.left*4, pTrackers_[i]->m_CurInfo.rect.top*4, pTrackers_[i]->m_CurInfo.rect.right*4, pTrackers_[i]->m_CurInfo.rect.bottom*4);
            for (int j = 0; j < detect_num; j++){
                auto pt1 = tmp_pts[j][0];// tmp_pts[j].at<Point2f>(0, 0); //(4,1)
                auto pt2 = tmp_pts[j][2];// tmp_pts[j].at<Point2f>(2, 0);
                BSTRect det_rect(pt1[0], pt1[1], pt2[0], pt2[1]);
                float iou = CalIOU(track_rect, det_rect);
                if (iou > MATCH_IOU_THD){
                    valid_flags[i] = true;
                    break;
                }
            }
        }
        for (int i = valid_flags.size() - 1; i >= 0; i--){
            if (!valid_flags[i]){
                delete pTrackers_[i];
                pTrackers_.erase(pTrackers_.begin() + i);
            }
        }
    }

    // track box verify
    if (detect_flag_ && detect_noqr_cumulate_cnt_ >= 2){
        auto it = pTrackers_.begin();
        for (;it != pTrackers_.end();){
            LOGE("delete tracker");
            delete (*it);
            it = pTrackers_.erase(it);
        }
        return BST_QR_NOT_FOUND;
    }

    // trackers nms，Avoid duplicate tracking
    if (pTrackers_.size() > 1){
        std::vector<bool> flags(pTrackers_.size(), false);
        for (int i = 0; i < (int)pTrackers_.size()-1; i++){
            if (!flags[i]){
                for (int j = i+1; j < (int)pTrackers_.size(); j++){
                    if (!flags[j]){
                        BSTRect rect1(pTrackers_[i]->m_CurInfo.rect.left*4, pTrackers_[i]->m_CurInfo.rect.top*4, pTrackers_[i]->m_CurInfo.rect.right*4, pTrackers_[i]->m_CurInfo.rect.bottom*4);
                        BSTRect rect2(pTrackers_[j]->m_CurInfo.rect.left*4, pTrackers_[j]->m_CurInfo.rect.top*4, pTrackers_[j]->m_CurInfo.rect.right*4, pTrackers_[j]->m_CurInfo.rect.bottom*4);
                        float iou = CalIOU(rect1, rect2);
                        if (iou > NMS_IOU_THD){
                            flags[j] = true;
                        }
                    }
                }
            }
        }

        for (int i = flags.size() - 1; i >= 0; i--){
            if (flags[i]){
                delete pTrackers_[i];
                pTrackers_.erase(pTrackers_.begin() + i);
            }
        }
    }
    // collect trackers result
    for (int i = 0; i < (int)pTrackers_.size(); i++){
        pTrackers_[i]->m_CurInfo.rect.left   *= DOWN_SCALE;
        pTrackers_[i]->m_CurInfo.rect.top    *= DOWN_SCALE;
        pTrackers_[i]->m_CurInfo.rect.right  *= DOWN_SCALE;
        pTrackers_[i]->m_CurInfo.rect.bottom *= DOWN_SCALE;
        pTrackers_[i]->m_CurInfo.ct_x        *= DOWN_SCALE;
        pTrackers_[i]->m_CurInfo.ct_y        *= DOWN_SCALE;
        SingleQRInfo tmp_qr_info = pTrackers_[i]->m_CurInfo;
        QR_infos.push_back(tmp_qr_info);
    }

    if (QR_infos.size() == 0){
        return BST_QR_NOT_FOUND;
    }else{
        return BST_QR_OK;
    }
}