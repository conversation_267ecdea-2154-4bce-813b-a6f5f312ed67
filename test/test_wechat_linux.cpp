#include <iostream>
#include <chrono>
#include <iomanip>
#include <sys/stat.h>

// #include "bstQRCode.h"
#include "utils.h"
#include "bst_QR_scanner.h"
#include "opencv2/opencv.hpp"

using namespace std;
using namespace cv;
using namespace BstCE;

// Helper function to check if file exists
bool file_exists(const string& filename) {
    struct stat buffer;
    return (stat(filename.c_str(), &buffer) == 0);
}

// Helper function to check if directory exists
bool directory_exists(const string& dirname) {
    struct stat buffer;
    return (stat(dirname.c_str(), &buffer) == 0 && S_ISDIR(buffer.st_mode));
}

// Helper function to get current timestamp
string get_timestamp() {
    auto now = chrono::system_clock::now();
    auto time_t = chrono::system_clock::to_time_t(now);
    stringstream ss;
    ss << put_time(localtime(&time_t), "%Y-%m-%d %H:%M:%S");
    return ss.str();
}

unsigned char* readbin_GRAY(char const* imgpath, int width, int height)
{
    if (!file_exists(imgpath)) {
        cout << "[ERROR] Gray image file not found: " << imgpath << endl;
        return NULL;
    }

    unsigned char* srcImg = (unsigned char *)malloc(width * height);
    if (!srcImg) {
        cout << "[ERROR] Failed to allocate memory for image" << endl;
        return NULL;
    }

    FILE *fp = fopen(imgpath, "rb");
    if (NULL == fp) {
        cout << "[ERROR] Failed to open gray image file: " << imgpath << endl;
        free(srcImg);
        return NULL;
    }

    size_t bytes_read = fread(srcImg, 1, width * height, fp);
    fclose(fp);

    if (bytes_read != (size_t)(width * height)) {
        cout << "[WARNING] Expected " << (width * height) << " bytes, but read " << bytes_read << " bytes" << endl;
    }

    return srcImg;
}

void folder_demo_scanner(string img_dir, int width, int height, string img_type){
    cout << "[INFO] " << get_timestamp() << " - Starting folder demo scanner" << endl;
    cout << "[INFO] Image directory: " << img_dir << endl;
    cout << "[INFO] Image type: " << img_type << endl;

    // Check if directory exists
    if (!directory_exists(img_dir)) {
        cout << "[ERROR] Directory does not exist: " << img_dir << endl;
        return;
    }

    vector<string> filenames;
    FileUtils::GetFileNames(img_dir, filenames);

    if (filenames.empty()) {
        cout << "[ERROR] No files found in directory: " << img_dir << endl;
        return;
    }

    cout << "[INFO] Found " << filenames.size() << " files" << endl;

    // Check for config file
    string config_paths[] = {
        "../Data/config/qr.cfg",
        "../config/config.cfg",
        "./config/config.cfg",
        "config.cfg"
    };

    string config_file;
    bool config_found = false;
    for (const string& path : config_paths) {
        if (file_exists(path)) {
            config_file = path;
            config_found = true;
            break;
        }
    }

    if (!config_found) {
        cout << "[WARNING] Config file not found. Trying default path: ../Data/config/qr.cfg" << endl;
        config_file = "../Data/config/qr.cfg";
    } else {
        cout << "[INFO] Using config file: " << config_file << endl;
    }

    BST_QR_SCANNER* bst_QR_scanner = nullptr;
    try {
        bst_QR_scanner = new BST_QR_SCANNER(config_file.c_str());
        cout << "[INFO] QR scanner initialized successfully" << endl;
    } catch (const exception& e) {
        cout << "[ERROR] Failed to initialize QR scanner: " << e.what() << endl;
        return;
    } catch (...) {
        cout << "[ERROR] Failed to initialize QR scanner: Unknown error" << endl;
        return;
    }

    int success_num = 0;
    int processed_num = 0;
    auto start_time = chrono::high_resolution_clock::now();

    for (int n = 0; n < (int)filenames.size(); n++){
        cout << "[PROGRESS] " << (n+1) << "/" << filenames.size() << " Processing: " << filenames[n] << endl;

        bst_QR_img_t QRImg;
        QRImg.pimage = nullptr;
        Mat img;
        bool image_loaded = false;

        if (img_type == "jpg"){
            img = imread(filenames[n]);
            if (img.empty()) {
                cout << "[ERROR] Failed to load image: " << filenames[n] << endl;
                continue;
            }

            if (img.channels() == 3) {
                cvtColor(img, img, COLOR_BGR2GRAY);
            }

            QRImg.pimage = (unsigned char*)malloc(img.cols*img.rows);
            if (!QRImg.pimage) {
                cout << "[ERROR] Failed to allocate memory for image: " << filenames[n] << endl;
                continue;
            }

            memcpy(QRImg.pimage, img.data, img.cols*img.rows);
            QRImg.width = img.cols;
            QRImg.height = img.rows;
            QRImg.stride = img.cols;
            image_loaded = true;

        } else if (img_type == "gray"){
            QRImg.pimage = readbin_GRAY(filenames[n].c_str(), width, height);
            if (!QRImg.pimage) {
                cout << "[ERROR] Failed to load gray image: " << filenames[n] << endl;
                continue;
            }

            QRImg.width = width;
            QRImg.height = height;
            QRImg.stride = width;
            Mat gray(height, width, CV_8UC1, QRImg.pimage);
            img = gray.clone();
            image_loaded = true;
        } else {
            cout << "[ERROR] Unsupported image type: " << img_type << endl;
            continue;
        }

        if (!image_loaded) {
            continue;
        }

        bst_QR_input_t QRInput;
        QRInput.image = QRImg;

        std::vector<bst_QR_output_t> vQROutput;
        auto scan_start = chrono::high_resolution_clock::now();
        int retVal = bst_QR_scanner->scan(&QRInput, vQROutput);
        auto scan_end = chrono::high_resolution_clock::now();

        auto scan_duration = chrono::duration_cast<chrono::milliseconds>(scan_end - scan_start);

        processed_num++;

        if (vQROutput.size() > 0){
            success_num++;
            cout << "[SUCCESS] Found " << vQROutput.size() << " QR code(s) in " << scan_duration.count() << "ms:" << endl;
            for (size_t i = 0; i < vQROutput.size(); i++) {
                cout << "  QR " << (i+1) << ": \"" << vQROutput[i].decode_result << "\"" << endl;
                cout << "    Position: (" << vQROutput[i].xmin << "," << vQROutput[i].ymin
                     << ") to (" << vQROutput[i].xmax << "," << vQROutput[i].ymax << ")" << endl;
            }
        } else {
            cout << "[INFO] No QR codes found in " << scan_duration.count() << "ms (return code: " << retVal << ")" << endl;
        }

        // Clean up image memory
        if (QRImg.pimage) {
            free(QRImg.pimage);
            QRImg.pimage = nullptr;
        }
    }

    auto end_time = chrono::high_resolution_clock::now();
    auto total_duration = chrono::duration_cast<chrono::milliseconds>(end_time - start_time);

    cout << "\n" << string(60, '=') << endl;
    cout << "[SUMMARY] QR Code Detection Results" << endl;
    cout << string(60, '=') << endl;
    cout << "Total files processed: " << processed_num << "/" << filenames.size() << endl;
    cout << "Successful detections: " << success_num << endl;
    cout << "Success ratio: " << fixed << setprecision(2) << (success_num * 100.0 / processed_num) << "%" << endl;
    cout << "Total processing time: " << total_duration.count() << "ms" << endl;
    cout << "Average time per image: " << (processed_num > 0 ? total_duration.count() / processed_num : 0) << "ms" << endl;
    cout << "Timestamp: " << get_timestamp() << endl;
    cout << string(60, '=') << endl;

    if (bst_QR_scanner){
        delete bst_QR_scanner;
        bst_QR_scanner = nullptr;
        cout << "[INFO] QR scanner cleaned up" << endl;
    }
}

/*
void folder_demo_bst(string img_dir, int width, int height, string img_type)
{
    vector<string> filenames;
    FileUtils::GetFileNames(img_dir, filenames);

    int retVal = -1;
    bst_QR_config_t config;
    config.config_path = "../config/bstQRCode_1.1.2.1_Ton_Coff_20100101-20231231.cfg";
    retVal = bst_QR_init(&config);
    printf("bst_QR_init return %d\n", retVal);

    int success_num = 0;

    for (int n = 0; n < (int)filenames.size(); n++)
    {
        cout<<n<<"/"<<filenames.size()<<" "<<filenames[n]<<endl;
        bst_QR_img_t QRImg;
        Mat img;
        
        if (img_type == "jpg")
        {
            img = imread(filenames[n]);
            cvtColor(img, img, COLOR_BGR2GRAY);

            QRImg.pimage = (unsigned char*)malloc(img.cols*img.rows);
            memcpy(QRImg.pimage, img.data, img.cols*img.rows);
            QRImg.width = img.cols;
            QRImg.height = img.rows;
            QRImg.stride = img.cols;
        }
        else if (img_type == "gray")
        {
            QRImg.pimage = readbin_GRAY(filenames[n].c_str(), width, height);
            QRImg.width = width;
            QRImg.height = height;
            QRImg.stride = width;
            Mat gray(height, width, CV_8UC1, QRImg.pimage);
            img = gray.clone();
        }

        bst_QR_input_t QRInput;
        QRInput.image = QRImg;

        std::vector<bst_QR_output_t> vQROutput;
        BstCE::TimeUtils time_utils;
        time_utils.reset();
        retVal = bst_QR_scan(&QRInput, vQROutput);
        double cost_time = time_utils.get_time();

        if (vQROutput.size() > 0)
        {
            success_num += 1;
        }

        for (int i = 0; i<vQROutput.size(); i++)
        {
            Point pt1 = Point(vQROutput[i].xmin, vQROutput[i].ymin);
            Point pt2 = Point(vQROutput[i].xmax, vQROutput[i].ymax);
            Rect rect{pt1, pt2};
            Point center = (pt1 + pt2) / 2;
            rectangle(img, rect, {0, 0, 255}, 2);
            circle(img, center, rect.height/50, {0, 0, 255}, -1);
            putText(img, vQROutput[i].decode_result, {pt1.x, pt2.y + 16}, 1, 1, {0, 0, 255});
        }
        putText(img, to_string(cost_time), Point(10, 10), 1, 1, Scalar(0, 0, 255));
        
        // imshow("QR code", img);
        // waitKey(0);
        if (QRImg.pimage)
        {
            free(QRImg.pimage);
            QRImg.pimage = NULL;
        }
    }

    printf("det/decode success ratio = %f(%d/%d) \n", success_num*1.0/filenames.size(), success_num, filenames.size());
}


void img_demo_bst(string &img_path, string &img_type, int width, int height)
{
    Mat gray;
    if (img_type=="jpg"){
        Mat img = imread(img_path);
        cvtColor(img, gray, COLOR_BGR2GRAY);
    } else if (img_type=="gray"){
        gray = Mat(height,width,CV_8UC1,readbin_GRAY(img_path.c_str(),width,height));
    }
    
    int retVal = -1;
    bst_QR_config_t config;
    config.config_path = "../bstQRCode_1.1.2.1_Ton_Coff_20100101-20231231.cfg";
    retVal = bst_QR_init(&config);
    printf("bst_QR_init return %d\n", retVal);
    bst_QR_img_t QRImg;
    QRImg.pimage = gray.data;
    QRImg.width = gray.cols;
    QRImg.height = gray.rows;
    QRImg.stride = gray.cols;

    bst_QR_input_t QRInput;
    QRInput.image = QRImg;

    std::vector<bst_QR_output_t> vQROutput;
    BstCE::TimeUtils time_utils;
    time_utils.reset();
    retVal = bst_QR_scan(&QRInput, vQROutput);
    double cost_time = time_utils.get_time();

    printf("bst_QR_scan return %d\n", retVal);

    for (int i = 0; i<vQROutput.size(); i++)
    {
        Point pt1 = Point(vQROutput[i].xmin, vQROutput[i].ymin);
        Point pt2 = Point(vQROutput[i].xmax, vQROutput[i].ymax);
        Rect rect{pt1, pt2};
        Point center = (pt1 + pt2) / 2;
        rectangle(gray, rect, {0, 0, 255}, 2);
        circle(gray, center, rect.height/50, {0, 0, 255}, -1);
        putText(gray, vQROutput[i].decode_result, {pt1.x, pt2.y + 16}, 1, 1, {0, 0, 255});
    }
    putText(gray, to_string(cost_time), Point(10, 10), 1, 1, Scalar(0, 0, 255));
    
    namedWindow("QR code", WINDOW_NORMAL);
    imshow("QR code", gray);
    waitKey(0);
}

void camera_demo(string img_dir)
{
    int retVal = -1;
    bst_QR_config_t config;
    config.config_path = "../config/bstQRCode_1.1.2.1_Ton_Coff_20100101-20231231.cfg";
    retVal = bst_QR_init(&config);
    printf("bst_QR_init return %d\n", retVal);

    int det_success_num = 0;

    BstCE::TimeUtils time_utils;
    int number = 0;
    VideoCapture cap(img_dir);
    if (!cap.isOpened()) {
        std::cerr << "Could not open the video file: " << img_dir << std::endl;
    }
    VideoWriter video_writer(img_dir.substr(img_dir.find_last_of("/\\") + 1), cv::VideoWriter::fourcc('M', 'J', 'P', 'G'),
        30, cv::Size((int)cap.get(cv::CAP_PROP_FRAME_WIDTH), (int)cap.get(cv::CAP_PROP_FRAME_HEIGHT)));
    while (true)
    {
        Mat img, orig_img;
        bst_QR_img_t QRImg;
        cap >> orig_img;
        if (orig_img.empty()) break; 
        cvtColor(orig_img, img, COLOR_BGR2GRAY);
        QRImg.pimage = (unsigned char*)malloc(img.cols*img.rows);
        memcpy(QRImg.pimage, img.data, img.cols*img.rows);
        QRImg.width = img.cols;
        QRImg.height = img.rows;
        QRImg.stride = img.cols;

        bst_QR_input_t QRInput;
        QRInput.image = QRImg;

        std::vector<bst_QR_output_t> vQROutput;


        time_utils.reset();
        retVal = bst_QR_scan(&QRInput, vQROutput);
        double cost_time = time_utils.get_time();

        if (vQROutput.size() > 0)
        {
            det_success_num += 1;
        }

        for (int i = 0; i<vQROutput.size(); i++)
        {
            Point pt1 = Point(vQROutput[i].xmin, vQROutput[i].ymin);
            Point pt2 = Point(vQROutput[i].xmax, vQROutput[i].ymax);
            Rect rect{pt1, pt2};
            Point center = (pt1 + pt2) / 2;
            rectangle(orig_img, rect, {0, 0, 255}, 2);
            circle(orig_img, center, rect.height/50, {0, 0, 255}, -1);
            putText(orig_img, vQROutput[i].decode_result, {pt1.x, pt2.y + 16}, 1, 1, {0, 0, 255});
        }
        putText(orig_img, to_string(cost_time), Point(10, 10), 1, 1, Scalar(0, 0, 255));
        
        video_writer.write(orig_img);
        number += 1;
    }
}
*/

void show_usage(const char* program_name) {
    cout << "\n" << string(60, '=') << endl;
    cout << "QR Code Scanner Test Program" << endl;
    cout << string(60, '=') << endl;
    cout << "Usage: " << program_name << " <input_path> <input_type> <width> <height> <img_type>" << endl;
    cout << "\nParameters:" << endl;
    cout << "  input_path  - Path to input (file or directory)" << endl;
    cout << "  input_type  - Type of input: 'folder', 'camera', 'img'" << endl;
    cout << "  width       - Image width (for gray images, 0 for auto)" << endl;
    cout << "  height      - Image height (for gray images, 0 for auto)" << endl;
    cout << "  img_type    - Image format: 'jpg', 'gray'" << endl;
    cout << "\nSupported input types:" << endl;
    cout << "  folder      - Process all images in a directory" << endl;
    cout << "  camera      - Process video from camera (not implemented)" << endl;
    cout << "  img         - Process single image (not implemented)" << endl;
    cout << "\nSupported image types:" << endl;
    cout << "  jpg         - JPEG/JPG images (recommended)" << endl;
    cout << "  gray        - Raw grayscale binary files" << endl;
    cout << "\nExamples:" << endl;
    cout << "  " << program_name << " ./test_images folder 0 0 jpg" << endl;
    cout << "  " << program_name << " ./gray_data folder 640 480 gray" << endl;
    cout << "\nNotes:" << endl;
    cout << "  - For 'jpg' type, width and height are ignored (auto-detected)" << endl;
    cout << "  - For 'gray' type, width and height must be specified" << endl;
    cout << "  - Config file will be searched in multiple locations" << endl;
    cout << string(60, '=') << endl;
}

bool validate_parameters(const string& input_path, const string& input_type,
                        int width, int height, const string& img_type) {
    // Validate input_type
    if (input_type != "folder" && input_type != "camera" && input_type != "img") {
        cout << "[ERROR] Invalid input_type: " << input_type << endl;
        cout << "[ERROR] Must be one of: folder, camera, img" << endl;
        return false;
    }

    // Validate img_type
    if (img_type != "jpg" && img_type != "gray") {
        cout << "[ERROR] Invalid img_type: " << img_type << endl;
        cout << "[ERROR] Must be one of: jpg, gray" << endl;
        return false;
    }

    // Validate dimensions for gray images
    if (img_type == "gray") {
        if (width <= 0 || height <= 0) {
            cout << "[ERROR] For gray images, width and height must be positive integers" << endl;
            cout << "[ERROR] Got width=" << width << ", height=" << height << endl;
            return false;
        }
    }

    // Validate input path exists
    if (input_type == "folder") {
        if (!directory_exists(input_path)) {
            cout << "[ERROR] Directory does not exist: " << input_path << endl;
            return false;
        }
    } else if (input_type == "img") {
        if (!file_exists(input_path)) {
            cout << "[ERROR] File does not exist: " << input_path << endl;
            return false;
        }
    }

    return true;
}

int main(int argc, char** argv)
{
    cout << "[INFO] QR Code Scanner Test Program" << endl;
    cout << "[INFO] Build time: " << __DATE__ << " " << __TIME__ << endl;
    cout << "[INFO] Start time: " << get_timestamp() << endl;

    if (argc == 2 && (string(argv[1]) == "-h" || string(argv[1]) == "--help")) {
        show_usage(argv[0]);
        return 0;
    }

    if (argc != 6) {
        cout << "[ERROR] Invalid number of arguments. Expected 5, got " << (argc-1) << endl;
        show_usage(argv[0]);
        return -1;
    }

    string input_path = argv[1];
    string input_type = argv[2];
    int width = atoi(argv[3]);
    int height = atoi(argv[4]);
    string img_type = argv[5];

    cout << "[INFO] Parameters:" << endl;
    cout << "  Input path: " << input_path << endl;
    cout << "  Input type: " << input_type << endl;
    cout << "  Dimensions: " << width << "x" << height << endl;
    cout << "  Image type: " << img_type << endl;

    // Validate parameters
    if (!validate_parameters(input_path, input_type, width, height, img_type)) {
        cout << "[ERROR] Parameter validation failed" << endl;
        show_usage(argv[0]);
        return -1;
    }

    cout << "[INFO] Parameter validation passed" << endl;

    try {
        if (input_type == "folder") {
            folder_demo_scanner(input_path, width, height, img_type);
        } else if (input_type == "camera") {
            cout << "[ERROR] Camera input type is not implemented yet" << endl;
            return -1;
        } else if (input_type == "img") {
            cout << "[ERROR] Single image input type is not implemented yet" << endl;
            return -1;
        }
    } catch (const exception& e) {
        cout << "[ERROR] Exception occurred: " << e.what() << endl;
        return -1;
    } catch (...) {
        cout << "[ERROR] Unknown exception occurred" << endl;
        return -1;
    }

    cout << "[INFO] Program completed successfully at " << get_timestamp() << endl;
    return 0;
}