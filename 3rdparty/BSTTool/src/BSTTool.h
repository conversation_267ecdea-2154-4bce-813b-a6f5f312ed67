#ifndef XMTOOL_H
#define XMTOOL_H

#ifdef USING_LEGACY
#pragma warning(deprecated tools/api)
#endif

#include <string>
namespace BstCE
{
namespace BSTTool
{
    // image format convert
    void BGR2Gray(const unsigned char *bgr, int w, int h, unsigned char *gray);
    void RGB2Gray(const unsigned char *rgb, int w, int h, unsigned char *gray);
    void RGBA2Gray(const unsigned char *rgba, int w, int h, unsigned char *gray);
    void BGRA2Gray(const unsigned char *source, int w, int h, unsigned char *dest);
    void BGR2RGB(const unsigned char *bgr, int w, int h, unsigned char *rgb);
    void RGB2BGR(const unsigned char *rgb, int w, int h, unsigned char *bgr);
    void Gray2C3(const unsigned char *gray, int w, int h, unsigned char *bgr);
    void ABGR2Gray(const unsigned char *abgr, int w, int h, unsigned char *gray);
    
    void Gray2C3_CHW(const unsigned char* source, int w, int h, unsigned char* dest);
    void C1ToFloatC1(const unsigned char* source, int w, int h, float* dest, float mean, float normal);
    void C3ToFloatC3(const unsigned char* source, int w, int h, float* dest, const float* mean, const float* normal);

    void HWC2CHW_C3(const unsigned char* source, int w, int h, unsigned char* dest);
    void HWC2CHW_and_UC2F_C3(const unsigned char* source, int w, int h, float* dest);

    void C1ToFloatC3_CHW(const unsigned char* source, int w, int h, float* dest, float mean, float normal);
    void C3ToFloatC3_CHW(const unsigned char* source, int w, int h, float* dest, const float* mean, const float* normal);
    void C3ToFloatC3_CHW_BGR2RGB(const unsigned char* source, int w, int h, float* dest, const float* mean, const float* normal);

    void RotateC1(unsigned char *src, int srcw, int srch, int direction, unsigned char *dst, int &dstw, int &dsth);
    void RotateC3(unsigned char *src, int srcw, int srch, int direction, unsigned char *dst, int &dstw, int &dsth);

    void KannaRotateC1(unsigned char *src, int srcw, int srch, int direction, unsigned char *dst, int &dstw, int &dsth);
    void KannaRotateC3(unsigned char *src, int srcw, int srch, int direction, unsigned char *dst, int &dstw, int &dsth);
    
    // image process
    void NCNNResizeBilinearC3(const unsigned char *src, int srcw, int srch, unsigned char *dst, int dstw, int dsth);
    void NCNNResizeBilinearC1(unsigned char *src, int srcw, int srch, unsigned char *dst, int dstw, int dsth);
    void ResizeBilinearC1(unsigned char * pSrcImg, int srcWidth, int srcHeight, unsigned char * pDesImg, int desWidth, int desHeight);

    void CropImageC3(const unsigned char *src, int srcw, int srch, unsigned char *dst, int rx, int ry, int rw, int rh);
    void CropImageC1(const unsigned char *src, int srcw, int srch, unsigned char *dst, int rx, int ry, int rw, int rh);
    void CropImageYUV420(const unsigned char *src, int srcw, int srch, unsigned char *dst, int rx, int ry, int rw, int rh);

    void DrawPoint(unsigned char *src, int srcw, int srch, int srcc, int dx, int dy, int size, int r, int g, int b);
    void DrawRect(unsigned char *src, int srcw, int srch, int srcc, int x1, int y1, int x2, int y2, int size, int r, int g, int b);

    void DownSampleImageHalfScale(unsigned char *down_2_image, int down_wd, int down_ht, int prev_down_wd, int prev_down_ht, int down_move);

    // image io
    unsigned char *GetRGBImage(const std::string &name, int &w, int &h, int &c);
    unsigned char *GetBGRImage(const std::string &name, int &w, int &h, int &c);
    void DestroyBGRImage(unsigned char *ptr);
    void DestroyRGBImage(unsigned char *ptr);
    void SaveImagePNG(const std::string &name, unsigned char *img, int w, int h, int c);
    int getOrientation(const char *photoPath);

    double GetTimeMS();
}
}
#endif