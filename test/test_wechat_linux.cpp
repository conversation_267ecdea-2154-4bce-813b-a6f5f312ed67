#include <iostream>

// #include "bstQRCode.h"
#include "utils.h"
#include "bst_QR_scanner.h"
#include "opencv2/opencv.hpp"

using namespace std;
using namespace cv;
using namespace BstCE;

unsigned char* readbin_GRAY(char const* imgpath,int width,int height)
{
    unsigned char* srcImg = (unsigned char *)malloc(width * height);
    FILE *fp = fopen(imgpath , "rb+");

    if (NULL == fp)
    {
        return NULL;
    }
    fread(srcImg , 1 , width * height, fp);
    fclose(fp);
    return srcImg;
}

void folder_demo_scanner(string img_dir, int width, int height, string img_type){
    vector<string> filenames;
    FileUtils::GetFileNames(img_dir, filenames);
    int retVal = BST_QR_OK;

    BST_QR_SCANNER* bst_QR_scanner = new BST_QR_SCANNER("../Data/config/qr.cfg");

    int success_num = 0;
    for (int n = 0; n < (int)filenames.size(); n++){
        cout<<n<<"/"<<filenames.size()<<" "<<filenames[n]<<endl;
        bst_QR_img_t QRImg;
        Mat img;
        
        if (img_type == "jpg"){
            img = imread(filenames[n]);
            cvtColor(img, img, COLOR_BGR2GRAY);

            QRImg.pimage = (unsigned char*)malloc(img.cols*img.rows);
            memcpy(QRImg.pimage, img.data, img.cols*img.rows);
            QRImg.width = img.cols;
            QRImg.height = img.rows;
            QRImg.stride = img.cols;
        }else if (img_type == "gray"){
            QRImg.pimage = readbin_GRAY(filenames[n].c_str(), width, height);
            QRImg.width = width;
            QRImg.height = height;
            QRImg.stride = width;
            Mat gray(height, width, CV_8UC1, QRImg.pimage);
            img = gray.clone();
        }

        bst_QR_input_t QRInput;
        QRInput.image = QRImg;

        std::vector<bst_QR_output_t> vQROutput;
        retVal = bst_QR_scanner->scan(&QRInput, vQROutput);

        if (vQROutput.size() > 0){
            success_num += 1;
        }
    }

    printf("det/decode success ratio = %f(%d/%d) \n", success_num*1.0/filenames.size(), success_num, filenames.size());

    if (bst_QR_scanner){
        delete bst_QR_scanner;
        bst_QR_scanner = NULL;
    }
}

/*
void folder_demo_bst(string img_dir, int width, int height, string img_type)
{
    vector<string> filenames;
    FileUtils::GetFileNames(img_dir, filenames);

    int retVal = -1;
    bst_QR_config_t config;
    config.config_path = "../config/bstQRCode_1.1.2.1_Ton_Coff_20100101-20231231.cfg";
    retVal = bst_QR_init(&config);
    printf("bst_QR_init return %d\n", retVal);

    int success_num = 0;

    for (int n = 0; n < (int)filenames.size(); n++)
    {
        cout<<n<<"/"<<filenames.size()<<" "<<filenames[n]<<endl;
        bst_QR_img_t QRImg;
        Mat img;
        
        if (img_type == "jpg")
        {
            img = imread(filenames[n]);
            cvtColor(img, img, COLOR_BGR2GRAY);

            QRImg.pimage = (unsigned char*)malloc(img.cols*img.rows);
            memcpy(QRImg.pimage, img.data, img.cols*img.rows);
            QRImg.width = img.cols;
            QRImg.height = img.rows;
            QRImg.stride = img.cols;
        }
        else if (img_type == "gray")
        {
            QRImg.pimage = readbin_GRAY(filenames[n].c_str(), width, height);
            QRImg.width = width;
            QRImg.height = height;
            QRImg.stride = width;
            Mat gray(height, width, CV_8UC1, QRImg.pimage);
            img = gray.clone();
        }

        bst_QR_input_t QRInput;
        QRInput.image = QRImg;

        std::vector<bst_QR_output_t> vQROutput;
        BstCE::TimeUtils time_utils;
        time_utils.reset();
        retVal = bst_QR_scan(&QRInput, vQROutput);
        double cost_time = time_utils.get_time();

        if (vQROutput.size() > 0)
        {
            success_num += 1;
        }

        for (int i = 0; i<vQROutput.size(); i++)
        {
            Point pt1 = Point(vQROutput[i].xmin, vQROutput[i].ymin);
            Point pt2 = Point(vQROutput[i].xmax, vQROutput[i].ymax);
            Rect rect{pt1, pt2};
            Point center = (pt1 + pt2) / 2;
            rectangle(img, rect, {0, 0, 255}, 2);
            circle(img, center, rect.height/50, {0, 0, 255}, -1);
            putText(img, vQROutput[i].decode_result, {pt1.x, pt2.y + 16}, 1, 1, {0, 0, 255});
        }
        putText(img, to_string(cost_time), Point(10, 10), 1, 1, Scalar(0, 0, 255));
        
        // imshow("QR code", img);
        // waitKey(0);
        if (QRImg.pimage)
        {
            free(QRImg.pimage);
            QRImg.pimage = NULL;
        }
    }

    printf("det/decode success ratio = %f(%d/%d) \n", success_num*1.0/filenames.size(), success_num, filenames.size());
}


void img_demo_bst(string &img_path, string &img_type, int width, int height)
{
    Mat gray;
    if (img_type=="jpg"){
        Mat img = imread(img_path);
        cvtColor(img, gray, COLOR_BGR2GRAY);
    } else if (img_type=="gray"){
        gray = Mat(height,width,CV_8UC1,readbin_GRAY(img_path.c_str(),width,height));
    }
    
    int retVal = -1;
    bst_QR_config_t config;
    config.config_path = "../bstQRCode_1.1.2.1_Ton_Coff_20100101-20231231.cfg";
    retVal = bst_QR_init(&config);
    printf("bst_QR_init return %d\n", retVal);
    bst_QR_img_t QRImg;
    QRImg.pimage = gray.data;
    QRImg.width = gray.cols;
    QRImg.height = gray.rows;
    QRImg.stride = gray.cols;

    bst_QR_input_t QRInput;
    QRInput.image = QRImg;

    std::vector<bst_QR_output_t> vQROutput;
    BstCE::TimeUtils time_utils;
    time_utils.reset();
    retVal = bst_QR_scan(&QRInput, vQROutput);
    double cost_time = time_utils.get_time();

    printf("bst_QR_scan return %d\n", retVal);

    for (int i = 0; i<vQROutput.size(); i++)
    {
        Point pt1 = Point(vQROutput[i].xmin, vQROutput[i].ymin);
        Point pt2 = Point(vQROutput[i].xmax, vQROutput[i].ymax);
        Rect rect{pt1, pt2};
        Point center = (pt1 + pt2) / 2;
        rectangle(gray, rect, {0, 0, 255}, 2);
        circle(gray, center, rect.height/50, {0, 0, 255}, -1);
        putText(gray, vQROutput[i].decode_result, {pt1.x, pt2.y + 16}, 1, 1, {0, 0, 255});
    }
    putText(gray, to_string(cost_time), Point(10, 10), 1, 1, Scalar(0, 0, 255));
    
    namedWindow("QR code", WINDOW_NORMAL);
    imshow("QR code", gray);
    waitKey(0);
}

void camera_demo(string img_dir)
{
    int retVal = -1;
    bst_QR_config_t config;
    config.config_path = "../config/bstQRCode_1.1.2.1_Ton_Coff_20100101-20231231.cfg";
    retVal = bst_QR_init(&config);
    printf("bst_QR_init return %d\n", retVal);

    int det_success_num = 0;

    BstCE::TimeUtils time_utils;
    int number = 0;
    VideoCapture cap(img_dir);
    if (!cap.isOpened()) {
        std::cerr << "Could not open the video file: " << img_dir << std::endl;
    }
    VideoWriter video_writer(img_dir.substr(img_dir.find_last_of("/\\") + 1), cv::VideoWriter::fourcc('M', 'J', 'P', 'G'),
        30, cv::Size((int)cap.get(cv::CAP_PROP_FRAME_WIDTH), (int)cap.get(cv::CAP_PROP_FRAME_HEIGHT)));
    while (true)
    {
        Mat img, orig_img;
        bst_QR_img_t QRImg;
        cap >> orig_img;
        if (orig_img.empty()) break; 
        cvtColor(orig_img, img, COLOR_BGR2GRAY);
        QRImg.pimage = (unsigned char*)malloc(img.cols*img.rows);
        memcpy(QRImg.pimage, img.data, img.cols*img.rows);
        QRImg.width = img.cols;
        QRImg.height = img.rows;
        QRImg.stride = img.cols;

        bst_QR_input_t QRInput;
        QRInput.image = QRImg;

        std::vector<bst_QR_output_t> vQROutput;


        time_utils.reset();
        retVal = bst_QR_scan(&QRInput, vQROutput);
        double cost_time = time_utils.get_time();

        if (vQROutput.size() > 0)
        {
            det_success_num += 1;
        }

        for (int i = 0; i<vQROutput.size(); i++)
        {
            Point pt1 = Point(vQROutput[i].xmin, vQROutput[i].ymin);
            Point pt2 = Point(vQROutput[i].xmax, vQROutput[i].ymax);
            Rect rect{pt1, pt2};
            Point center = (pt1 + pt2) / 2;
            rectangle(orig_img, rect, {0, 0, 255}, 2);
            circle(orig_img, center, rect.height/50, {0, 0, 255}, -1);
            putText(orig_img, vQROutput[i].decode_result, {pt1.x, pt2.y + 16}, 1, 1, {0, 0, 255});
        }
        putText(orig_img, to_string(cost_time), Point(10, 10), 1, 1, Scalar(0, 0, 255));
        
        video_writer.write(orig_img);
        number += 1;
    }
}
*/

int main(int argc, char** argv)
{
    if (argc != 6)
    {
        cout<<"Usage: <input_path> <input_type> <width> <height> <img_type>"<<endl;
        return -1;
    }

    string input_path = argv[1];
    string input_type = argv[2];
    int width = atoi(argv[3]);
    int height = atoi(argv[4]);
    string img_type = argv[5];

    if (input_type == "folder")
    {
        // folder_demo_bst(input_path, width, height, img_type);
        folder_demo_scanner(input_path, width, height, img_type);
    }else if (input_type == "camera")
    {
        // camera_demo(input_path);
    } else if (input_type == "img")
    {
        // img_demo_bst(input_path,img_type,width,height);
    }

    return 0;
}