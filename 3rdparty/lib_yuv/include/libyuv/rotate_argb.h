/*
 *  Copyright 2012 The LibYuv Project Authors. All rights reserved.
 *
 *  Use of this source code is governed by a BSD-style license
 *  that can be found in the LICENSE file in the root of the source
 *  tree. An additional intellectual property rights grant can be found
 *  in the file PATENTS. All contributing project authors may
 *  be found in the AUTHORS file in the root of the source tree.
 */

#ifndef INCLUDE_LIB_BST_YUV_ROTATE_ARGB_H_
#define INCLUDE_LIB_BST_YUV_ROTATE_ARGB_H_

#include "libyuv/basic_types.h"
#include "libyuv/rotate.h"  // For RotationMode.

#ifdef __cplusplus
namespace lib_bst_yuv {
extern "C" {
#endif

// Rotate ARGB frame
LIBYUV_API
int ARGBRotate(const uint8_t* src_argb,
               int src_stride_argb,
               uint8_t* dst_argb,
               int dst_stride_argb,
               int src_width,
               int src_height,
               enum RotationMode mode);

#ifdef __cplusplus
}  // extern "C"
}  // namespace lib_bst_yuv
#endif

#endif  // INCLUDE_LIB_BST_YUV_ROTATE_ARGB_H_
