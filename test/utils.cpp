#include "utils.h"
#include <sys/stat.h>
#include <sys/time.h>
#include <dirent.h>
#include <unistd.h>
#include <sys/types.h>
#include <stdint.h>
#include <iomanip>
#include <vector>
#include <map>
#include <string>
#include <string.h>
#include <iostream>
#include <fstream>
#define ACCESS(fileName,accessMode) access(fileName,accessMode)
#define MKDIR(path) mkdir(path,S_IRWXU | S_IRWXG | S_IROTH | S_IXOTH)
using namespace BstCE;
using namespace std;

void FileUtils::GetFileNames(const std::string &path, std::vector<std::string> &files)
{
    const std::string path0 = path;
    DIR *pDir;
    struct dirent *ptr;

    struct stat s;
    lstat(path.c_str(), &s);

    if (!S_ISDIR(s.st_mode))
    {
        //cout << "not a valid directory: " << path << endl;
        return;
    }

    if (!(pDir = opendir(path.c_str())))
    {
        //cout << "opendir error: " << path << endl;
        return;
    }
    //int i = 0;
    std::string subFile;
    while ((ptr = readdir(pDir)) != 0)
    {
        subFile = ptr->d_name;
        if (subFile == "." || subFile == "..")
            continue;

        if(ptr->d_type & DT_DIR)
        {
            subFile = path0 + "/" + subFile;
            GetFileNames(subFile, files);
        }
        else 
        {
            subFile = path0 + "/" + subFile;
            
            if (IsImage(subFile))
            {
                files.push_back(subFile);
            }
        }

    }
    closedir(pDir);
}

string FileUtils::GetFileName(const string &path)
{
    size_t pos = path.find_last_of('/');

    if(pos != string::npos)
    {
        return path.substr(pos + 1);
    }
    else
    {
        pos = path.find_last_of('\\');
        if(pos != string::npos)
        {
            return path.substr(pos + 1);
        }
    }
    
    return path;
}


string FileUtils::GetBaseName(const string &path)
{
    string filename = GetFileName(path);
    size_t pos = filename.find_last_of('.');
    if(pos != string::npos)
    {
        return filename.substr(0, pos);
    }
    return filename;
}

string FileUtils::GetBaseDir(const string &path)
{
    size_t pos = path.find_last_of('.');
    if (pos != string::npos)
    {
        return path.substr(0, pos);
    }
    return path;
}

bool FileUtils::IsFolderExist(const string &path)
{
    return !access(path.c_str(), F_OK);
}

void FileUtils::MakeDirs(const string &directoryPath)
{
    uint32_t dirPathLen = directoryPath.length();
    unsigned int len = 512;
    if (dirPathLen > len)
    {
        // return -1;
    }
    // char tmpDirPath[len] = { 0 };
    char tmpDirPath[len];
    tmpDirPath[len] = {0};
    for (uint32_t i = 0; i < dirPathLen; ++i)
    {
        tmpDirPath[i] = directoryPath[i];
        if (tmpDirPath[i] == '\\' || tmpDirPath[i] == '/')
        {
            if (ACCESS(tmpDirPath, 0) != 0)
            {
                int32_t ret = MKDIR(tmpDirPath);
                if (ret != 0)
                {
                    // return ret;
                }
            }
        }
    }
}

void FileUtils::CreateFolders(const char* dir)
{
    char order[1000] = "mkdir -p ";
    strcat(order, dir);
    system(order);
}

string FileUtils::GetFileSuffix(const string &path)
{
    size_t pos = path.find_last_of('.');
    if(pos != string::npos)
    {
        return path.substr(pos + 1);
    }
    return string();
}

bool FileUtils::IsImage(const string &name)
{
    string ext = FileUtils::GetFileSuffix(name);
    if(ext == "jpg" || ext == "JPG"
        || ext == "png" || ext == "PNG"
        || ext == "bmp" || ext == "BMP"
        || ext == "jpeg" || ext == "JPEG"
        || ext == "jfif" || ext == "gray"
        || ext == "nv21" || ext == "NV21")
    {
        return true;
    }
    return false;
}

bool FileUtils::IsVideo(const string &name)
{   
    string ext = FileUtils::GetFileSuffix(name);
    if(ext == "mp4" || ext == "MP4"
        || ext == "mov" || ext == "MOV"
        || ext == "avi" || ext == "AVI"
        || ext == "mkv" || ext == "MKV")
    {
        return true;
    }
    return false;
}

void FileUtils::CopyFile(const std::string &from, const std::string &to)
{
    std::ifstream src(from.c_str(),std::ios::binary);
	std::ofstream dst(to.c_str(),std::ios::binary);

	dst<<src.rdbuf();
}
void FileUtils::CreatFloder(const std::string &filename)
{
    mkdir(filename.c_str(), 0777);
}

void FileUtils::DelFile(const std::string &filename)
{
    remove(filename.c_str());
}

void FileUtils::saveFloat2Txt(std::string &path, float *data, int length)
{
    ofstream of;
    of.open(path.c_str(), ios::out | ios::trunc);
    of << fixed;

    for (int i = 0; i < length; ++i)
    {
        // of << setprecision(4) << data[i] << endl;
        of << data[i] << endl;
    }

    of.close();
}

vector<string> StringUtils::Split(const string &src, char delimiter)
{
    vector<string> r;
    string str = src;
    while (!str.empty())
    {
        int ind = str.find_first_of(delimiter);
        if (ind == -1)
        {
            r.push_back(str);
            str.clear();
        }
        else
        {
            r.push_back(str.substr(0, ind));
            str = str.substr(ind + 1, str.size() - ind - 1);
        }
    }
    return r;
}

std::string StringUtils::Replace(const string &src, const char *from, const char *to)
{
    string::size_type pos = 0;
	string::size_type fromlen = strlen(from);
	string::size_type tolen = strlen(to);

    string str = src;
	pos = str.find(from,pos);
	while(pos != string::npos)
	{
		str.replace(pos,fromlen,to);
		pos = str.find(from,pos + tolen);
	}
    return str;
}

void DataUtils::save_data2txt(float* data, int length, std::string save_path)
{
    ofstream outfile;
    outfile.open(save_path.c_str(), ios::out | ios::trunc);
    for (int i = 0; i < length; ++i)
    {
        outfile << std::setprecision(4) << data[i] << endl;
    }
}