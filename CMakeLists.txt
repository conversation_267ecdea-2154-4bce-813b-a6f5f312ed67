cmake_minimum_required(VERSION 2.8.12)
project(bstQRCode)

if (${TARGET_PLATFORM} STREQUAL "linux")
    ADD_DEFINITIONS(-DRUN_LINUX)
    SET(CMAKE_EXE_LINKER_FLAGS "${CMAKE_EXE_LINKER_FLAGS}    -fPIC -fopenmp ")
    SET(CMAKE_SHARED_LINKER_FLAGS "${CMAKE_EXE_LINKER_FLAGS} -fPIC -fopenmp ")
    SET(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -fPIC -ldl -std=c++14")
    SET(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -fPIC")
    include_directories(
        ${CMAKE_CURRENT_SOURCE_DIR}/src
        ${CMAKE_CURRENT_SOURCE_DIR}/src/bstai_qrcode
        ${CMAKE_CURRENT_SOURCE_DIR}/src/bstai_qrcode/detector
        ${CMAKE_CURRENT_SOURCE_DIR}/src/bstai_qrcode/scale
        ${CMAKE_CURRENT_SOURCE_DIR}/src/bstai_qrcode/zxing
        ${CMAKE_CURRENT_SOURCE_DIR}/test
        ${CMAKE_CURRENT_SOURCE_DIR}/ImgLib
        ${CMAKE_CURRENT_SOURCE_DIR}/mathlib
    )
    include_directories(${PROJECT_SOURCE_DIR}/Util)
    include_directories ("${PROJECT_SOURCE_DIR}/3rdparty/mipp")
    add_subdirectory("${PROJECT_SOURCE_DIR}/Util")
    add_subdirectory("${PROJECT_SOURCE_DIR}/ImgLib")
    add_subdirectory("${PROJECT_SOURCE_DIR}/mathlib")
    option(USE_BSTTOOL "compile with use_bsttool or not" ON)
    if (USE_BSTTOOL)
        ADD_DEFINITIONS(-DUSE_BSTTOOL)
        include_directories(${CMAKE_CURRENT_SOURCE_DIR}/3rdparty/BSTTool/src)
        add_subdirectory(${CMAKE_CURRENT_SOURCE_DIR}/3rdparty/BSTTool BSTTool)
    endif()
    add_definitions(-D__SSE__)
    add_definitions(-D__SSE2__)
    add_definitions(-D__SSE4_1__)
    add_definitions(-D__SSE3__)
    add_definitions(-D__SSSE3__)
    #add_definitions(-D__AVX__)
    #add_definitions(-D__AVX512__)
    #add_definitions(-D__AVX512F__)
    option(USE_TFLITE "compile with use_tflite or not" ON)
    if (USE_TFLITE)
        ADD_DEFINITIONS(-DUSE_TFLITE)
        include_directories(${CMAKE_CURRENT_SOURCE_DIR}/3rdparty/tensorflow2.10/include)
        SET("TfLite_LIBS" 
            ${CMAKE_CURRENT_SOURCE_DIR}/3rdparty/tensorflow2.10/libs/linux-amd64/libtensorflow-lite.a
            ${CMAKE_CURRENT_SOURCE_DIR}/3rdparty/tensorflow2.10/libs/linux-amd64/libtensorflow-lite-deps.a
            ${CMAKE_CURRENT_SOURCE_DIR}/3rdparty/tensorflow2.10/libs/linux-amd64/libpthreadpool.a
        )
    endif()

    if (ANDROID)

    else()
        set(Opencv_DIR ${CMAKE_CURRENT_SOURCE_DIR}/3rdparty/opencv/4.2.0/pc)
        include_directories(${Opencv_DIR}/include/opencv4)
        link_directories(${Opencv_DIR}/lib)
        set(
            OpenCV_LIBS
            opencv_core
            opencv_highgui
            opencv_imgproc
            opencv_imgcodecs
            opencv_videoio
            opencv_dnn
        )
    endif()
    set(
        target_lib
        Util
        BSTTool
        ${OpenCV_LIBS}
        ${TfLite_LIBS}
    )
    set(
        SRC_FILE
        ${CMAKE_CURRENT_SOURCE_DIR}/src/bst_QR_scanner.cpp
        ${CMAKE_CURRENT_SOURCE_DIR}/src/BSTDes.cpp
        ${CMAKE_CURRENT_SOURCE_DIR}/src/bstImgProc.cpp
        ${CMAKE_CURRENT_SOURCE_DIR}/src/DES.cpp
        ${CMAKE_CURRENT_SOURCE_DIR}/src/Filter.cpp
		${CMAKE_CURRENT_SOURCE_DIR}/src/xxhash.c
    )
    
    file(GLOB_RECURSE SRC_FILE_INNER ${CMAKE_CURRENT_SOURCE_DIR}/src/**/*.cpp)
    list(APPEND SRC_FILE ${SRC_FILE_INNER})
    
    add_library(${PROJECT_NAME} SHARED ${SRC_FILE})
    add_executable(QRCodeExc ${CMAKE_CURRENT_SOURCE_DIR}/test/test_wechat_linux.cpp ${CMAKE_CURRENT_SOURCE_DIR}/test/utils.cpp)
    target_link_libraries(${PROJECT_NAME} ImgLib mathlib)
    target_link_libraries(QRCodeExc ${PROJECT_NAME} ${target_lib})
else()
    ADD_DEFINITIONS(-DFASTCV)
    set(wechatlib wechat)
    set(EXE_NAME Test)
    set(G_EXE_NAME gTest)
    set(PTHREAD_LIBS "")
    if(MSVC)
        set (TFLITE_DIR "E:/code/svn_code/11_AiScene/branch/AiScneeV4/3rdparty/tensorflow2.4")
        include_directories("${PROJECT_SOURCE_DIR}/pthread_for_windows/include")
        include_directories("${PROJECT_SOURCE_DIR}/3rdparty")
        include_directories("${PROJECT_SOURCE_DIR}")
        include_directories("${PROJECT_SOURCE_DIR}/3rdparty/FastCV/inc")
        include_directories("${PROJECT_SOURCE_DIR}/3rdparty/BSTTool/src")
        include_directories ("${TFLITE_DIR}/include/tensorflow")
        include_directories ("${TFLITE_DIR}/include")
        include_directories ("${TFLITE_DIR}/include/tensorflow/flatbuffers")
        include_directories ("${PROJECT_SOURCE_DIR}/OCLNative/include")
        include_directories ("${PROJECT_SOURCE_DIR}/OCLNative/src")
        include_directories ("${PROJECT_SOURCE_DIR}/3rdparty/mipp")
        include_directories ("${PROJECT_SOURCE_DIR}/3rdparty/simpleocv/debug")
        #include_directories ("${PROJECT_SOURCE_DIR}/3rdparty/xsimd")
        link_directories("${PROJECT_SOURCE_DIR}/pthread_for_windows/lib/x64" 
                         "${CMAKE_CURRENT_SOURCE_DIR}/3rdparty/vld/lib/Win64"
                         "${CMAKE_CURRENT_SOURCE_DIR}/3rdparty/FastCV/lib/VS2013/lib64/MD"
        				 "${TFLITE_DIR}/libs/x64_debug"
						 "${PROJECT_SOURCE_DIR}/3rdparty/simpleocv/debug"
        				 )
        set(PTHREAD_LIBS "pthreadVC2.lib")
        add_definitions(-D_TIMESPEC_DEFINED)
        add_definitions(-D__SSE__)
        add_definitions(-D__SSE2__)
        add_definitions(-D__SSE4_1__)
        add_definitions(-D__SSE3__)
        add_definitions(-D__SSSE3__)
        #add_definitions(-D__AVX__)
        #add_definitions(-D__AVX512__)
        #add_definitions(-D__AVX512F__)
		set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} /arch:SSE2")
    endif()
    include_directories(
        ${CMAKE_CURRENT_SOURCE_DIR}/src
        ${CMAKE_CURRENT_SOURCE_DIR}/src/bstai_qrcode
        ${CMAKE_CURRENT_SOURCE_DIR}/3rdparty/vld/include
    )
    add_definitions(-DNOMINMAX)
    add_definitions(-DMOTION_TRACK)
    add_definitions(-DLIBLICENSE_AS_STATIC_LIB)
    add_definitions(-DUSE_BSTTOOL)
    set(CMAKE_WINDOWS_EXPORT_ALL_SYMBOLS ON)
    add_subdirectory("${PROJECT_SOURCE_DIR}/3rdparty/BSTTool")
    add_subdirectory("${PROJECT_SOURCE_DIR}/Util")
    add_subdirectory("${PROJECT_SOURCE_DIR}/mathlib")
    add_subdirectory("${PROJECT_SOURCE_DIR}/ImgLib")
	add_subdirectory("${PROJECT_SOURCE_DIR}/OCLNative")
    add_definitions(-DNOGDI)
    file(GLOB_RECURSE SRC_WECHAT_QR ${CMAKE_CURRENT_SOURCE_DIR}/src/bstai_qrcode/**.cpp ${CMAKE_CURRENT_SOURCE_DIR}/src/bstai_qrcode/**.hpp)
                            
    file(GLOB SRC_FILE ${CMAKE_CURRENT_SOURCE_DIR}/src/*.cpp ${CMAKE_CURRENT_SOURCE_DIR}/src/*.h ${CMAKE_CURRENT_SOURCE_DIR}/src/*.c)

    add_library(${wechatlib} ${SRC_WECHAT_QR})
    add_library(${PROJECT_NAME} ${SRC_FILE})
    add_executable(${EXE_NAME} ${CMAKE_CURRENT_SOURCE_DIR}/Test/test_wechat.cpp ${CMAKE_CURRENT_SOURCE_DIR}/Test/AlgPipepine.cpp)
	file(GLOB_RECURSE G_SRC_FILE_INNER ${CMAKE_CURRENT_SOURCE_DIR}/InterfaceTest/**.cpp)
	include_directories("F:/SDK/android-ndk-r14b/sources/third_party/googletest/googletest/include"
	                    "${CMAKE_CURRENT_SOURCE_DIR}/InterfaceTest"
						"${CMAKE_CURRENT_SOURCE_DIR}/InterfaceTest/TestSuite")
    add_executable(${G_EXE_NAME} ${G_SRC_FILE_INNER})
    target_link_libraries(${PROJECT_NAME} ${PTHREAD_LIBS} ${wechatlib} tensorflowlite.dll.if.lib mathlib ImgLib) #BSTOpenCL ImgLib Util ParaEcrpt vld.lib)
    target_link_libraries(${EXE_NAME} ${PROJECT_NAME} simpleocv BSTTool libfastcv Util legacy_stdio_definitions mathlib ImgLib)
    set_target_properties(${EXE_NAME} PROPERTIES VS_DEBUGGER_WORKING_DIRECTORY ${PROJECT_SOURCE_DIR}/Data)
endif()
