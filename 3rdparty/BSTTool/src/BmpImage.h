#ifndef BMP_IMAGE_HEADER_201406160947
#define BMP_IMAGE_HEADER_201406160947
#include <stdio.h>
#include <stdlib.h>
#include <ctype.h>
#include <string.h>

#pragma warning(deprecated tools/api)

//typedef struct tagRGBQUAD
//{
//    unsigned char    rgbBlue;
//    unsigned char    rgbGreen;
//    unsigned char    rgbRed;
//    unsigned char    rgbReserved;
//} RGBQUAD;


//typedef struct tagBITMAPFILEHEADER
//{
//    unsigned short    bfType;
//    unsigned long   bfSize;
//    unsigned short    bfReserved1;
//    unsigned short    bfReserved2;
//    unsigned long   bfOffBits;
//} BITMAPFILEHEADER;
//
//
//typedef struct tagBITMAPINFOHEADER
//{
//    unsigned long      biSize;
//    long       biWidth;
//    long       biHeight;
//    unsigned short       biPlanes;
//    unsigned short       biBitCount;
//    unsigned long      biCompression;
//    unsigned long      biSizeImage;
//    long       biXPelsPerMeter;
//    long       biYPelsPerMeter;
//    unsigned long      biClrUsed;
//    unsigned long      biClrImportant;
//} BITMAPINFOHEADER;

typedef struct tagDRAW_RECT
{
    long    left;
    long    top;
    long    right;
    long    bottom;
} DRAW_RECT;

typedef struct SimpleBmpImage_tag
{
//    SimpleBmpImage( unsigned char* p, int w, int h, int b, bool v) {
//         pData = p;       // Image data, do not contain bytes of data alignment
//         nImgW = w;          // the width of image
//         nImgH = h;          // the height of image
//         nBits = b;          //  8,16, 24,32
//         bValid = v;        // whether it is valid
//    }
    unsigned char* pData;       // Image data, do not contain bytes of data alignment
    int nImgW;          // the width of image
    int nImgH;          // the height of image
    int nBits;          //  8,16, 24,32
    bool bValid;        // whether it is valid
    bool bCreateMem;    // whether the memory has allocated.
} SimpleBmpImage;

//////////////////////////////////////////////////////////////////////////

/*!
    \fn     read bmp image form the file
    \param  pMIImg, the output image from file
    \param  pImgPath, the path of image file
    \return 0 successful, or else failed
 */
int LoadBmp(SimpleBmpImage* pMIImg,
            const char* pImgPath);


/*!
    \fn     relese memory of image struct
    \param  pMIImg, the image is to be released
    \return 0 successful, or else failed
 */
int ReleaseBmpImage(SimpleBmpImage* pMIImg);


/*!
    \fn     initialize the image struct with specified buffer, do not copy the memory
    \param  pMIImg, the image struct is to be initialized
    \param  pImgSrc, the raw image data buffer
    \param  nImgw, the width of image
    \param  nImgh, the height of image
    \param  nBits, the bit of bitmap image
    \return 0 successful, or else failed
 */
int BmpInitWithPtr(SimpleBmpImage* pMIImg,
                   unsigned char* pImgSrc,
                   int nImgW,
                   int nImgH,
                   int nBits);

/*!
    \fn     initialize the image struct with specified buffer, copy the memory
    \param  pMIImg, the image struct is to be initialized
    \param  pImgSrc, the raw image data buffer
    \param  nImgw, the width of image
    \param  nImgh, the height of image
    \param  nBits, the bit of bitmap image
    \return 0 successful, or else failed
 */
int BmpInitWithData(SimpleBmpImage* pMIImg,
                    unsigned char* pImgSrc,
                    int nImgW,
                    int nImgH,
                    int nBits);

/*!
    \fn     initialize the image object with other image object
    \param  pDstImg, the destination image object
    \param  pSrcImg, the source image object
    \return 0 successful, or else failed
 */
int BmpImageClone(SimpleBmpImage* pDstImg,
                  SimpleBmpImage* pSrcImg);

/*!
    \fn     read bmp image form the file
    \param  pImgPath, the path of image file
    \param  pMIImg, the output image from file
    \return 0 failed, or else successful
 */
int SaveBmp(const char* pImgPath,
            SimpleBmpImage* pMIImg);

/*!
    \fn     save the image object in continus buffer, include bmp header information
    \param  ppOutMem, the output buffer of image object
    \param  pnSize, the size of output buffer
    \param  pMIImg, the image object
    \return the address of output buffer
 */
unsigned char* BmpCreateSaveToMem(unsigned char** ppOutMem,
                                  int* pnSize,
                                  SimpleBmpImage* pMIImg);

/*!
    \fn     release the memory that is allocate by SimpleBmpImage object
    \param  the pointer of the pointer of the memory
    \return null
 */
void ReleaseMem(unsigned char** pOutMem);

/*!
    \fn     create the memory, the size is nNumbyte
    \param  nNumbyte, the size is to be allocated
    \return the allocated memory address.
 */
void* CreateMem(int nNumbyte);

/*!
    \fn     change the colour image to the gray image
    \param  pDstImg, the destination gray image.
    \param  pSrcImg, the source color image.
    \return 0 successful, or else failed
 */
int BmpRGB2Gray(SimpleBmpImage* pDstImg,
                SimpleBmpImage* pSrcImg);

/*!
    \fn     draw Rectangle in specified image
    \param  pImg, the image struct
    \param  rect, the rectangle area is to be draw
    \param  nThickness, the thickness of pen
    \param  r, the value of red
    \param  g, the value of green
    \param  b, the value of blue
    \return 0 successful, or else failed
 */
int BmpDrawRect(SimpleBmpImage* pImg,
                DRAW_RECT rect,
                int nThickness,
                unsigned char r,
                unsigned char g,
                unsigned char b);

/*!
    \fn     draw vertial line in specifiedn image
    \param  row1, the begin row of line
    \param  row2, the end row of line
    \param  col, the column of line
    \param  nThickness, the thickness of pen
    \param  r, the value of red
    \param  g, the value of green
    \param  b, the value of blue
    \return 0 successful, or else failed
 */
int BmpDrawVLine(SimpleBmpImage* pMIImg,
                 int row1,
                 int row2,
                 int col,
                 int nThickness,
                 unsigned char r,
                 unsigned char g,
                 unsigned char b);

/*!
    \fn     draw horizontal line in specifiedn image
    \param  col1, the begin column of line
    \param  col2, the end column of line
    \param  row, the row of line
    \param  nThickness, the thickness of pen
    \param  r, the value of red
    \param  g, the value of green
    \param  b, the value of blue
    \return 0 successful, or else failed
 */
int BmpDrawHLine(SimpleBmpImage* pMIImg,
                 int col1,
                 int col2,
                 int row,
                 int nThickness,
                 unsigned char r,
                 unsigned char g,
                 unsigned char b);

/*!
    \fn     draw point in specifiedn image
    \param  col, the column of point
    \param  row, the row of point
    \param  nRadius, the radius of point
    \param  nThickness, the thickness of pen
    \param  r, the value of red
    \param  g, the value of green
    \param  b, the value of blue
    \return 0 successful, or else failed
 */
int BmpDrawPoint(
    SimpleBmpImage* pMIImg,
    int row,
    int col,
    int nRadius,
    int nThickness,
    unsigned char r,
    unsigned char g,
    unsigned char b);


/*!
    \fn     whether the rectangle area is valid
    \param  rect, the rectangle area
    \param  nImgw, the width of image
    \param  nImgH, the height of image
    \return TRUE is valid, or else is invalid
 */
bool BmpValidRect(
    DRAW_RECT rect,
    int nImgW,
    int nImgH);

/*!
    \fn     convert color image to gram image
    \param  pDstImg, the destination gray image buffer
    \param  pSrcImg, the source color image buffer
    \param  nWidth, the width of image
    \param  nHeight, the height of image
    \return TRUE is successful, or else is failed
 */
bool BGR2Gray(
    unsigned char* pDstImg,
    unsigned char* pSrcImg,
    int nWidth,
    int nHeight);

/*!
    \fn     flip image in vertical direction
    \param  pDestImg, the destination image data buffer
    \param  pSrcImg, the source image data buffer
    \param  nWidth, the width of image
    \param  nHeight, the height of image
    \param  nSrcChannel, the channel of source image
    \param  nDestChannel, the channel of destination image
    \return true successful, or else failed
 */
bool FlipImageVertical(
    unsigned char * pDesImg,
    unsigned char * pSrcImg,
    int nWidth,
    int nHeight,
    int nSrcChannel,
    int nDestChannel);
    
unsigned char* BmpCreateSaveToMem(  unsigned char** ppMen,   int* pnSize, SimpleBmpImage* pMIImg);

// mode 0 : CbCr, NV12, mode 1 : CrCb, NV21
void BGR24_to_YUV420SP(unsigned char *pBGR24, int wd, int ht, unsigned char* pYUV420, int mode);

// mode 0 : CbCr, NV12, mode 1 : CrCb, NV21
void YUV420SP_to_BGR24(unsigned char *pYUV420, int wd, int ht, unsigned char* pBGR24, int mode);

void ResetColor(unsigned char *pImage, unsigned char *pMask, int wd, int ht, unsigned char in_r, unsigned char in_g, unsigned char in_b, int os_flag);

void ResetColorLineLash(unsigned char *pImage, unsigned char *pMask, int wd, int ht, 
    unsigned char in_r, unsigned char in_g, unsigned char in_b, int os_flag);

void DrawPtOnSingleMask(unsigned char* mask, int wd, int ht, int ptx, int pty, unsigned char val, int thickness);
void DrawLineOnSingleMask(unsigned char* mask, int wd, int ht, int start_x, int start_y, int end_x, int end_y, unsigned char val, int thickness);

#endif
