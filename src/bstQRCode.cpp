#include "bstQRCode.h"
#include "../Util/BindCore.h"
#ifdef MOTION_TRACK
#include "bst_QR_track.h"
#else
#include "bst_QR_scanner.h"
#endif
#include "DES.h"
#include "../Util/TimeStatistic.h"
#if !defined(WIN32)
#include <sys/time.h>
#endif
#include <stdio.h>
#if !defined(WIN32)
#include <unistd.h>
#include <sys/stat.h>
#define Sleep(X) usleep((X)*1000)
#else
#include <windows.h>
#include "vld.h"
#endif
#include "../Util/MemPool.h"
#if defined(ANDROID)
#include <sys/system_properties.h>
#endif
#include "../Util/AutoLock.h"

extern char dumpPath[];;
char dumpFolder[256];
static long long mlDumpUsec;
static int mnFrameCount;
#include <thread>
using std::thread;
using std::string;
#ifdef MOTION_TRACK
static BstMultiQRTracker* bst_QR_scanner = NULL;
static int isInited = 0;
#else
static BST_QR_SCANNER* bst_QR_scanner = NULL;
#endif
static int ver_major = 42;
static int ver_minor = 0;
static const char* strLibVersion = "1.1.4.16b";
static BSTVersion version;
static char buildDateTime[256];
int gBdecodeReturn = 0;
bool  m_bThreadAliving = false;
bool m_nInComputing = false;
pthread_t m_thread;
std::thread uninit_thread;
pthread_cond_t m_cond;
pthread_mutex_t m_mutex;
int m_ErrorCode = 0;

#if defined(ANDROID)|| defined(WIN32)
typedef struct _tInOutputParam
{
    bst_QR_input_t* input = nullptr;
    std::vector<SingleQRInfo> qr_infos;
    int ret = 0;

}InOutParam;

InOutParam gInputParam;
InOutParam gPreInputParam;
int timeOutDecode = 3000;
int current_time = 0;
#endif

bst_QR_input_t _input;
void* thread_bst_QR_scan(void* para);
void* thread_bst_uninit(void* para);
#if __GNUC__ >= 4
#define DLL_PUBLIC __attribute__ ((visibility("default")))
#define DLL_LOCAL  __attribute__ ((visibility("hidden")))
#else
#define DLL_PUBLIC
#define DLL_LOCAL
#endif

int disableAlg()
{
#if defined(ANDROID)
    char algDisable[PROP_VALUE_MAX];
    algDisable[0] = '0';
    __system_property_get("persist.camera.algo.bst.qrcode.sdk.bypass", algDisable);
#else
    char algDisable[128] = { 0 };
    algDisable[0] = '0';
#endif
    if (algDisable[0] == '1')
    {
        return 1;
    }
    return 0;
}

int getBypassNum()
{
    int ret = 0;
    int ignore_num = 0;
#if defined(ANDROID)
    char ignore[PROP_VALUE_MAX];
    memset(ignore, 0, PROP_VALUE_MAX);
    ret = __system_property_get("debug.algo.bst.qrcode.sdk.ignore", ignore);
#else
    char ignore[128] = { 0 };
    ignore[0] = '0';
#endif
    if (ret != 0)
    {
        int intS = atoi(ignore);
        if (intS>=0 || intS<=300)
        {
            ignore_num = intS;
        }
        else
        {
            ignore_num = 0;
        }
    }
    return ignore_num;
}

DLL_PUBLIC
const BSTVersion bstQRGetVersion()
{
    version.versionString = strLibVersion;
    sscanf(version.versionString, "%d.%d.%d.%d", &version.codeBase, &version.major, &version.minor, &version.build);
    memset(buildDateTime, 0, 256);
    sprintf(buildDateTime, "%s %s", __DATE__, __TIME__);
    version.buildDate = buildDateTime;
    version.configVersion = "0.0.0.0";
    LOGE("Lib Version : %s", version.versionString);
    LOGE("Build Date : %s", version.buildDate);
    printf("Lib Version : %s\n", version.versionString);
    printf("Build Date : %s\n", version.buildDate);
    return version;
}

DLL_PUBLIC
int bst_QR_get_version(int* major, int* minor)
{
    bstQRGetVersion();
    *major = ver_major;
    *minor = ver_minor;
    return BST_QR_OK;
}

DLL_PUBLIC
int bst_QR_init(bst_QR_config_t* config)
{
    LOGInit();
    
    LOGI("bst_QR_init\n");
    int major = 0, minor = 0;
    bst_QR_get_version(&major, &minor);
    if (disableAlg()) {
        LOGE("QR init bypass");
        return BST_QR_OK;
    }
    if (isInited == 1)
    {
        LOGE("isInited == 1");
        return BST_QR_INVALID_SCANNER;
    }
#if defined(ANDROID)|| defined(WIN32)
    AutoLock locker(_auto_mutex);
#endif
    if(bst_QR_scanner != NULL)
    {
        return BST_QR_INVALID_SCANNER;
    }
    TimeUtils time_utils;
    time_utils.reset();
#ifdef MOTION_TRACK
    bst_QR_scanner = new BstMultiQRTracker(config->config_path.c_str());
    if (!bst_QR_scanner->mBuildObject)
    {
        LOGI("build object failed!\n");
        return BST_QR_INVALID_ARGUMENT;
    }
    LOGI("nDump = %d\n", bst_QR_scanner->mDump);
#else
    bst_QR_scanner = new BST_QR_SCANNER("../config/config.cfg");
#endif
#if !defined(WIN32)
    if (bst_QR_scanner->mDump)
    {
        mnFrameCount = 0;
        struct timeval dumptime;
        gettimeofday(&dumptime,NULL);
        mlDumpUsec = (1000000ll * dumptime.tv_sec) + dumptime.tv_usec;
        LOGI("Dump time %lld\n", mlDumpUsec);
        memset(dumpFolder, 0, 256);
        sprintf(dumpFolder, "%s/%lld", dumpPath,  mlDumpUsec);
        printf("dump dir :%s", dumpFolder);
        if ((access(dumpPath, 0)) == -1)
        {
            const int tmp_err = mkdir(dumpPath, S_IRWXU | S_IRWXG | S_IROTH | S_IXOTH);
            if (-1 == tmp_err)
            {
                LOGE("Create %s error \n", dumpPath);
            }
        }
        const int dir_err = mkdir(dumpFolder, S_IRWXU | S_IRWXG | S_IROTH | S_IXOTH);
        if(-1 == dir_err) 
        {
            LOGE("Create folder error\n");
        }
    }
#endif

#if defined(ANDROID)|| defined(WIN32)
    bst_QR_scanner->mIgnoreframeNum = getBypassNum();
    _input.image.pimage = nullptr;
    if (bst_QR_scanner->mParams.bUseAsync)
    {
        pthread_cond_init(&m_cond, NULL);
        pthread_mutex_init(&m_mutex, NULL);
        m_bThreadAliving = false;
        gInputParam.input = &_input;
        gInputParam.qr_infos.clear();
        gInputParam.ret = BST_QR_NOT_FOUND;
        pthread_create(&m_thread, NULL, thread_bst_QR_scan, &gInputParam);
#if defined(WIN32)
        Sleep(20);
#else
        usleep(20000);
#endif
        // Make sure the thread startup.
        pthread_mutex_lock(&m_mutex);
        while (m_bThreadAliving == false)
            pthread_cond_wait(&m_cond, &m_mutex);
        pthread_mutex_unlock(&m_mutex);
    }
#endif
    LOGI("qr init %lf ms", time_utils.get_time());
    isInited = 1;
    return BST_QR_OK;
}

DLL_PUBLIC
int bst_QR_scan(bst_QR_input_t* input, bst_QR_output_t* result)
{
    return BST_QR_OK;
}

void bst_QR_dump_input(bst_QR_input_t* input,int retVal, std::vector<bst_QR_output_t> result)
{
    LOGI("Dump dir %s\n", dumpFolder);

    char imageFileName[256];
    string file_name;
    sprintf(imageFileName, "%s/bst_%d_%d_%d_%d", \
        dumpFolder, mnFrameCount, input->image.width, input->image.height, input->image.stride);
    file_name = imageFileName;
    for (int i = 0; i < result.size(); i++)
    {
        file_name += ("_rect"+ std::to_string(i)+"_" + std::to_string(result[i].xmin) + "_" + std::to_string(result[i].ymin) + "_" + std::to_string(result[i].xmax) + "_" + std::to_string(result[i].ymax));
    }
   
    file_name = file_name + ".gray";
    LOGI("Saving image %s\n", file_name.c_str());
    unsigned char* out = new unsigned char[input->image.height * input->image.stride];
    memcpy(out, input->image.pimage, input->image.stride * input->image.height);
    FILE* Dump = fopen(file_name.c_str(), "wb");
    if (Dump != NULL)
    {
        LOGI("Dump Image Width: %d,Height: %d ,Stride : %d \n", input->image.width, input->image.height, input->image.stride);
        fwrite(out, 1, input->image.stride * input->image.height, Dump);
        fclose(Dump);
    }
    delete[]out;
    out = nullptr;
    mnFrameCount++;  
}


#if defined(ANDROID)|| defined(WIN32)
void* thread_bst_QR_scan(void* para)
{
    TimeUtils time_utils;
    InOutParam* pImp = (InOutParam*)para;
    m_bThreadAliving = true;
    pthread_cond_signal(&m_cond);
    for (; m_bThreadAliving;)
    {
        pthread_mutex_lock(&m_mutex);
        std::vector<SingleQRInfo> qr_infos;
        m_nInComputing = false;
        timeOutDecode = 3000;
        current_time = 0;
        gPreInputParam.qr_infos = pImp->qr_infos;
        gPreInputParam.ret = pImp->ret;
        LOGI("decode thread wait input image");
        pthread_cond_wait(&m_cond, &m_mutex);
        unsigned char* data = new unsigned char[pImp->input->image.width * pImp->input->image.height];
        if (!data || !m_bThreadAliving || gBdecodeReturn==1)
        {
            if (data) delete[]data;
            data = nullptr;
            pthread_mutex_unlock(&m_mutex);
            break;
        }
        if (!pImp->input->image.pimage || !m_bThreadAliving || gBdecodeReturn == 1)
        {
            delete[]data;
            data = nullptr;
            pthread_mutex_unlock(&m_mutex);
            break;
        }
        if(pImp->input->image.pimage)
            memcpy(data, pImp->input->image.pimage, pImp->input->image.width * pImp->input->image.height);
        m_nInComputing = true;
        pthread_mutex_unlock(&m_mutex);
        if (!m_bThreadAliving)
        {
            delete[]data;
            data = nullptr;
            break;
        }
        time_utils.reset();
        int retVal = bst_QR_scanner->track(data, pImp->input->image.width, pImp->input->image.height, 1, qr_infos);
        LOGI("process time = %f ms (async track)", time_utils.get_time());
        pImp->qr_infos = qr_infos;
        pImp->ret = retVal;

        delete[]data;
        data = nullptr;
    }
    return (void*)0;
}

template<class T> 
inline T bstClamp(T val, T vMin, T vMax)
{
    T tmp = val < vMin ? vMin : val;
    tmp = tmp > vMax ?  vMax : tmp;
    return tmp;
}

int bst_QR_scan_Async(bst_QR_input_t*  input , std::vector<bst_QR_output_t>& result)
{
    std::vector<SingleQRInfo> qr_infos;
    int ret = 0;
    if (m_bThreadAliving && !m_nInComputing)
    {
        pthread_mutex_lock(&m_mutex);
        gInputParam.input = &_input;
        qr_infos = gInputParam.qr_infos;
        ret = gInputParam.ret;
        if (_input.image.pimage == nullptr || _input.image.width != input->image.width || _input.image.height != input->image.height || _input.image.stride != input->image.stride)
        {
            if (_input.image.pimage != nullptr)
            {
                MemPool::Deallocate(_input.image.pimage);
                _input.image.pimage = nullptr;
            }
            _input.image.pimage = (unsigned char*)MemPool::Allocate(input->image.width * input->image.height);
            _input.image.width = input->image.width;
            _input.image.stride = input->image.stride;
            _input.image.height = input->image.height;
            gInputParam.input = &_input;
        }

        LOGI("ptr = %p,w=%d,h=%d,s=%d src ptr = %p", _input.image.pimage, input->image.width, input->image.height, input->image.stride, input->image.pimage);
        for (int i = 0; i < input->image.height; i++)
            memcpy(_input.image.pimage + i * input->image.width, input->image.pimage + i * input->image.stride, input->image.width);
        pthread_cond_signal(&m_cond);
        pthread_mutex_unlock(&m_mutex);
        ret = gPreInputParam.ret;
        qr_infos = gPreInputParam.qr_infos;
        gInputParam.input = &_input;
        current_time = 0;
    }
    pthread_mutex_lock(&m_mutex);
    qr_infos = gPreInputParam.qr_infos;
    ret = gPreInputParam.ret;
    pthread_mutex_unlock(&m_mutex);
    for (int i = 0; i < (int)qr_infos.size(); i++)
    {
        bst_QR_output_t out;
        out.xmin = int(qr_infos[i].rect.left);
        out.ymin = int(qr_infos[i].rect.top);
        out.xmax = int(qr_infos[i].rect.right);
        out.ymax = int(qr_infos[i].rect.bottom);
        out.QR_location_x = int(qr_infos[i].ct_x);
        out.QR_location_y = int(qr_infos[i].ct_y);
        out.decode_result = qr_infos[i].decode_text;
        out.xmin = bstClamp(out.xmin, 0, input->image.width);
        out.xmax = bstClamp(out.xmax, 0, input->image.width);
        out.ymin = bstClamp(out.ymin, 0, input->image.height);
        out.ymax = bstClamp(out.ymax, 0, input->image.height);
        out.QR_location_x = bstClamp(out.QR_location_x,0, input->image.width);
        out.QR_location_y = bstClamp(out.QR_location_y,0, input->image.height);
        result.push_back(out);
    }
    if (gBdecodeReturn == 1)
    {
        result.clear();
    }
    current_time += 60;
    if (current_time >= timeOutDecode)
    {
        current_time = 0;
        result.clear();
        LOGE("bst_QR_scan_Async decode timer out !!!");
        pthread_mutex_lock(&m_mutex);
        if (_input.image.pimage == nullptr || _input.image.width != input->image.width || _input.image.height != input->image.height || _input.image.stride != input->image.stride)
        {
            if (_input.image.pimage != nullptr)
            {
                MemPool::Deallocate(_input.image.pimage);
                _input.image.pimage = nullptr;
            }
            _input.image.pimage = (unsigned char*)MemPool::Allocate(input->image.width * input->image.height);
            _input.image.width = input->image.width;
            _input.image.stride = input->image.stride;
            _input.image.height = input->image.height;
            gInputParam.input = &_input;
        }

        LOGI("ptr2 = %p,w=%d,h=%d,s=%d src ptr = %p", _input.image.pimage, input->image.width, input->image.height, input->image.stride, input->image.pimage);
        for (int i = 0; i < input->image.height; i++)
            memcpy(_input.image.pimage + i * input->image.width, input->image.pimage + i * input->image.stride, input->image.width);
        pthread_cond_signal(&m_cond);
        pthread_mutex_unlock(&m_mutex);
    }
    for (int i = 0; i < (int)result.size(); i++)
    {
        LOGI("rect[%d]:%d %d %d %d", i, result[i].xmin, result[i].ymin, result[i].xmax, result[i].ymax);
        LOGI("str[%d]:%s", i, result[i].decode_result.c_str());
    }
    return ret;
}
#endif

DLL_PUBLIC
int bst_QR_scan(bst_QR_input_t* input, std::vector<bst_QR_output_t> &result)
{
    if (gBdecodeReturn == 1 || isInited==0)
    {
        result.clear();
        return BST_QR_NOT_FOUND;
    }
    if (disableAlg()) {
        LOGE("QR bst_QR_scan bypass");
        return BST_QR_NOT_FOUND;
    }
    if (bst_QR_scanner == NULL)
    {
        return BST_QR_INVALID_SCANNER;
    }
    if (bst_QR_scanner->mByPass)
    {
        LOGI("QRCode sdk bypass!!");
        return BST_QR_NOT_FOUND;
    }
#if defined(ANDROID)|| defined(WIN32)
    AutoLock locker(_auto_mutex);
#endif

    if (input->image.pimage == nullptr || input->image.stride==0)
    {
        LOGE("error !!! input->image.pimage = nullptr");
        LOGI("w=%d,h=%d,s=%d src ptr = %p", input->image.width, input->image.height, input->image.stride, input->image.pimage);
        return BST_QR_INVALID_SCANNER;
    }
    bst_QR_scanner->mBusy = true;

    result.clear();
#if defined(ANDROID)|| defined(WIN32)
    if (bst_QR_scanner->mIgnoreframeNum > 0)
    {
        LOGI("i %d ...\n", bst_QR_scanner->mIgnoreframeNum);
        bst_QR_scanner->mIgnoreframeNum--;
        return BST_QR_NOT_FOUND;
    }
#endif
    int retVal = BST_QR_OK;
    TimeUtils time_utils;

    time_utils.reset();
    if (bst_QR_scanner->mParams.bUseAsync)
    {
 #if defined(ANDROID)|| defined(WIN32)
        retVal = bst_QR_scan_Async(input, result);
    #endif
    }
    else
    {
        unsigned char* data = (unsigned char*)MemPool::Allocate(input->image.width * input->image.height);
        LOGI("ptr = %p,w=%d,h=%d,s=%d src ptr = %p", data, input->image.width, input->image.height, input->image.stride, input->image.pimage);
        for (int i = 0; i < input->image.height; i++)
            memcpy(data + i * input->image.width, input->image.pimage + i * input->image.stride, input->image.width);

        bst_QR_input_t _input;
        memcpy(&_input, input, sizeof(bst_QR_input_t));
        _input.image.stride = input->image.width;
        _input.image.pimage = data;

#ifdef MOTION_TRACK
       std::vector<SingleQRInfo> qr_infos;
       retVal = bst_QR_scanner->track(_input.image.pimage, _input.image.width, _input.image.height, 1, qr_infos);
       for (int i = 0; i < (int)qr_infos.size(); i++)
       {
           bst_QR_output_t out;
           out.xmin = int(qr_infos[i].rect.left);
           out.ymin = int(qr_infos[i].rect.top);
           out.xmax = int(qr_infos[i].rect.right);
           out.ymax = int(qr_infos[i].rect.bottom);
           out.QR_location_x = int(qr_infos[i].ct_x);
           out.QR_location_y = int(qr_infos[i].ct_y);
           out.decode_result = qr_infos[i].decode_text;
           result.push_back(out);
       }
       if (data)
       {
           LOGE("dealloc ptr = %p", data);
           MemPool::Deallocate(data);
           data = nullptr;
       }
#else
        retVal = bst_QR_scanner->scan(input, result);
#endif
    }
    double process_time = time_utils.get_time();
    LOGI("process time = %f ms (all) \n", process_time);

    if (bst_QR_scanner->mDump)
    {
      //  if (process_time > 0)
        {
            bst_QR_dump_input(input, retVal, result);
        }
    }
    

    if(retVal == BST_QR_NOT_FOUND)
    {
        bst_QR_scanner->mBusy = false;
        return retVal;
    }

    bst_QR_scanner->mBusy = false;
    return retVal;
}

DLL_PUBLIC
int bst_QR_uninit()
{
    LOGI("bst_QR_uninit\n");
    gBdecodeReturn = 1;
    if (disableAlg()) {
        LOGE("QR uninit bypass");
        return BST_QR_OK;
    }
    TimeUtils time_utils_2;
    time_utils_2.reset();
#if defined(ANDROID)|| defined(WIN32)
    AutoLock locker(_auto_mutex);
#endif
    if (bst_QR_scanner == NULL || isInited==0)
    {
        LOGE("qr uninit done");
        gBdecodeReturn = 0;
        LOGE("bst_QR_scanner=nullptr");
        LOGE("isInited  == %d", isInited);
        return BST_QR_INVALID_SCANNER;
    }
    TimeUtils time_utils;
    
    bst_QR_scanner->mParams.bDecodeReturn = 1;
    bst_QR_scanner->mParams_gpu_scanner_.bDecodeReturn = 1;
    bst_QR_scanner->mParams_gpu_scanner64_.bDecodeReturn = 1;
    bst_QR_scanner->mParams_scanner_.bDecodeReturn = 1;
    if (bst_QR_scanner->mParams.bUseAsync)
    {
        m_bThreadAliving = false;
        bst_QR_scanner->mParams.decodeTimerOut = 0;
#if defined(WIN32)
        Sleep(20);
#else
        usleep(20000);
#endif      
        time_utils.reset();
        pthread_mutex_lock(&m_mutex);
        pthread_cond_signal(&m_cond);

        pthread_mutex_unlock(&m_mutex);

        pthread_join(m_thread, NULL);

        pthread_mutex_destroy(&m_mutex);
        pthread_cond_destroy(&m_cond);
        LOGI("pthread_join %lf", time_utils.get_time());
        if (_input.image.pimage)
            MemPool::Deallocate(_input.image.pimage);
        _input.image.pimage = nullptr;
        m_nInComputing = false;
    }
    if(_input.image.pimage)
        MemPool::Deallocate(_input.image.pimage);
    _input.image.pimage = nullptr;
    time_utils.reset();
    while(bst_QR_scanner->mBusy)
        Sleep(10);
    LOGI("bst_QR_scanner busy %lf", time_utils.get_time());
    time_utils.reset();
    delete bst_QR_scanner;
    bst_QR_scanner = NULL;
    LOGI("bst_QR_scanner u %lf", time_utils.get_time());
    LOGE("uninit u %lf", time_utils_2.get_time());
    LOGE("qr uninit done");
    gBdecodeReturn = 0;
    isInited = 0;
    return BST_QR_OK;
}

