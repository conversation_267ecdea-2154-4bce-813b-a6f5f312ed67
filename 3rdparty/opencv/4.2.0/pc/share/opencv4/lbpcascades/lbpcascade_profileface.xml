<?xml version="1.0"?>
<!--
    This is 20x34 detector of profile faces using LBP features.
    It was created by <PERSON><PERSON><PERSON> during GSoC 2012.
    Note that the detector only detects faces rotated to the right,
    so you may want to run it on the original and on
    the flipped image to detect different profile faces.
-->
<opencv_storage>
<cascade>
  <stageType>BOOST</stageType>
  <featureType>LBP</featureType>
  <height>34</height>
  <width>20</width>
  <stageParams>
    <boostType>GAB</boostType>
    <minHitRate>9.9500000476837158e-001</minHitRate>
    <maxFalseAlarm>3.0000001192092896e-001</maxFalseAlarm>
    <weightTrimRate>9.4999999999999996e-001</weightTrimRate>
    <maxDepth>1</maxDepth>
    <maxWeakCount>100</maxWeakCount></stageParams>
  <featureParams>
    <maxCatCount>256</maxCatCount>
    <featSize>1</featSize></featureParams>
  <stageNum>16</stageNum>
  <stages>
    <!-- stage 0 -->
    <_>
      <maxWeakCount>4</maxWeakCount>
      <stageThreshold>-5.9480339288711548e-001</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 114 -2360321 -82228595 -771518211 -713436773
            -1060447799 -810385271 -2004135683 -2566104</internalNodes>
          <leafValues>
            -8.0942183732986450e-001 5.9530025720596313e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 54 -649134608 -1060077114 1375916272 -719981432
            1073801352 33024 281198795 -5246465</internalNodes>
          <leafValues>
            -7.7979278564453125e-001 5.4052764177322388e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 12 -960266913 -495857599 -1068498864 -867970987
            457398579 -1174173695 1749041235 1849162079</internalNodes>
          <leafValues>
            -8.0028575658798218e-001 5.0435048341751099e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 120 -1228145793 -807247727 18059735 -138644520
            998980043 -41250583 673112549 -1930366540</internalNodes>
          <leafValues>
            -7.7902388572692871e-001 4.9006074666976929e-001</leafValues></_></weakClassifiers></_>
    <!-- stage 1 -->
    <_>
      <maxWeakCount>6</maxWeakCount>
      <stageThreshold>-5.4879629611968994e-001</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 6 -254346881 -746143606 -1039596583 1963430479
            -263790449 -1073545213 698505999 -1349357</internalNodes>
          <leafValues>
            -6.6315788030624390e-001 6.0000002384185791e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 112 -134225985 -684228389 -988213089 -684716007
            -1966960899 -896630615 152815840 -864497420</internalNodes>
          <leafValues>
            -7.0195454359054565e-001 5.8843690156936646e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 53 -35923461 520818827 -1862167847 856916291 68141197
            2072530978 304306417 526079163</internalNodes>
          <leafValues>
            -6.4593964815139771e-001 5.7274609804153442e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 101 -2097665 -1781432163 588321018 -1677405808
            -1968469982 -1450147831 -1467632684 -593693808</internalNodes>
          <leafValues>
            -7.2959578037261963e-001 4.9470889568328857e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 79 -205847273 -1088716541 285266431 1393693056
            293931101 -1634205688 -452263692 -111136684</internalNodes>
          <leafValues>
            -7.0331865549087524e-001 5.2564400434494019e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 126 579801457 -670613495 -1065269989 -117095565
            -1295163359 -779534335 -1744220101 -1355860</internalNodes>
          <leafValues>
            -7.5121974945068359e-001 4.5217981934547424e-001</leafValues></_></weakClassifiers></_>
    <!-- stage 2 -->
    <_>
      <maxWeakCount>4</maxWeakCount>
      <stageThreshold>-4.3886357545852661e-001</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 20 -346563793 1217040543 -1324639677 206303367
            -260894653 1165249072 1359168335 1652518863</internalNodes>
          <leafValues>
            -8.3054625988006592e-001 5.5417186021804810e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 69 -925898078 -917290147 -2147368790 -1995968378
            1203961890 1765910571 789128481 -4201473</internalNodes>
          <leafValues>
            -7.5220447778701782e-001 6.1290657520294189e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 7 -425790473 -368916470 -1065172848 -1877712894
            -1067360254 -847191997 1342400518 -680037517</internalNodes>
          <leafValues>
            -7.8469508886337280e-001 5.9731280803680420e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 5 -260315918 -1567751150 -805289977 1721229843
            1644296976 1954742530 824530213 -8392601</internalNodes>
          <leafValues>
            -7.3686408996582031e-001 5.6347119808197021e-001</leafValues></_></weakClassifiers></_>
    <!-- stage 3 -->
    <_>
      <maxWeakCount>6</maxWeakCount>
      <stageThreshold>-4.6629825234413147e-001</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 111 -67634177 -72175593 -246181185 -144772036
            -1465917455 -1426934837 -345249307 -539041852</internalNodes>
          <leafValues>
            -7.1692305803298950e-001 5.5034482479095459e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 47 -1048705 -96415158 -1996126927 67301684 -659873481
            1800863745 -402143413 1647570815</internalNodes>
          <leafValues>
            -7.6134461164474487e-001 4.7370144724845886e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 119 1905247351 -1111526689 1426654203 -116427277
            1731664419 -81052249 1051905317 -1628448513</internalNodes>
          <leafValues>
            -5.9460461139678955e-001 6.1952447891235352e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 2 578486263 -2115313530 -788268733 -1122507629
            -343408719 2127242147 -85406399 -37295</internalNodes>
          <leafValues>
            -6.0801470279693604e-001 5.8719038963317871e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 127 -1147176065 52139167 21156225 -540503783 -771529299
            -33325024 -671045243 -1913073360</internalNodes>
          <leafValues>
            -7.4383884668350220e-001 5.1643568277359009e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 93 -319091633 -58633529 1166906391 1854443149
            1267403009 -1198817246 1208634960 -35661669</internalNodes>
          <leafValues>
            -6.8595260381698608e-001 5.5931246280670166e-001</leafValues></_></weakClassifiers></_>
    <!-- stage 4 -->
    <_>
      <maxWeakCount>8</maxWeakCount>
      <stageThreshold>-6.0948312282562256e-001</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 102 -747899393 -543522675 545333467 -34230241
            -1572626245 -17790840 -1182162691 -1078427420</internalNodes>
          <leafValues>
            -6.0826772451400757e-001 4.6491229534149170e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 38 -103812609 503024467 -2121908081 722834075
            1375757518 2022089353 197321677 2077719203</internalNodes>
          <leafValues>
            -6.2948691844940186e-001 4.8044654726982117e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 19 -774429826 -607461158 1158791644 -971587409
            -1732167611 2015560010 -1278549257 -159911361</internalNodes>
          <leafValues>
            -5.9694272279739380e-001 4.7999730706214905e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 122 735837495 -875325281 152208339 -741020481
            -1471817477 -1165246433 -1450830159 -1696546384</internalNodes>
          <leafValues>
            -6.4947181940078735e-001 4.2661586403846741e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 104 -629063145 -49708711 50692231 1973945160 157637120
            2056259593 1771350547 -78911181</internalNodes>
          <leafValues>
            -6.2496536970138550e-001 4.4524449110031128e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 67 -74189973 -803307502 688005268 1600057378 -131870050
            -1600503318 571446250 -386668002</internalNodes>
          <leafValues>
            -5.5046343803405762e-001 5.6090569496154785e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 81 586347861 -2071051852 -250078020 -1455374076
            546287843 1216708619 -1853707673 -35130912</internalNodes>
          <leafValues>
            -6.3877129554748535e-001 4.7911971807479858e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 22 -1436568057 1555188001 164315 2084672259 1809869105
            1132626050 1223430266 -596124761</internalNodes>
          <leafValues>
            -6.4428490400314331e-001 4.7921949625015259e-001</leafValues></_></weakClassifiers></_>
    <!-- stage 5 -->
    <_>
      <maxWeakCount>8</maxWeakCount>
      <stageThreshold>-5.4387503862380981e-001</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 44 -783680003 -771883143 -302055943 -5898247 -253370375
            -1996628131 1625947386 -2004157446</internalNodes>
          <leafValues>
            -5.2870607376098633e-001 5.9474670886993408e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 49 -586034977 -41205679 352424062 -163145456 151126042
            -1171652503 1208036058 -9019322</internalNodes>
          <leafValues>
            -5.6763833761215210e-001 4.8789894580841064e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 39 1402589836 1363509256 103583 823365787 -1861443377
            412131360 539718283 1002160350</internalNodes>
          <leafValues>
            -5.9899079799652100e-001 4.9562713503837585e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 113 -783429121 -1559215981 286355953 -794820602
            461510679 -611662910 -2136237584 -96429424</internalNodes>
          <leafValues>
            -6.3842493295669556e-001 4.3330931663513184e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 99 -1365839532 -1291265163 1091604493 965968977
            147472779 -1466925055 -2013090821 -1410703205</internalNodes>
          <leafValues>
            -5.8633142709732056e-001 5.0152444839477539e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 26 1846469631 -788479850 268796195 -754872317
            1630603451 -896532480 1208092751 -72652777</internalNodes>
          <leafValues>
            -5.9243172407150269e-001 4.7917708754539490e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 85 -715395062 -113037167 1342198133 -552594287
            411123713 11059209 -2012512153 -877809205</internalNodes>
          <leafValues>
            -6.9079184532165527e-001 4.2610234022140503e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 100 -526391817 -921022135 -1593630697 671093393
            -2004270453 -1962835840 -1870413655 -1597095644</internalNodes>
          <leafValues>
            -6.5030521154403687e-001 4.4748127460479736e-001</leafValues></_></weakClassifiers></_>
    <!-- stage 6 -->
    <_>
      <maxWeakCount>8</maxWeakCount>
      <stageThreshold>-6.3195121288299561e-001</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 109 -674761315 -581726065 352407899 -83717423
            -660870145 -1165915966 -326837763 -927182608</internalNodes>
          <leafValues>
            -7.3185729980468750e-001 3.3258172869682312e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 97 860755579 -707063662 1361264863 1065505299
            -1022866435 -1776123776 -1865661700 -1615196136</internalNodes>
          <leafValues>
            -6.1147916316986084e-001 3.7205791473388672e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 15 -678435969 -106962866 268652561 -826396597
            -802066313 1931092070 1208025439 1211582847</internalNodes>
          <leafValues>
            -6.8679082393646240e-001 3.6285603046417236e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 86 -1573074550 -2080337595 299991 110482176 268552379
            -310373944 596185787 -1428952165</internalNodes>
          <leafValues>
            -6.4654982089996338e-001 4.1456297039985657e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 30 -72637790 -1258143612 1342937104 -544352374
            -1046875163 -121076606 -786059128 -71702400</internalNodes>
          <leafValues>
            -5.2772462368011475e-001 4.9787566065788269e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 89 -683288417 -218031996 33734999 -16115386 -2013259561
            -2008907509 -1978533232 -352342880</internalNodes>
          <leafValues>
            -5.2718847990036011e-001 5.2839303016662598e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 10 -268764033 -1078984772 -65537 -281182212 -524291 -1
            -8489090 -4227265</internalNodes>
          <leafValues>
            -5.0513482093811035e-001 5.8522778749465942e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 82 -570445845 784662143 -268435661 -1292701712
            -436263043 -1367507075 -671091243 -751108132</internalNodes>
          <leafValues>
            -5.2438414096832275e-001 5.4709094762802124e-001</leafValues></_></weakClassifiers></_>
    <!-- stage 7 -->
    <_>
      <maxWeakCount>8</maxWeakCount>
      <stageThreshold>-5.9874147176742554e-001</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 27 -721421649 -1001940437 2300046 -720004829 -792686333
            1908900882 -160055232 -134763633</internalNodes>
          <leafValues>
            -5.7692307233810425e-001 3.7921348214149475e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 78 -1764279809 -1755824061 1937871313 -42069793
            -1241158993 -1196293937 -1576828673 -70371296</internalNodes>
          <leafValues>
            -4.7039109468460083e-001 4.8607903718948364e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 29 -795875130 432079111 285457049 -620658641 -780072971
            1158283432 -226254016 1839935243</internalNodes>
          <leafValues>
            -6.2938809394836426e-001 4.1353255510330200e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 33 -37236389 1654493543 202129823 1788182787
            -1186162321 1912913933 -122942838 1968176815</internalNodes>
          <leafValues>
            -5.9031385183334351e-001 4.1488575935363770e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 88 1903888863 -286828472 -2125248034 -623115882
            -268301806 -894826357 -2046633148 -696873056</internalNodes>
          <leafValues>
            -6.3875061273574829e-001 4.0209171175956726e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 123 -87223501 -1873424249 -1878929092 -586710990
            -643825151 -1039040192 -285122488 -264093</internalNodes>
          <leafValues>
            -5.4196298122406006e-001 4.5856228470802307e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 52 -780030833 1363755203 -385150929 25502018 1214818435
            -1020786271 -1870036478 1200354241</internalNodes>
          <leafValues>
            -5.2826374769210815e-001 5.3351372480392456e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 84 -1724706499 -184429355 620844509 -179010317
            -1610327896 -341801844 -1190328066 1755915264</internalNodes>
          <leafValues>
            -5.7672232389450073e-001 4.4138705730438232e-001</leafValues></_></weakClassifiers></_>
    <!-- stage 8 -->
    <_>
      <maxWeakCount>9</maxWeakCount>
      <stageThreshold>-5.4533123970031738e-001</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 48 -254347649 -565919658 1079050328 1090502875
            1895985446 2013437961 -916419445 -53481573</internalNodes>
          <leafValues>
            -5.8105266094207764e-001 3.3599999547004700e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 65 2030928895 1438877010 1124143121 258207763
            1361199276 1527410834 2072519624 1004267991</internalNodes>
          <leafValues>
            -5.9629368782043457e-001 3.6112698912620544e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 45 -247204964 -242712316 54544644 892459288 1888023456
            -2138044280 -802615208 13199500</internalNodes>
          <leafValues>
            -6.5467655658721924e-001 3.0486112833023071e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 3 -430509345 -1865653973 554091143 -1069121312
            1091180718 50577994 -1031731181 -211321225</internalNodes>
          <leafValues>
            -5.8759629726409912e-001 3.9526104927062988e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 106 -741412064 -255623164 1090945848 -1687760764
            42428760 -1064762741 -1861683196 -81029101</internalNodes>
          <leafValues>
            -6.5875691175460815e-001 3.4154877066612244e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 128 -464010241 762112 285299147 -589082223 1373135017
            -2138955645 1057005712 -526876236</internalNodes>
          <leafValues>
            -6.5968728065490723e-001 3.3614772558212280e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 80 -666744719 -635780797 33637339 -887860848
            -1073532217 -108904320 440608996 -1100753973</internalNodes>
          <leafValues>
            -5.0520354509353638e-001 4.4810971617698669e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 28 -1580738774 -1506653838 302055688 -721223615
            1427604224 -1566332144 1078565791 -558431977</internalNodes>
          <leafValues>
            -5.5560898780822754e-001 4.3426483869552612e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 103 957796629 538644536 352997725 80838797 453085387
            -1165492198 285346042 1487077737</internalNodes>
          <leafValues>
            -5.5915868282318115e-001 4.0778505802154541e-001</leafValues></_></weakClassifiers></_>
    <!-- stage 9 -->
    <_>
      <maxWeakCount>9</maxWeakCount>
      <stageThreshold>-6.7299038171768188e-001</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 0 -882973185 -620584737 279035921 -673986422
            -1568464349 -2105466877 1468391879 -38825</internalNodes>
          <leafValues>
            -5.7544225454330444e-001 3.4235453605651855e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 90 -1820101795 -1336770299 285245717 -57216724
            -502134548 -1425341984 -1475618680 -1195896480</internalNodes>
          <leafValues>
            -6.6810834407806396e-001 2.7653357386589050e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 9 -100197449 -457893579 200991 1964749325 -754875920
            1897044675 1669843618 -70792821</internalNodes>
          <leafValues>
            -4.9064287543296814e-001 4.3120625615119934e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 117 -792114173 -544111547 537001999 2034569362
            -1065213888 1630052634 -1450583484 -532405661</internalNodes>
          <leafValues>
            -6.4218991994857788e-001 3.6113587021827698e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 107 -1564241697 -1429683702 -2062974587 -1900539448
            -1040078205 -394262006 -188628336 -390485984</internalNodes>
          <leafValues>
            -5.9181970357894897e-001 3.5756480693817139e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 4 1893434787 -1945108258 82458 -318734161 -939347837
            684196040 1078496869 2133023515</internalNodes>
          <leafValues>
            -6.1955446004867554e-001 3.4674292802810669e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 31 -196247204 1964277780 -1810886012 21827851
            -364280891 -1062338560 -536741128 -362562814</internalNodes>
          <leafValues>
            -5.2849757671356201e-001 4.1380330920219421e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 61 -1929140897 353472529 -721412674 -1228123782
            -392951233 -1442693096 672800826 -232914898</internalNodes>
          <leafValues>
            -5.7934975624084473e-001 3.9208874106407166e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 72 -1004361296 -1069243858 268710018 1393598601
            213956864 417530145 -912735606 1327495627</internalNodes>
          <leafValues>
            -7.5585323572158813e-001 2.6728668808937073e-001</leafValues></_></weakClassifiers></_>
    <!-- stage 10 -->
    <_>
      <maxWeakCount>9</maxWeakCount>
      <stageThreshold>-7.1303337812423706e-001</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 23 -557797393 1524138462 277074064 -737259367
            -1878818960 -81600384 -1740109301 -59267505</internalNodes>
          <leafValues>
            -6.7397260665893555e-001 1.9793814420700073e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 42 -1222377543 960610456 -2013138684 -989277927
            -1010064731 -802979830 -645806439 -885143219</internalNodes>
          <leafValues>
            -4.5935314893722534e-001 4.1904711723327637e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 124 -783292542 -728791016 1342570700 1481418249
            1258825942 -1580563964 -1178136688 -272306640</internalNodes>
          <leafValues>
            -6.3012123107910156e-001 2.9463621973991394e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 46 1369396573 -188563225 22085642 -1005861886
            2023260232 -1123842045 -2146991925 1245170171</internalNodes>
          <leafValues>
            -5.2092707157135010e-001 3.9743596315383911e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 64 1540188400 1976259599 -805025279 864127692 544944
            1484935304 -2147056504 1002584738</internalNodes>
          <leafValues>
            -6.5315401554107666e-001 3.1758561730384827e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 77 -188606981 -1873391210 16842830 -117157654
            -1576842600 -1454767992 -518835576 -1625272280</internalNodes>
          <leafValues>
            -5.8580338954925537e-001 3.4936144948005676e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 18 -473497030 -477572088 16842905 -12164860 184698994
            1350566019 -2143169323 1405313030</internalNodes>
          <leafValues>
            -6.0962837934494019e-001 3.0044576525688171e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 92 -528022006 -611028904 1075937757 -577660920
            1073809492 -1341620207 -1475846395 -162412743</internalNodes>
          <leafValues>
            -6.6547930240631104e-001 3.1993752717971802e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 116 -2062347245 35311783 406966429 -640155632
            -1904205761 -2012610494 399245455 -937752211</internalNodes>
          <leafValues>
            -4.8515367507934570e-001 4.3642494082450867e-001</leafValues></_></weakClassifiers></_>
    <!-- stage 11 -->
    <_>
      <maxWeakCount>10</maxWeakCount>
      <stageThreshold>-1.1831332445144653e+000</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 115 -912525479 -2146793066 247327 -554139184 320582141
            -1442774971 1552517769 -1464330096</internalNodes>
          <leafValues>
            -7.2892564535140991e-001 1.2876711785793304e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 41 -182757566 -683667118 268566545 -540408959
            1547915506 2014497074 1817806103 -549486525</internalNodes>
          <leafValues>
            -5.6024330854415894e-001 2.8734233975410461e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 13 -1396013057 -175218480 536903951 -35946104 -92067077
            956498056 -200474487 1331907188</internalNodes>
          <leafValues>
            -5.5237007141113281e-001 3.2844060659408569e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 17 2110443855 1547702666 -1874853670 1083212172
            -2004008413 -498614008 572624451 1179093527</internalNodes>
          <leafValues>
            -7.2481799125671387e-001 2.6627025008201599e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 43 -1751428966 -1626324992 -1073540847 -783806124
            -2146909454 -913440767 -2138941303 -558233160</internalNodes>
          <leafValues>
            -4.4304186105728149e-001 4.1505634784698486e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 37 -576405461 -1625709950 1627439763 1116373274
            1622902452 1107834529 975868423 2074176171</internalNodes>
          <leafValues>
            -5.6509882211685181e-001 3.5433205962181091e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 118 1171205664 1426522307 49281 563122240 -791985520
            -930869245 -364148081 -590624140</internalNodes>
          <leafValues>
            -5.6250953674316406e-001 3.3341854810714722e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 76 1162033968 1180991656 16859165 230787289 -2104786299
            -1819967351 1118240928 -343561865</internalNodes>
          <leafValues>
            -4.7331553697586060e-001 4.1576251387596130e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 110 -2147085315 -1228897088 -2146839339 -1751314339
            -531605907 -393183232 1804153563 -1399324416</internalNodes>
          <leafValues>
            -5.8979070186614990e-001 3.7525305151939392e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 55 1581887865 999817729 151311688 331546624 -991625824
            -938834941 1837335184 852075394</internalNodes>
          <leafValues>
            -5.4071021080017090e-001 4.0077716112136841e-001</leafValues></_></weakClassifiers></_>
    <!-- stage 12 -->
    <_>
      <maxWeakCount>10</maxWeakCount>
      <stageThreshold>-6.4480733871459961e-001</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 16 -510660401 -884555766 272896026 -12189566
            -1685363509 -662568805 1073840823 -545105785</internalNodes>
          <leafValues>
            -5.3361344337463379e-001 2.7807486057281494e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 48 -557408354 2115155922 -2130669353 1616707591
            693193240 -1569554175 -1743918878 1983596555</internalNodes>
          <leafValues>
            -5.3364741802215576e-001 3.1411096453666687e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 108 -413278733 83935516 536961502 1452278484
            -2004277212 -391683967 -1426466672 -85395040</internalNodes>
          <leafValues>
            -7.4530494213104248e-001 2.3025059700012207e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 32 -938623022 1469386887 822151432 421593370
            -1433793568 -1602191360 -527916919 680112651</internalNodes>
          <leafValues>
            -4.6078306436538696e-001 4.0021440386772156e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 50 1619785226 -1004367410 1417725137 126732357
            148062614 -625983352 -712398335 -412918226</internalNodes>
          <leafValues>
            -4.9818846583366394e-001 3.6678382754325867e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 24 -1064322531 1351938204 196691 -561840073 -1978859471
            -649944954 -2003664885 -1172094197</internalNodes>
          <leafValues>
            -4.7309580445289612e-001 4.2868506908416748e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 96 -1878961904 1360035888 -1073721317 -1051487863
            -431841087 1628112896 -2112640640 -1829440828</internalNodes>
          <leafValues>
            -6.9250243902206421e-001 2.8783574700355530e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 62 67496095 391741589 -2146154237 96245592 -893992548
            982687872 571488264 278906307</internalNodes>
          <leafValues>
            -6.4613574743270874e-001 3.0145862698554993e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 73 -415771792 1208487966 339825796 1792117580
            1128517807 144965669 -536376816 732856538</internalNodes>
          <leafValues>
            -6.9449120759963989e-001 3.0338683724403381e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 40 -1991530440 324215457 -2080275930 -1857940798
            1342685625 721420800 1250592988 1493903457</internalNodes>
          <leafValues>
            -7.0043331384658813e-001 2.5916099548339844e-001</leafValues></_></weakClassifiers></_>
    <!-- stage 13 -->
    <_>
      <maxWeakCount>10</maxWeakCount>
      <stageThreshold>-6.0248321294784546e-001</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 21 -16537745 2114438797 1409323561 1691064397
            -207434939 822260754 -384857461 2031088579</internalNodes>
          <leafValues>
            -6.1256545782089233e-001 1.7948718369007111e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1 -95427858 67117166 -1308426467 -1962693439 601886855
            924320187 1661215701 2078945158</internalNodes>
          <leafValues>
            -6.8756872415542603e-001 2.2317354381084442e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 121 -1853361185 -619857007 16793601 -184516476
            -1422775873 -488996831 1476610285 -926297672</internalNodes>
          <leafValues>
            -5.2260422706604004e-001 3.2479336857795715e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 105 -267171326 1436635177 1937772829 -2092859315
            -769638067 -2122268534 1502103583 -18894227</internalNodes>
          <leafValues>
            -5.2588832378387451e-001 3.4061828255653381e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 83 1880187281 -1862250368 303299 960921986 -2002701917
            -1593343958 -334888263 1058018448</internalNodes>
          <leafValues>
            -6.9037044048309326e-001 2.7262538671493530e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 34 -2125487365 1347551377 -1861970752 1368654274
            -1064675233 436275211 327448684 2068015115</internalNodes>
          <leafValues>
            -5.3338903188705444e-001 3.2425448298454285e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 36 1192659162 235536712 1078002258 428089414
            -2138651204 -1937242101 507742421 1932739127</internalNodes>
          <leafValues>
            -6.4654779434204102e-001 3.0722403526306152e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 14 -805047416 -1962622822 -2013265442 2030239751
            1082134810 1744963592 -1836871485 -249326965</internalNodes>
          <leafValues>
            -5.7250964641571045e-001 3.1499111652374268e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 75 -650653297 170234379 -2063527695 448823424
            -2139088862 319586315 -2067685344 -1347692410</internalNodes>
          <leafValues>
            -5.4618871212005615e-001 3.8171616196632385e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 56 -168821125 -1107300354 -536871052 -1125515426
            -1795721360 -1672085508 1845358040 -2114327569</internalNodes>
          <leafValues>
            -4.2669427394866943e-001 5.0532561540603638e-001</leafValues></_></weakClassifiers></_>
    <!-- stage 14 -->
    <_>
      <maxWeakCount>11</maxWeakCount>
      <stageThreshold>-1.1912760734558105e+000</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 11 -1043414305 -1735900650 268517385 -1137929054
            -1048411462 -2011152253 -1957405841 -497557425</internalNodes>
          <leafValues>
            -5.7042253017425537e-001 2.1933962404727936e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 71 -233469310 1360073157 376971 626087057 -1180588024
            -1191067261 -1474310132 830601690</internalNodes>
          <leafValues>
            -5.3927713632583618e-001 2.9026004672050476e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 35 -1599643389 42074270 -1811918838 -949960625
            1564707361 289538187 1204527649 -112006873</internalNodes>
          <leafValues>
            -6.0980087518692017e-001 2.8851604461669922e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 59 585529126 -1100070936 -1342177537 833961983
            1306961797 1986559992 -810088568 -1082149201</internalNodes>
          <leafValues>
            -3.2345715165138245e-001 5.5635309219360352e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 95 1107806555 2030223765 17039707 -1224163308
            -1073053535 -1291837432 822618633 -121972608</internalNodes>
          <leafValues>
            -6.5054124593734741e-001 3.1912675499916077e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 51 -171583461 -1660890605 268504396 453157697
            -1065215606 -1740602879 1824636801 1940062923</internalNodes>
          <leafValues>
            -4.7275745868682861e-001 4.2362514138221741e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 87 -799546379 -2097769968 293605405 -21571376 285294733
            136347650 -930405536 -69420863</internalNodes>
          <leafValues>
            -5.5549502372741699e-001 3.3842340111732483e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 60 -594509036 -267114166 35413 -1052598126 545325639
            -1207959408 -1073643381 682827807</internalNodes>
          <leafValues>
            -5.4805672168731689e-001 3.7224516272544861e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 63 1513710022 194882313 1109000450 28010496 -601835264
            -645791614 -1041880446 1561822180</internalNodes>
          <leafValues>
            -5.3384119272232056e-001 3.7635508179664612e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 125 -754581391 -246595569 -2113336948 -1855323709
            1090531337 -931133310 950984 -3971805</internalNodes>
          <leafValues>
            -5.2334308624267578e-001 4.0167775750160217e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 58 -361268680 662383988 2147483638 -209756289
            -1375932428 -1895890954 -1744855042 -1142215109</internalNodes>
          <leafValues>
            -3.4343415498733521e-001 6.1590969562530518e-001</leafValues></_></weakClassifiers></_>
    <!-- stage 15 -->
    <_>
      <maxWeakCount>10</maxWeakCount>
      <stageThreshold>-7.7425497770309448e-001</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 66 -716447302 -602037376 1090519043 -150261760
            342934202 -2034138749 1141152394 -351301493</internalNodes>
          <leafValues>
            -4.8867926001548767e-001 3.4062498807907104e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 98 -2071985592 -700120831 1078417460 672719121
            1082264136 -209075063 -1438988203 -1465205245</internalNodes>
          <leafValues>
            -7.1539443731307983e-001 2.4058867990970612e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 74 872558624 331821072 1610649929 -1181384552
            -2130081587 -92209146 -612134248 -1199562344</internalNodes>
          <leafValues>
            -4.4142067432403564e-001 3.7935256958007813e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 68 -791554721 -737771072 2425605 740044819 1208549387
            973897998 1124108962 802102203</internalNodes>
          <leafValues>
            -4.6558478474617004e-001 4.2193859815597534e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 8 1893114270 -1013792636 360523 -586362838 -1073151001
            -2146917824 -2104934391 -875596965</internalNodes>
          <leafValues>
            -5.0676107406616211e-001 3.5864940285682678e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 91 574816266 -2011773950 1476495634 580227538
            -2146781128 -2147448830 1901535891 -692616573</internalNodes>
          <leafValues>
            -6.1020326614379883e-001 3.0061775445938110e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 70 2125429880 2080309246 -285282561 2142961407
            -1259516274 1073741823 754945025 867497448</internalNodes>
          <leafValues>
            -4.3854746222496033e-001 4.7815895080566406e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 94 -1727736509 -1979678624 285229334 1115689064
            537927788 -1207402368 1098914016 -91503488</internalNodes>
          <leafValues>
            -6.8697202205657959e-001 3.5183742642402649e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 57 -528465144 -707035113 -1048575869 1372127361 8651416
            -526909310 -1845360374 -1451016182</internalNodes>
          <leafValues>
            -4.5901125669479370e-001 4.5875525474548340e-001</leafValues></_>
        <_>
          <internalNodes>
            0 -1 25 -2076984798 -533130869 -1060954112 1639977472
            828440586 1792508680 -1693988801 -13285232</internalNodes>
          <leafValues>
            -4.8493441939353943e-001 4.3403539061546326e-001</leafValues></_></weakClassifiers></_></stages>
  <features>
    <_>
      <rect>
        0 1 1 9</rect></_>
    <_>
      <rect>
        0 1 4 7</rect></_>
    <_>
      <rect>
        0 2 2 6</rect></_>
    <_>
      <rect>
        0 2 2 10</rect></_>
    <_>
      <rect>
        0 2 3 4</rect></_>
    <_>
      <rect>
        0 3 3 8</rect></_>
    <_>
      <rect>
        0 4 1 8</rect></_>
    <_>
      <rect>
        0 5 2 9</rect></_>
    <_>
      <rect>
        0 7 1 8</rect></_>
    <_>
      <rect>
        0 7 5 7</rect></_>
    <_>
      <rect>
        0 9 1 5</rect></_>
    <_>
      <rect>
        0 9 2 6</rect></_>
    <_>
      <rect>
        0 10 3 7</rect></_>
    <_>
      <rect>
        0 11 1 3</rect></_>
    <_>
      <rect>
        0 12 2 1</rect></_>
    <_>
      <rect>
        0 13 3 7</rect></_>
    <_>
      <rect>
        0 14 1 1</rect></_>
    <_>
      <rect>
        0 14 3 4</rect></_>
    <_>
      <rect>
        0 16 1 1</rect></_>
    <_>
      <rect>
        0 19 3 5</rect></_>
    <_>
      <rect>
        0 20 3 4</rect></_>
    <_>
      <rect>
        0 21 3 4</rect></_>
    <_>
      <rect>
        0 22 2 4</rect></_>
    <_>
      <rect>
        0 25 3 3</rect></_>
    <_>
      <rect>
        0 25 4 3</rect></_>
    <_>
      <rect>
        1 0 5 10</rect></_>
    <_>
      <rect>
        1 2 1 9</rect></_>
    <_>
      <rect>
        1 4 4 8</rect></_>
    <_>
      <rect>
        1 4 5 9</rect></_>
    <_>
      <rect>
        1 6 3 5</rect></_>
    <_>
      <rect>
        1 9 2 3</rect></_>
    <_>
      <rect>
        1 11 2 4</rect></_>
    <_>
      <rect>
        1 15 3 2</rect></_>
    <_>
      <rect>
        1 20 3 3</rect></_>
    <_>
      <rect>
        1 28 2 2</rect></_>
    <_>
      <rect>
        2 0 2 3</rect></_>
    <_>
      <rect>
        2 0 3 5</rect></_>
    <_>
      <rect>
        2 0 4 8</rect></_>
    <_>
      <rect>
        2 3 4 5</rect></_>
    <_>
      <rect>
        2 4 5 5</rect></_>
    <_>
      <rect>
        2 5 2 5</rect></_>
    <_>
      <rect>
        2 7 5 9</rect></_>
    <_>
      <rect>
        2 8 1 3</rect></_>
    <_>
      <rect>
        2 12 1 2</rect></_>
    <_>
      <rect>
        2 13 3 3</rect></_>
    <_>
      <rect>
        2 14 2 2</rect></_>
    <_>
      <rect>
        2 16 3 5</rect></_>
    <_>
      <rect>
        2 18 3 5</rect></_>
    <_>
      <rect>
        2 22 2 4</rect></_>
    <_>
      <rect>
        2 31 3 1</rect></_>
    <_>
      <rect>
        3 0 2 3</rect></_>
    <_>
      <rect>
        3 1 3 5</rect></_>
    <_>
      <rect>
        3 1 3 8</rect></_>
    <_>
      <rect>
        3 2 3 6</rect></_>
    <_>
      <rect>
        3 8 4 6</rect></_>
    <_>
      <rect>
        3 10 2 4</rect></_>
    <_>
      <rect>
        3 14 2 2</rect></_>
    <_>
      <rect>
        3 16 1 1</rect></_>
    <_>
      <rect>
        3 18 1 1</rect></_>
    <_>
      <rect>
        3 19 1 1</rect></_>
    <_>
      <rect>
        3 19 1 2</rect></_>
    <_>
      <rect>
        3 31 2 1</rect></_>
    <_>
      <rect>
        4 4 4 4</rect></_>
    <_>
      <rect>
        4 5 2 7</rect></_>
    <_>
      <rect>
        4 6 2 4</rect></_>
    <_>
      <rect>
        4 6 3 4</rect></_>
    <_>
      <rect>
        4 7 2 8</rect></_>
    <_>
      <rect>
        4 12 3 5</rect></_>
    <_>
      <rect>
        4 19 2 3</rect></_>
    <_>
      <rect>
        5 0 5 7</rect></_>
    <_>
      <rect>
        5 3 4 4</rect></_>
    <_>
      <rect>
        5 3 5 4</rect></_>
    <_>
      <rect>
        5 5 2 8</rect></_>
    <_>
      <rect>
        5 12 4 4</rect></_>
    <_>
      <rect>
        5 22 1 1</rect></_>
    <_>
      <rect>
        6 21 3 3</rect></_>
    <_>
      <rect>
        6 26 2 2</rect></_>
    <_>
      <rect>
        6 30 1 1</rect></_>
    <_>
      <rect>
        6 31 1 1</rect></_>
    <_>
      <rect>
        6 31 2 1</rect></_>
    <_>
      <rect>
        7 0 2 3</rect></_>
    <_>
      <rect>
        7 9 3 7</rect></_>
    <_>
      <rect>
        7 17 1 1</rect></_>
    <_>
      <rect>
        7 31 1 1</rect></_>
    <_>
      <rect>
        7 31 2 1</rect></_>
    <_>
      <rect>
        8 0 4 1</rect></_>
    <_>
      <rect>
        8 5 2 4</rect></_>
    <_>
      <rect>
        8 10 3 6</rect></_>
    <_>
      <rect>
        8 16 2 1</rect></_>
    <_>
      <rect>
        8 25 3 2</rect></_>
    <_>
      <rect>
        8 30 1 1</rect></_>
    <_>
      <rect>
        9 0 1 1</rect></_>
    <_>
      <rect>
        9 0 3 2</rect></_>
    <_>
      <rect>
        9 0 3 4</rect></_>
    <_>
      <rect>
        9 15 2 1</rect></_>
    <_>
      <rect>
        9 24 3 3</rect></_>
    <_>
      <rect>
        9 29 1 1</rect></_>
    <_>
      <rect>
        9 31 1 1</rect></_>
    <_>
      <rect>
        10 4 2 2</rect></_>
    <_>
      <rect>
        10 8 1 3</rect></_>
    <_>
      <rect>
        10 15 1 3</rect></_>
    <_>
      <rect>
        10 26 2 1</rect></_>
    <_>
      <rect>
        10 30 1 1</rect></_>
    <_>
      <rect>
        10 31 3 1</rect></_>
    <_>
      <rect>
        11 0 3 2</rect></_>
    <_>
      <rect>
        11 1 3 4</rect></_>
    <_>
      <rect>
        11 5 3 8</rect></_>
    <_>
      <rect>
        11 14 1 1</rect></_>
    <_>
      <rect>
        11 23 2 2</rect></_>
    <_>
      <rect>
        11 27 2 2</rect></_>
    <_>
      <rect>
        11 31 1 1</rect></_>
    <_>
      <rect>
        12 22 2 3</rect></_>
    <_>
      <rect>
        12 29 1 1</rect></_>
    <_>
      <rect>
        13 23 2 1</rect></_>
    <_>
      <rect>
        13 24 1 3</rect></_>
    <_>
      <rect>
        13 29 1 1</rect></_>
    <_>
      <rect>
        13 31 2 1</rect></_>
    <_>
      <rect>
        14 1 2 2</rect></_>
    <_>
      <rect>
        14 1 2 6</rect></_>
    <_>
      <rect>
        14 2 2 1</rect></_>
    <_>
      <rect>
        14 24 2 2</rect></_>
    <_>
      <rect>
        14 26 2 2</rect></_>
    <_>
      <rect>
        14 28 1 1</rect></_>
    <_>
      <rect>
        15 4 1 1</rect></_>
    <_>
      <rect>
        15 24 1 1</rect></_>
    <_>
      <rect>
        17 0 1 3</rect></_>
    <_>
      <rect>
        17 3 1 4</rect></_>
    <_>
      <rect>
        17 23 1 2</rect></_>
    <_>
      <rect>
        17 27 1 1</rect></_></features></cascade>
</opencv_storage>
