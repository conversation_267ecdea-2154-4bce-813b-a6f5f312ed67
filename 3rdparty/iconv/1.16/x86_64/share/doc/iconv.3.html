<!-- Creator     : groff version 1.22.3 -->
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
"http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta name="generator" content="groff -Thtml, see www.gnu.org">
<meta http-equiv="Content-Type" content="text/html; charset=US-ASCII">
<meta name="Content-Style" content="text/css">
<style type="text/css">
       p       { margin-top: 0; margin-bottom: 0; vertical-align: top }
       pre     { margin-top: 0; margin-bottom: 0; vertical-align: top }
       table   { margin-top: 0; margin-bottom: 0; vertical-align: top }
       h1      { text-align: center }
</style>
<title>ICONV</title>

</head>
<body>

<h1 align="center">ICONV</h1>

<a href="#NAME">NAME</a><br>
<a href="#SYNOPSIS">SYNOPSIS</a><br>
<a href="#DESCRIPTION">DESCRIPTION</a><br>
<a href="#RETURN VALUE">RETURN VALUE</a><br>
<a href="#ERRORS">ERRORS</a><br>
<a href="#CONFORMING TO">CONFORMING TO</a><br>
<a href="#SEE ALSO">SEE ALSO</a><br>

<hr>


<h2>NAME
<a name="NAME"></a>
</h2>


<p style="margin-left:11%; margin-top: 1em">iconv &minus;
perform character set conversion</p>

<h2>SYNOPSIS
<a name="SYNOPSIS"></a>
</h2>


<p style="margin-left:11%; margin-top: 1em"><b>#include
&lt;iconv.h&gt;</b></p>

<p style="margin-left:11%; margin-top: 1em"><b>size_t iconv
(iconv_t</b> <i>cd</i><b>, <br>
const char* *</b> <i>inbuf</i><b>, size_t *</b>
<i>inbytesleft</i><b>, <br>
char* *</b> <i>outbuf</i><b>, size_t *</b>
<i>outbytesleft</i><b>);</b></p>

<h2>DESCRIPTION
<a name="DESCRIPTION"></a>
</h2>


<p style="margin-left:11%; margin-top: 1em">The argument
<i>cd</i> must be a conversion descriptor created using the
function <b>iconv_open</b>.</p>

<p style="margin-left:11%; margin-top: 1em">The main case
is when <i>inbuf</i> is not NULL and <i>*inbuf</i> is not
NULL. In this case, the <b>iconv</b> function converts the
multibyte sequence starting at <i>*inbuf</i> to a multibyte
sequence starting at <i>*outbuf</i>. At most
<i>*inbytesleft</i> bytes, starting at <i>*inbuf</i>, will
be read. At most <i>*outbytesleft</i> bytes, starting at
<i>*outbuf</i>, will be written.</p>

<p style="margin-left:11%; margin-top: 1em">The
<b>iconv</b> function converts one multibyte character at a
time, and for each character conversion it increments
<i>*inbuf</i> and decrements <i>*inbytesleft</i> by the
number of converted input bytes, it increments
<i>*outbuf</i> and decrements <i>*outbytesleft</i> by the
number of converted output bytes, and it updates the
conversion state contained in <i>cd</i>. If the character
encoding of the input is stateful, the <b>iconv</b> function
can also convert a sequence of input bytes to an update of
the conversion state without producing any output bytes;
such input is called a <i>shift sequence</i>. The conversion
can stop for four reasons:</p>

<p style="margin-left:11%; margin-top: 1em">1. An invalid
multibyte sequence is encountered in the input. In this case
it sets <b>errno</b> to <b>EILSEQ</b> and returns
(size_t)(&minus;1). <i>*inbuf</i> is left pointing to the
beginning of the invalid multibyte sequence.</p>

<p style="margin-left:11%; margin-top: 1em">2. The input
byte sequence has been entirely converted, i.e.
<i>*inbytesleft</i> has gone down to 0. In this case
<b>iconv</b> returns the number of non-reversible
conversions performed during this call.</p>

<p style="margin-left:11%; margin-top: 1em">3. An
incomplete multibyte sequence is encountered in the input,
and the input byte sequence terminates after it. In this
case it sets <b>errno</b> to <b>EINVAL</b> and returns
(size_t)(&minus;1). <i>*inbuf</i> is left pointing to the
beginning of the incomplete multibyte sequence.</p>

<p style="margin-left:11%; margin-top: 1em">4. The output
buffer has no more room for the next converted character. In
this case it sets <b>errno</b> to <b>E2BIG</b> and returns
(size_t)(&minus;1).</p>

<p style="margin-left:11%; margin-top: 1em">A different
case is when <i>inbuf</i> is NULL or <i>*inbuf</i> is NULL,
but <i>outbuf</i> is not NULL and <i>*outbuf</i> is not
NULL. In this case, the <b>iconv</b> function attempts to
set <i>cd</i>&rsquo;s conversion state to the initial state
and store a corresponding shift sequence at <i>*outbuf</i>.
At most <i>*outbytesleft</i> bytes, starting at
<i>*outbuf</i>, will be written. If the output buffer has no
more room for this reset sequence, it sets <b>errno</b> to
<b>E2BIG</b> and returns (size_t)(&minus;1). Otherwise it
increments <i>*outbuf</i> and decrements
<i>*outbytesleft</i> by the number of bytes written.</p>

<p style="margin-left:11%; margin-top: 1em">A third case is
when <i>inbuf</i> is NULL or <i>*inbuf</i> is NULL, and
<i>outbuf</i> is NULL or <i>*outbuf</i> is NULL. In this
case, the <b>iconv</b> function sets <i>cd</i>&rsquo;s
conversion state to the initial state.</p>

<h2>RETURN VALUE
<a name="RETURN VALUE"></a>
</h2>


<p style="margin-left:11%; margin-top: 1em">The
<b>iconv</b> function returns the number of characters
converted in a non-reversible way during this call;
reversible conversions are not counted. In case of error, it
sets <b>errno</b> and returns (size_t)(&minus;1).</p>

<h2>ERRORS
<a name="ERRORS"></a>
</h2>


<p style="margin-left:11%; margin-top: 1em">The following
errors can occur, among others:</p>

<table width="100%" border="0" rules="none" frame="void"
       cellspacing="0" cellpadding="0">
<tr valign="top" align="left">
<td width="11%"></td>
<td width="9%">


<p><b>E2BIG</b></p></td>
<td width="2%"></td>
<td width="78%">


<p>There is not sufficient room at <i>*outbuf</i>.</p></td></tr>
<tr valign="top" align="left">
<td width="11%"></td>
<td width="9%">


<p><b>EILSEQ</b></p></td>
<td width="2%"></td>
<td width="78%">


<p>An invalid multibyte sequence has been encountered in
the input.</p></td></tr>
<tr valign="top" align="left">
<td width="11%"></td>
<td width="9%">


<p><b>EINVAL</b></p></td>
<td width="2%"></td>
<td width="78%">


<p>An incomplete multibyte sequence has been encountered in
the input.</p></td></tr>
</table>

<h2>CONFORMING TO
<a name="CONFORMING TO"></a>
</h2>


<p style="margin-left:11%; margin-top: 1em">POSIX:2001</p>

<h2>SEE ALSO
<a name="SEE ALSO"></a>
</h2>



<p style="margin-left:11%; margin-top: 1em"><b>iconv_open</b>(3),
<b>iconvctl</b>(3) <b>iconv_close</b>(3)</p>
<hr>
</body>
</html>
