.\" Copyright (c) <PERSON>
.\" Copyright (c) Free Software Foundation, Inc.
.\"
.\" This is free documentation; you can redistribute it and/or
.\" modify it under the terms of the GNU General Public License as
.\" published by the Free Software Foundation; either version 3 of
.\" the License, or (at your option) any later version.
.\"
.\" References consulted:
.\"   iconv.h
.\"
.TH ICONVCTL 3  "March 31, 2007" "GNU" "Linux Programmer's Manual"
.SH NAME
iconvctl \- control iconv behavior
.SH SYNOPSIS
.nf
.B #include <iconv.h>
.sp
.BI "int iconvctl (iconv_t " cd " , int " request ", void * " argument );
.fi
.SH DESCRIPTION
The argument \fIcd\fP must be a conversion descriptor created using the
function \fBiconv_open\fP.
.PP
\fBiconvctl\fP queries or adjusts the behavior of the \fBiconv\fP function,
when invoked with the specified conversion descriptor, depending on the
request value.
.SH "REQUEST VALUES"
The following are permissible values for the \fIrequest\fP parameter.
.TP
.B ICONV_TRIVIALP
\fIargument\fP should be an \fBint *\fP which will receive 1 if the
conversion is trivial, or 0 otherwise.
.TP
.B ICONV_GET_TRANSLITERATE
\fIargument\fP should be an \fBint *\fP which will receive 1 if 
transliteration is enabled in the conversion, or 0 otherwise.
.TP
.B ICONV_SET_TRANSLITERATE
\fIargument\fP should be a \fBconst int *\fP, pointing to an \fBint\fP value.
A non-zero value is used to enable transliteration in the conversion. A zero
value disables it.
.TP
.B ICONV_GET_DISCARD_ILSEQ
\fIargument\fP should be an \fBint *\fP which will receive 1 if 
"illegal sequence discard and continue" is enabled in the conversion,
or 0 otherwise.
.TP
.B ICONV_SET_DISCARD_ILSEQ
\fIargument\fP should be a \fBconst int *\fP, pointing to an \fBint\fP value.
A non-zero value is used to enable "illegal sequence discard and continue"
in the conversion. A zero value disables it.
.SH "RETURN VALUE"
The \fBiconvctl\fP function returns 0 if it succeeds. In case of error, it sets
\fBerrno\fP and returns \-1.
.SH ERRORS
The following errors can occur, among others:
.TP
.B EINVAL
The request is invalid.
.SH "CONFORMING TO"
This function is implemented only in GNU libiconv and not in other \fBiconv\fP
implementations. It is not backed by a standard. You can test for its presence
through \fB(_LIBICONV_VERSION >= 0x0108)\fP.
.SH "SEE ALSO"
.BR iconv_open (3)
.BR iconv (3)
