#include <iostream>
#include <sstream>
#include <fstream>
#include <opencv2/core/core.hpp>
#include <opencv2/highgui/highgui.hpp>
#include <opencv2/imgproc/imgproc.hpp>

#include "XMTool.h"
#include "kanna_rotate.h"
#include "Utils.h"
using namespace std;
using namespace MiAILab;

void TestRotate(const cv::Mat& img, int iters)
{
    //opencv
    cv::Mat cvDst, xmDst, diff;
    int dstw, dsth, countNonZero;
    double cvTm, xmTm1, xmTm2;
    double start, stop;
    //int iters = 10;
    
    vector<int> rotates = {cv::ROTATE_90_CLOCKWISE, cv::ROTATE_180,cv::ROTATE_90_COUNTERCLOCKWISE};

    for(int k = 1; k <= 3;k++)
    {
        start = XMTool::GetTimeMS();
        for(int i = 0;i < iters;i++)
            cv::rotate(img, cvDst, rotates[k - 1]);
        stop = XMTool::GetTimeMS();
        cvTm = (stop - start)/iters;
        
        dstw = img.rows;
        dsth = img.cols;
        if(k == 2)
        {
            dstw = img.cols;
            dsth = img.rows;
        }

        xmDst = cv::Mat(dsth, dstw, CV_8UC3);
        start = XMTool::GetTimeMS();
        for(int i = 0;i < iters;i++)
            XMTool::RotateC3(img.data, img.cols, img.rows, k, xmDst.data, dstw, dsth);
        stop = XMTool::GetTimeMS();
        xmTm1 = (stop - start)/iters;

        start = XMTool::GetTimeMS();
        for(int i = 0;i < iters;i++)
            XMTool::KannaRotateC3(img.data, img.cols, img.rows, k, xmDst.data, dstw, dsth);
        stop = XMTool::GetTimeMS();
        xmTm2 = (stop - start) / iters;

        //cout<<"cvDst "<<cvDst.rows<<" "<<cvDst.cols<<" "<<cvDst.channels()<<endl;
        //cout<<"xmDst "<<xmDst.rows<<" "<<xmDst.cols<<" "<<xmDst.channels()<<endl;
        diff = cv::abs(cvDst - xmDst);
        if(diff.channels() == 3)
            cv::cvtColor(diff, diff, CV_BGR2GRAY);
        countNonZero = cv::countNonZero(diff);

        std::cout<<"rotate "<<k* 90<<" diff "<< countNonZero<<" "<<(float) countNonZero/(dstw * dsth)<<endl;
        std::cout<<"cv time "<<cvTm<<" rotate tm "<<xmTm1<<" kanna rotate tm "<<xmTm2<<" ms"<<endl;
    }     

    cv::Mat gray;
    cv::cvtColor(img, gray, CV_BGR2GRAY);
    for(int k = 1; k <= 3;k++)
    {
        start = XMTool::GetTimeMS();
        for(int i = 0;i < iters;i++)
            cv::rotate(gray, cvDst, rotates[k - 1]);
        stop = XMTool::GetTimeMS();
        cvTm = (stop - start)/iters;
        
        dstw = gray.rows;
        dsth = gray.cols;
        if(k == 2)
        {
            dstw = img.cols;
            dsth = img.rows;
        }

        xmDst = cv::Mat(dsth, dstw, CV_8UC1);
        start = XMTool::GetTimeMS();
        for(int i = 0;i < iters;i++)
            XMTool::RotateC1(gray.data, gray.cols, gray.rows, k, xmDst.data, dstw, dsth);
        stop = XMTool::GetTimeMS();
        xmTm1 = (stop - start)/iters;

        start = XMTool::GetTimeMS();
        for(int i = 0;i < iters;i++)
            XMTool::KannaRotateC1(gray.data, gray.cols, gray.rows, k, xmDst.data, dstw, dsth);
        stop = XMTool::GetTimeMS();
        xmTm2 = (stop - start) / iters;

        //cout<<"cvDst "<<cvDst.rows<<" "<<cvDst.cols<<" "<<cvDst.channels()<<endl;
        //cout<<"xmDst "<<xmDst.rows<<" "<<xmDst.cols<<" "<<xmDst.channels()<<endl;
        diff = cv::abs(cvDst - xmDst);
        if(diff.channels() == 3)
            cv::cvtColor(diff, diff, CV_BGR2GRAY);
        countNonZero = cv::countNonZero(diff);

        std::cout<<"rotate "<<k* 90<<" diff "<< countNonZero<<" "<<(float) countNonZero/(dstw * dsth)<<endl;
        std::cout<<"cv time "<<cvTm<<" rotate tm "<<xmTm1<<" kanna rotate tm "<<xmTm2<<" ms"<<endl;
    }     
}

void TestResize(const cv::Mat& img, int iters)
{
    cv::Mat cvDst, xmDst, diff;
    //int iters = 10;
    double start, stop;
    double cvTm, xmTm;
    float countNonZero = 0;
    cvTm = 0;
    xmTm = 0;

    int dstw, dsth;
    for(int i = 0;i < iters;i++)
    {
        dstw = rand() % 1000 + 10;
        dsth = rand() % 1000 + 10;

        start = XMTool::GetTimeMS();
        cv::resize(img, cvDst, cv::Size(dstw, dsth));
        stop = XMTool::GetTimeMS();
        cvTm += stop - start;

        cv::Mat xmDst(dsth, dstw, CV_8UC3);
        start = XMTool::GetTimeMS();
        XMTool::NCNNResizeBilinearC3(img.data, img.cols, img.rows, xmDst.data, dstw, dsth);
        stop = XMTool::GetTimeMS();
        xmTm += stop - start;

        diff = cv::abs(xmDst - cvDst);
        if(diff.channels() == 3)
            cv::cvtColor(diff, diff, CV_BGR2GRAY);
        countNonZero += cv::countNonZero(diff)/(dstw * dsth);
    }

    cout<<"resize bgr cv time "<<cvTm/iters<<" xm time "<<xmTm/iters<<" ms"<<endl;
    cout<<"resize bgr diff "<<countNonZero / iters<<endl;

    cv::Mat gray;
    cv::cvtColor(img, gray, CV_BGR2GRAY);
    for(int i = 0;i < iters;i++)
    {
        dstw = rand() % 1000 + 10;
        dsth = rand() % 1000 + 10;

        start = XMTool::GetTimeMS();
        cv::resize(gray, cvDst, cv::Size(dstw, dsth));
        stop = XMTool::GetTimeMS();
        cvTm += stop - start;

        cv::Mat xmDst(dsth, dstw, CV_8UC1);
        start = XMTool::GetTimeMS();
        XMTool::NCNNResizeBilinearC1(gray.data, gray.cols, gray.rows, xmDst.data, dstw, dsth);
        stop = XMTool::GetTimeMS();
        xmTm += stop - start;

        diff = cv::abs(xmDst - cvDst);
        if(diff.channels() == 3)
            cv::cvtColor(diff, diff, CV_BGR2GRAY);
        countNonZero += cv::countNonZero(diff)/(dstw * dsth);
    }

    cout<<"resize gray cv time "<<cvTm/iters<<" xm time "<<xmTm/iters<<" ms"<<endl;
    cout<<"resize gray diff "<<countNonZero / iters<<endl;    
}

void TestColorCvt(const cv::Mat& img, int iters)
{
    // to gray
    cv::Mat cvDst, xmDst, diff;
    //int iters = 10;
    double start, stop;
    double cvTm, xmTm;
    int countNonZero = 0;

    int w = img.cols;
    int h = img.rows;
    start = XMTool::GetTimeMS();
    for(int i = 0;i < iters;i++)
        cv::cvtColor(img, cvDst, CV_BGR2GRAY);
    stop = XMTool::GetTimeMS();
    cvTm = (stop - start)/iters;

    xmDst = cv::Mat(img.rows, img.cols, CV_8UC1);
    start = XMTool::GetTimeMS();
    for(int i = 0; i < iters;i++)
        XMTool::BGR2Gray(img.data, img.cols, img.rows, xmDst.data);
    stop = XMTool::GetTimeMS();
    xmTm = (stop - start)/iters;

    diff = cv::abs(cvDst - xmDst);
    if(diff.channels() == 3)
        cv::cvtColor(diff, diff, CV_BGR2GRAY);
    countNonZero = cv::countNonZero(diff);
    std::cout<<"BGR2GRAY diff "<< countNonZero<<" "<<(float) countNonZero/(w * h)<<endl;
    std::cout<<"BGR2GRAY cv time "<<cvTm<<" xm tm "<<xmTm<<" ms"<<endl;

    cv::cvtColor(img, img, CV_BGR2RGB);
    start = XMTool::GetTimeMS();
    for(int i = 0;i < iters;i++)
        cv::cvtColor(img, cvDst, CV_RGB2GRAY);
    stop = XMTool::GetTimeMS();
    cvTm = (stop - start)/iters;

    xmDst = cv::Mat(img.rows, img.cols, CV_8UC1);
    start = XMTool::GetTimeMS();
    for(int i = 0; i < iters;i++)
        XMTool::RGB2Gray(img.data, img.cols, img.rows, xmDst.data);
    stop = XMTool::GetTimeMS();
    xmTm = (stop - start)/iters;

    diff = cv::abs(cvDst - xmDst);
    if(diff.channels() == 3)
        cv::cvtColor(diff, diff, CV_RGB2GRAY);
    countNonZero = cv::countNonZero(diff);
    std::cout<<"RGB2GRAY diff "<< countNonZero<<" "<<(float) countNonZero/(w * h)<<endl;
    std::cout<<"RGB2GRAY cv time "<<cvTm<<" xm tm "<<xmTm<<" ms"<<endl;    
}


int main(int argc, char** argv)
{
    if(argc != 3)
    {
        cout<<"Usage:<img> <outpath>"<<endl;
        return -1;
    }

    string name = argv[1];
    string outpath = argv[2];

    cv::Mat img = cv::imread(name);
    if(img.empty())
    {
        cout<<"failed to read image file "<<name<<endl;
        return -1;
    }

    int iters = 100;
    for(int i = 0;i < iters;i++)
    {
        int rand_w = rand() % 2000 + 10;
        int rand_h = rand() % 2000 + 10;
        cv::resize(img, img, cv::Size(rand_w, rand_h));


        cout<<i<<" "<<rand_w<<" "<<rand_h<<" "<<img.isContinuous()<<endl;
        cv::Mat tmp1 = img.clone();
        TestRotate(tmp1, 1);
        cv::Mat tmp2 = img.clone();
        TestColorCvt(tmp2, 1);

        TestResize(img, 1);
    }

    return 0;
}