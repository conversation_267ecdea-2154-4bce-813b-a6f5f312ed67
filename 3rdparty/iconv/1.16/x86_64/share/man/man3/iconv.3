.\" Copyright (c) Free Software Foundation, Inc.
.\"
.\" This is free documentation; you can redistribute it and/or
.\" modify it under the terms of the GNU General Public License as
.\" published by the Free Software Foundation; either version 3 of
.\" the License, or (at your option) any later version.
.\"
.\" References consulted:
.\"   GNU glibc-2 source code and manual
.\"   OpenGroup's Single Unix specification http://www.UNIX-systems.org/online.html
.\"
.TH ICONV 3  "September 7, 2008" "GNU" "Linux Programmer's Manual"
.SH NAME
iconv \- perform character set conversion
.SH SYNOPSIS
.nf
.B #include <iconv.h>
.sp
.BI "size_t iconv (iconv_t " cd ,
.BI "              const char* * " inbuf ", size_t * "inbytesleft ,
.BI "              char* * " outbuf ", size_t * "outbytesleft );
.fi
.SH DESCRIPTION
The argument \fIcd\fP must be a conversion descriptor created using the
function \fBiconv_open\fP.
.PP
The main case is when \fIinbuf\fP is not NULL and \fI*inbuf\fP is not NULL.
In this case, the \fBiconv\fP function converts the multibyte sequence
starting at \fI*inbuf\fP to a multibyte sequence starting at \fI*outbuf\fP.
At most \fI*inbytesleft\fP bytes, starting at \fI*inbuf\fP, will be read.
At most \fI*outbytesleft\fP bytes, starting at \fI*outbuf\fP, will be written.
.PP
The \fBiconv\fP function converts one multibyte character at a time, and for
each character conversion it increments \fI*inbuf\fP and decrements
\fI*inbytesleft\fP by the number of converted input bytes, it increments
\fI*outbuf\fP and decrements \fI*outbytesleft\fP by the number of converted
output bytes, and it updates the conversion state contained in \fIcd\fP.
If the character encoding of the input is stateful, the \fBiconv\fP function
can also convert a sequence of input bytes to an update of the conversion state
without producing any output bytes; such input is called a \fIshift sequence\fP.
The conversion can stop for four reasons:
.PP
1. An invalid multibyte sequence is encountered in the input. In this case
it sets \fBerrno\fP to \fBEILSEQ\fP and returns (size_t)(\-1). \fI*inbuf\fP
is left pointing to the beginning of the invalid multibyte sequence.
.PP
2. The input byte sequence has been entirely converted, i.e. \fI*inbytesleft\fP
has gone down to 0. In this case \fBiconv\fP returns the number of
non-reversible conversions performed during this call.
.PP
3. An incomplete multibyte sequence is encountered in the input, and the
input byte sequence terminates after it. In this case it sets \fBerrno\fP to
\fBEINVAL\fP and returns (size_t)(\-1). \fI*inbuf\fP is left pointing to the
beginning of the incomplete multibyte sequence.
.PP
4. The output buffer has no more room for the next converted character. In
this case it sets \fBerrno\fP to \fBE2BIG\fP and returns (size_t)(\-1).
.PP
A different case is when \fIinbuf\fP is NULL or \fI*inbuf\fP is NULL, but
\fIoutbuf\fP is not NULL and \fI*outbuf\fP is not NULL. In this case, the
\fBiconv\fP function attempts to set \fIcd\fP's conversion state to the
initial state and store a corresponding shift sequence at \fI*outbuf\fP.
At most \fI*outbytesleft\fP bytes, starting at \fI*outbuf\fP, will be written.
If the output buffer has no more room for this reset sequence, it sets
\fBerrno\fP to \fBE2BIG\fP and returns (size_t)(\-1). Otherwise it increments
\fI*outbuf\fP and decrements \fI*outbytesleft\fP by the number of bytes
written.
.PP
A third case is when \fIinbuf\fP is NULL or \fI*inbuf\fP is NULL, and
\fIoutbuf\fP is NULL or \fI*outbuf\fP is NULL. In this case, the \fBiconv\fP
function sets \fIcd\fP's conversion state to the initial state.
.SH "RETURN VALUE"
The \fBiconv\fP function returns the number of characters converted in a
non-reversible way during this call; reversible conversions are not counted.
In case of error, it sets \fBerrno\fP and returns (size_t)(\-1).
.SH ERRORS
The following errors can occur, among others:
.TP
.B E2BIG
There is not sufficient room at \fI*outbuf\fP.
.TP
.B EILSEQ
An invalid multibyte sequence has been encountered in the input.
.TP
.B EINVAL
An incomplete multibyte sequence has been encountered in the input.
.SH "CONFORMING TO"
POSIX:2001
.SH "SEE ALSO"
.BR iconv_open (3),
.BR iconvctl (3)
.BR iconv_close (3)
