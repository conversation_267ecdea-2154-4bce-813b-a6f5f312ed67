# Build both ARMv5TE and ARMv7-A machine code.
#NDK_TOOLCHAIN_VERSION := 4.9
APP_PLATFORM := android-29
APP_ABI    +=  arm64-v8a
APP_MODULES:= Test
APP_OPTIM  := debug  
APP_STL    := c++_shared
OPT_CFLAGS := -O0 -fexceptions -fno-bounds-check -funroll-loops -funswitch-loops -fvisibility=hidden \
              -finline-functions -funsafe-loop-optimizations       
OPT_CPPFLAGS := $(OPT_CLFAGS)  
  
APP_CFLAGS  := $(APP_CFLAGS) $(OPT_CFLAGS)  -frtti -std=c++14 -fsanitize=address -fno-omit-frame-pointer
#-fsanitize=address -fno-omit-frame-pointer
APP_CPPFLAGS := $(APP_CPPFLAGS) $(OPT_CPPFLAGS) 
APP_LDFLAGS := -fsanitize=address
#APP_LDFLAGS := -fsanitize=address
#APP_CPPFLAGS += -fexceptions  