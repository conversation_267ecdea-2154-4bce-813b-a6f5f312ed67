#ifndef BST_QR_TRACK_H
#define BST_QR_TRACK_H
#include <vector>
#include <string>
#include <memory>
#ifdef FASTCV
#include "fastcv.h"
#endif
#include "MoveDetection.h"
//#include "opencv2/opencv.hpp"
#include "bstai_qrcode.hpp"
#include "../Util/my_log.h"
#include "QRFlow.h"
//#include "mosse_tracker.h"

#define MAX_QR_NUM 3 
#define MATCH_IOU_THD 0.3 
#define DECODE_AND_TRACK_IOU_THD 0.5
#define NMS_IOU_THD 0.3
#define FEAT_LEN 9
#define DOWN_SCALE 4

#ifndef BST_RECT_TAG
#define BST_RECT_TAG
typedef struct BSTRect
{
    float left;
    float top;
    float right;
    float bottom;
    BSTRect()
    {
        left = 0;
        top = 0;
        right = 0;
        bottom = 0;
    }

    BSTRect(float l, float t, float r, float b)
    {
        left = l;
        top = t;
        right = r;
        bottom = b;
    }
} BSTRect;
#endif

#ifndef SINGLE_QR_INFO_TAG
#define SINGLE_QR_INFO_TAG
typedef struct SingleQRInfo
{
    int track_id;

    BSTRect rect;

    float ct_x;
    float ct_y;

    std::string decode_text;
}SingleQRInfo;
#endif

#ifndef FRAME_INFO_TAG
#define FRAME_INFO_TAG
typedef struct FrameInfo
{
    unsigned char* img;
    int w;
    int h;
}FrameInfo;
#endif

#ifndef QR_CHANGE_INFO_TAG
#define QR_CHANGE_INFO_TAG
typedef struct QRChangeInfo
{
    float w_change_ratio;
    float h_change_ratio;
    float ct_x_change_ratio;
    float ct_y_change_ratio;

    QRChangeInfo()
    {
        w_change_ratio = 0;
        h_change_ratio = 0;
        ct_x_change_ratio = 0;
        ct_y_change_ratio = 0;
    }
}QRChangeInfo;
#endif

#ifndef GLOBAL_MATCH_INFO_TAG
#define GLOBAL_MATCH_INFO_TAG
typedef struct GlobalMatchInfo
{
    float min_diff_score;
    std::vector<float> block_min_diff_score;
    int slow_change_continue_num;

    GlobalMatchInfo()
    {
        min_diff_score = 0.0;
        slow_change_continue_num = 0;
    }
}GlobalMatchInfo;
#endif

class BstSingleQRTracker
{
public:
    BstSingleQRTracker();
    ~BstSingleQRTracker();

    int track(CGrayImage &img, CGrayImage& ori_img, const params &mParams);

    int setPreFeatPts(BSTRect &qr_rect);
    int setPreFeatPts(std::vector<PointXY> &corner_points);
    float* getPredictFeatPts() {return m_PredictFeatPts;};
    float getModuleSize(){return m_ModuleSize;};

    int createNewTrackingQR(CGrayImage& img, CGrayImage& ori_img, DecodeResult &decode_result, int maxIDNum);

private:
    unsigned char* m_PreImg=nullptr;
    int m_PreW;
    int m_PreH;
    int m_PreC;
    unsigned char* m_PreOriImg=nullptr;
    int m_PreOriW;
    int m_PreOriH;
    int m_PreOriC;

public:
    float* m_PreFeatPts=nullptr;
    float* m_PredictFeatPts=nullptr;
    float m_ModuleSize;

#ifdef FASTCV
    fcvPyramidLevel_v2 *preImgPyr = NULL;
#endif
public:
    SingleQRInfo m_PreInfo; // pre qrcode info
    SingleQRInfo m_CurInfo;
    int m_TrackID = -1;
    int m_DecodeVerifyContinueFailNum = 0;
    int m_DecodeVerifyContinueSuccessNum = 0;
    int m_QRCenterMoveFastContinueNum = 0;
    int m_QRCenterMoveSlowContinueNum = 0;
    int m_UpdateTrackCoordinateInfo = 0;

    QRChangeInfo m_QRChangeInfo; // morph change result

   // MosseTracker m_MosseTracker;
    int m_FrameFixNum; // 
    float m_CurResponseScore = 0; // mosse response score
    bool m_OpenMosseTrack = true;
    bool mIsNewTracker = false;
    bool m_OpenGlobalMatch = true;
    bool m_ConfirmIsQR = false;
    int m_11311VerifyDurationFailNum = 0;
    int m_11311VerifyContinueSuccessNum = 0;
    int m_GlobalMatchDurationFailNum = 0;
    int m_GlobalMatchContinueSuccessNum = 0;
    int m_TrackingContinueNum = 0;

    GlobalMatchInfo m_PreGLobalMatchInfo;
    GlobalMatchInfo m_CurGlobalMatchInfo;
};

class BstMultiQRTracker
{
public:
    BstMultiQRTracker(const char* config_path);
    ~BstMultiQRTracker();

    int track(unsigned char* img, int w, int h, int c, std::vector<SingleQRInfo> &QR_infos);
    int reset() {
        auto it = pTrackers_.begin();
        for (; it != pTrackers_.end();)
        {
            delete (*it);
            it = pTrackers_.erase(it);
        }
        frameNum_ = 0;
        detect_flag_ = false;
        mIsFirstFrame = true;
        m_DetectCnt = 0;
        m_SlowDetectCnt = 0;
        m_StableDetectCnt = 0;
    }
    void setQRRectInfo(SingleQRInfo &qr_trk_info, BstSingleQRTracker* pTracker, int w, int h);
    int getErrorCode() {
        return mErrorCode;
    };
    bool load_config(const char* config_path);
    params mParams;
    params mParams_gpu_scanner_;
    params mParams_gpu_scanner64_;
    params mParams_scanner_;
    params mParams_sync_scanner_;
    CQRFlow mQRFlow;
    bool mBusy;
    bool mByPass = false;
    bool mDump = false;
    int mIgnoreframeNum = 0;
    bool mBuildObject = true;
    int mErrorCode = 0;
private:
    // motion detect
    MoveDetect m_MoveDetect;
    int m_DetectCnt = 0;
    int m_SlowDetectCnt = 0;
    int m_StableDetectCnt = 0;

    cv::qrcode_imp::BstAIQRCode *scanner_ = NULL;
    cv::qrcode_imp::BstAIQRCode *sync_scanner_ = NULL;
    cv::qrcode_imp::BstAIQRCode *gpu_scanner_ = NULL;
    cv::qrcode_imp::BstAIQRCode *gpu_scanner64_ = NULL;

    std::vector<BstSingleQRTracker*> pTrackers_;

    int maxIDNum_ = 0;
    int frameNum_ = 0;

    bool detect_flag_ = false;
    int detect_noqr_cumulate_cnt_ = 0;

    bool mIsFirstFrame = true;
    FrameInfo mCurDownImgInfo;
    bool mNeedDetectFlag = false;
    bool mNeedDecodeFlag = false;

    // model buffer
    char* sr_buffer = nullptr;
    //char* sr_buffer_1 = nullptr;
    //char* sr_buffer_2 = nullptr;
    long long sr_len = 0;
    long long sr_len_1 = 0;
    long long sr_len_2 = 0;
    char* detect_buffer = nullptr;
    long long detect_len = 0;
    char* detect_buffer_tf = nullptr;
    long long detect_len_tf = 0;
    char* detect_buffer_tf64 = nullptr;
    long long detect_len_tf64 = 0;
    std::string m_dir;
    std::string m_strDetProroTxt;
    std::string m_strSRProroTxt;
};

#endif