
#include "BmpImage.h"
#include <math.h>
#pragma warning(disable:4996)

#define max(a,b)    (((a) > (b)) ? (a) : (b))
#define min(a,b)    (((a) < (b)) ? (a) : (b))

#ifndef SafeFree
#define SafeFree(x) \
    {\
        if((x) != NULL)\
        {\
            free(x);\
            x = NULL;\
        }\
    }
#endif

#define WIDTHBYTES(i) (((i)+31)/32*4)

typedef struct tagRGBQUAD
{
    unsigned char    rgbBlue;
    unsigned char    rgbGreen;
    unsigned char    rgbRed;
    unsigned char    rgbReserved;
} RGBQUAD;


#pragma pack(push,2)
typedef struct tagBITMAPFILEHEADER
{
    unsigned short    bfType;
    unsigned int   bfSize;
    unsigned short    bfReserved1;
    unsigned short    bfReserved2;
    unsigned int   bfOffBits;
} BITMAPFILEHEADER;
#pragma pack(pop)


typedef struct tagBITMAPINFOHEADER
{
    unsigned int      biSize;
    int       biWidth;
    int       biHeight;
    unsigned short       biPlanes;
    unsigned short       biBitCount;
    unsigned int      biCompression;
    unsigned int      biSizeImage;
    int       biXPelsPerMeter;
    int       biYPelsPerMeter;
    unsigned int      biClrUsed;
    unsigned int      biClrImportant;
} BITMAPINFOHEADER;

int LoadBmp( SimpleBmpImage* pMIImg, const char* pImgPath)
{
    BITMAPFILEHEADER bf; //BMP file header
    BITMAPINFOHEADER bi; //BMP information header

    FILE* fp = NULL;           //the pointer of file
    unsigned int fileLinebytes = 0;     // the number of unsigned char in one row , 4 bytes alignment
    unsigned int imgLinebytes = 0;
    unsigned int lineOffset = 0;
    unsigned int ImgbytesSize = 0; // the total bytes in the image
    int i = 0;
    SimpleBmpImage miImage = {NULL, 0, 0, 0, 0, 0};

    // release image memory
    if (pMIImg->bCreateMem == true)
    {
        free(pMIImg->pData);
    }
    pMIImg->pData = NULL;

    // open file
    fp = fopen(pImgPath, "rb");
    if (NULL == fp)
    {
        return -1;
    }

    //read file header and information header
    if ( 1 != fread(&bf, sizeof(BITMAPFILEHEADER), 1, fp))
    {
        fclose(fp);
        return -1;
    }
    if ( 1 != fread(&bi, sizeof(BITMAPINFOHEADER), 1, fp))
    {
        fclose(fp);
        return -1;
    }

    // calculate the number of unsigned char in one row and in the image area
    fileLinebytes = (unsigned int)WIDTHBYTES(bi.biWidth * bi.biBitCount); //4 bytes alignment
    imgLinebytes = bi.biWidth * (bi.biBitCount >> 3);
    lineOffset = fileLinebytes - imgLinebytes;
    ImgbytesSize = (unsigned int)imgLinebytes * bi.biHeight;

    if (bi.biBitCount == 16)
    {
        //unsigned int mask[4] = {0xF800, 0x7E0, 0x1F, 0};
        unsigned int mask[4] ;
        if ( 4 != fread(mask, sizeof(unsigned int), 4, fp))
        {
            fclose(fp);
            return -1;
        }
    }
    else if (bi.biBitCount == 8)
    {
        RGBQUAD ipRGB[256] = {0};
        if ( 256 != fread(ipRGB, sizeof(RGBQUAD), 256, fp))
        {
            fclose(fp);
            return -1;
        }
    }

    // initialize image
    pMIImg->pData = (unsigned char*)malloc(sizeof(unsigned char) * ImgbytesSize);
    if (!pMIImg->pData)
    {
        fclose(fp);
        return -1;
    }

    pMIImg->nBits = bi.biBitCount;
    pMIImg->nImgW = bi.biWidth;
    pMIImg->nImgH = bi.biHeight;
    pMIImg->bCreateMem = true;

    for (i = pMIImg->nImgH - 1; i >= 0; --i)
    {
        if ( imgLinebytes != fread(pMIImg->pData + i * imgLinebytes, 1, imgLinebytes, fp))
        {
            fclose(fp);
            return -1;
        }
        if ( 0 != fseek(fp, lineOffset, SEEK_CUR))
        {
            fclose(fp);
            return -1;
        }
    }

    fclose(fp);
    pMIImg->bValid = true;

    return 0;
}

int ReleaseBmpImage(SimpleBmpImage* pMIImg)
{
    if ( (true != pMIImg->bCreateMem) || (NULL == pMIImg->pData) )
    {
        return -1;
    }
    SafeFree(pMIImg->pData);
    pMIImg->bValid = false;
    pMIImg->bCreateMem = false;
    return 0;
}

/*!
    \fn     save the image object in continus buffer, include bmp header information
    \param  ppMen, the  put buffer of image object
    \param  pnSize, the size of  put buffer
    \param  pMIImg, the image object
    \return the address of  put buffer
 */
unsigned char* BmpCreateSaveToMem(  unsigned char** ppMen,   int* pnSize, SimpleBmpImage* pMIImg)
{
    BITMAPINFOHEADER bi = {0};
    BITMAPFILEHEADER fi = {0};
    int nLinebytes = 0, i = 0, imgLinebytes = 0;
    unsigned short wTemp = 0;
    unsigned int dwTemp = 0;
    unsigned char* pMem = NULL;
    unsigned char *p = NULL;
    int nShiftbytes = 0;
    int nColorTableEntries = 0;

    if (*ppMen)
    {
        free(*ppMen);
        *ppMen = NULL;
    }
    nLinebytes = WIDTHBYTES(pMIImg->nImgW * pMIImg->nBits);
    *pnSize = sizeof(BITMAPFILEHEADER) + sizeof(BITMAPINFOHEADER) + pMIImg->nImgH * nLinebytes;
    if (pMIImg->nBits == 16)
    {
        *pnSize = *pnSize + 4 * sizeof(unsigned int);
    }
    else if (pMIImg->nBits == 8)
    {
        *pnSize += (256 * sizeof(RGBQUAD));
    }
    *ppMen = (unsigned char*)malloc(sizeof(unsigned char) * (*pnSize));
    pMem = *ppMen;

    wTemp = 0x4D42;
    dwTemp = 54 + nLinebytes * pMIImg->nImgH;
    if (pMIImg->nBits == 16)
    {
        dwTemp += 4 * sizeof(unsigned int);
    }
    else if (pMIImg->nBits == 8)
    {
        dwTemp += 256 * sizeof(RGBQUAD);
    }

    switch (pMIImg->nBits)
    {
        case 1:
            nColorTableEntries = 2;
            break;
        case 4:
            nColorTableEntries = 16;
            break;
        case 8:
            nColorTableEntries = 256;
            break;
        case 16:
        case 24:
        case 32:
            nColorTableEntries = 0;
            break;
        default:
            break;
            //ASSERT(false);
    }

    fi.bfType = 0x4D42;
    fi.bfSize = *pnSize;
    fi.bfReserved1 = fi.bfReserved2 = 0;
    fi.bfOffBits = sizeof(BITMAPFILEHEADER) + sizeof(BITMAPINFOHEADER) + sizeof(RGBQUAD) * nColorTableEntries;
    memcpy(pMem, &fi, sizeof(fi));
    pMem += sizeof(fi);

    memset(&bi, 0, sizeof(BITMAPINFOHEADER));
    bi.biSize = sizeof(BITMAPINFOHEADER);
    bi.biWidth = pMIImg->nImgW;
    bi.biHeight = pMIImg->nImgH;
    bi.biBitCount = (unsigned short)pMIImg->nBits;
    bi.biPlanes = 1;
    if (pMIImg->nBits == 16)
    {
        bi.biCompression = 3L;
    }
    nShiftbytes = sizeof(BITMAPINFOHEADER);
    memcpy(pMem, &bi, nShiftbytes);
    pMem += nShiftbytes;

    if (pMIImg->nBits == 16)
    {
        unsigned int mask[4] = {0xF800, 0x7E0, 0x1F, 0};
        nShiftbytes = sizeof(unsigned int) * 4;
        memcpy(pMem, mask, nShiftbytes);
        pMem += nShiftbytes;
    }
    else if (pMIImg->nBits == 8)
    {
        RGBQUAD rgb[256];
        for (i = 0; i < 256; i++)
        {
            rgb[i].rgbBlue  = (unsigned char)i;
            rgb[i].rgbGreen = (unsigned char)i;
            rgb[i].rgbRed   = (unsigned char)i;
            rgb[i].rgbReserved  = 0;
        }
        nShiftbytes = 256 * sizeof(RGBQUAD);
        memcpy(pMem, rgb, nShiftbytes);
        pMem += nShiftbytes;
    }

    imgLinebytes = pMIImg->nImgW * pMIImg->nBits / 8;
    p = pMIImg->pData + (pMIImg->nImgH - 1) * (imgLinebytes);
    for (i = 0; i < pMIImg->nImgH; i++, p -= (imgLinebytes))
    {
        nShiftbytes = imgLinebytes;
        memcpy(pMem, p, nShiftbytes);
        pMem += imgLinebytes;
        pMem += (nLinebytes - imgLinebytes);
    }
    return *ppMen;
}

/*!
    \fn     release the memory that is allocate by SimpleBmpImage object
    \param  the pointer of the pointer of the memory
    \return null
 */
void ReleaseMem(unsigned char** pMen)
{
    if (*pMen != NULL)
    {
        free(*pMen);
        *pMen = NULL;
    }
}

/*!
    \fn     create the memory, the size is nNumbyte
    \param  nNumbyte, the size is to be allocated
    \return the allocated memory address.
 */
void* CreateMem(int nNumbyte)
{
    return malloc(nNumbyte);
}

/*!
    \fn     read bmp image form the file
    \param  pImgPath, the path of image file
    \param  pMIImg, the  put image from file
    \return 0 failed, or else successful
 */
int SaveBmp(const char* pImgPath, SimpleBmpImage* pMIImg)
{
    FILE * stream = 0;
    unsigned char *p = NULL;
    unsigned char *pTmp = NULL;

    BITMAPFILEHEADER bf = {0x4D42, 0};
    BITMAPINFOHEADER bi = {0};
    int nLinebytes, i, imgLinebytes;

    if (strlen(pImgPath) == 0)
    {
        return 0;
    }
    stream = fopen(pImgPath, "wb");
    if (stream == NULL)
    {
        return 0;
    }


    nLinebytes = WIDTHBYTES(pMIImg->nImgW * pMIImg->nBits);
    bf.bfSize = sizeof(BITMAPFILEHEADER) + sizeof(BITMAPINFOHEADER) + nLinebytes * pMIImg->nImgH;
    if (pMIImg->nBits == 16)
    {
        bf.bfSize += 4 * sizeof(unsigned int);
    }
    else if (pMIImg->nBits == 8)
    {
        bf.bfSize += 256 * sizeof(RGBQUAD);
    }


    bf.bfOffBits = sizeof(BITMAPFILEHEADER) + sizeof(BITMAPINFOHEADER);
    if (pMIImg->nBits == 16)
    {
        bf.bfOffBits += 4 * sizeof(unsigned int);
    }
    else if (pMIImg->nBits == 8)
    {
        bf.bfOffBits += 256 * sizeof(RGBQUAD);
    }
    if (sizeof(bf) != fwrite(&bf, 1, sizeof(bf), stream))
    {
        fclose(stream);
        return 0;
    }

    bi.biSize = sizeof(BITMAPINFOHEADER);
    bi.biWidth = pMIImg->nImgW;
    bi.biHeight = pMIImg->nImgH;
    bi.biBitCount = (unsigned short)pMIImg->nBits;
    bi.biPlanes = 1;
    if (pMIImg->nBits == 16)
    {
        bi.biCompression = 3L;
    }
    if ( sizeof(BITMAPINFOHEADER) != fwrite(&bi, 1, sizeof(BITMAPINFOHEADER), stream))
    {
        fclose(stream);
        return 0;
    }

    if (pMIImg->nBits == 16)
    {
        unsigned int mask[4] = {0xF800, 0x7E0, 0x1F, 0};
        if ( sizeof(unsigned int) != fwrite(mask, 4, sizeof(unsigned int), stream))
        {
            fclose(stream);
            return 0;
        }
    }
    else if (pMIImg->nBits == 8)
    {
        RGBQUAD rgb[256];
        for (i = 0; i < 256; i++)
        {
            rgb[i].rgbBlue  = (unsigned char)i;
            rgb[i].rgbGreen = (unsigned char)i;
            rgb[i].rgbRed   = (unsigned char)i;
            rgb[i].rgbReserved  = 0;
        }
        if (sizeof(RGBQUAD) != fwrite(rgb, 256, sizeof(RGBQUAD), stream))
        {
            fclose(stream);
            return 0;
        }
    }

    imgLinebytes = pMIImg->nImgW * pMIImg->nBits / 8;
    p = pMIImg->pData + (pMIImg->nImgH - 1) * (imgLinebytes);
    imgLinebytes = pMIImg->nImgW * pMIImg->nBits / 8;
    p = pMIImg->pData + (pMIImg->nImgH - 1) * (imgLinebytes);
    int nFill = nLinebytes - imgLinebytes;

    for (i = 0; i < pMIImg->nImgH; i++, p -= (imgLinebytes))
    {
        if (imgLinebytes != fwrite(p, 1, imgLinebytes, stream))
        {
            fclose(stream);
            SafeFree(pTmp);
            return 0;
        }
        if (nFill > 0)
        {
            pTmp = (unsigned char*)malloc(nFill);
            memset(pTmp, 0, nFill);
            if (nFill != fwrite(pTmp, 1, nLinebytes - imgLinebytes, stream))
            {
                fclose(stream);
                SafeFree(pTmp);
                return 0;
            }
            SafeFree(pTmp);
        }
    }
    fclose(stream);
    return 1;
}


/*!
    \fn     change the colour image to the gray image
    \param  pDstImg, the destination gray image.
    \param  pSrcImg, the source color image.
    \return 0 successful, or else failed
 */
int BmpRGB2Gray(SimpleBmpImage* pDstImg, SimpleBmpImage* pSrcImg)
{
    int row, col;
    int srcLinebytes;
    int dstLinebytes;
    unsigned char* pSrc;
    unsigned char* pDst;

    if ( (!pDstImg) || (!pSrcImg) || (true != pSrcImg->bValid) )
    {
        return -1;
    }
    if ((true == pDstImg->bCreateMem) && (true != pDstImg->bValid) )
    {
        SafeFree(pDstImg->pData);
        pDstImg->bCreateMem = false;
    }

    if (true != pDstImg->bValid)
    {
        pDstImg->nBits = 8;
        pDstImg->nImgW = pSrcImg->nImgW;
        pDstImg->nImgH = pSrcImg->nImgH;
        pDstImg->pData = (unsigned char*)malloc(sizeof(unsigned char) * pDstImg->nImgW * pDstImg->nImgH);
        pDstImg->bCreateMem = true;
    }

    if (!pDstImg->pData)
    {
        return -1;
    }

    switch (pSrcImg->nBits)
    {
        case 8:
            memcpy(pDstImg->pData, pSrcImg->pData, pSrcImg->nImgH * pSrcImg->nImgW);
            break;
        case 24:
            srcLinebytes = 3 * pSrcImg->nImgW;
            dstLinebytes = pDstImg->nImgW;
            for (row = 0; row < pSrcImg->nImgH; ++row)
            {
                for (col = 0; col < pSrcImg->nImgW; ++col)
                {
                    pDst = pDstImg->pData + row * dstLinebytes + col;
                    pSrc = pSrcImg->pData + row * srcLinebytes + 3 * col;
                    //*(pDst) = (unsigned char)(0.114*pSrc[0] + 0.587*pSrc[1] + 0.299*pSrc[2]);
                    *(pDst) = (unsigned char)((int(7471) * pSrc[0] + int(38470) * pSrc[1] + int(19595) * pSrc[2]) >> 16);
                }
            }
            break;
        case 32:
            srcLinebytes = 4 * pSrcImg->nImgW;
            dstLinebytes = pDstImg->nImgW;
            for (row = 0; row < pSrcImg->nImgH; ++row)
            {
                for (col = 0; col < pSrcImg->nImgW; ++col)
                {
                    pDst = pDstImg->pData + row * dstLinebytes + col;
                    pSrc = pSrcImg->pData + row * srcLinebytes + 4 * col;
                    *(pDst) = (unsigned char)(0.114 * pSrc[0] + 0.587 * pSrc[1] + 0.299 * pSrc[2]);
                }
            }
            break;
    }

    pDstImg->bValid = true;
    return 0;
}
int BmpInitWithPtr(SimpleBmpImage* pMIImg, unsigned char* pImgSrc, int nImgW, int nImgH, int nBits)
{
    if ( (!pMIImg) || (!pImgSrc) || (nImgW <= 0) || (nImgH <= 0) )
    {
        return -1;
    }
    if (true == pMIImg->bCreateMem)
    {
        SafeFree(pMIImg->pData);
    }
    pMIImg->bCreateMem = false;
    pMIImg->bValid = true;
    pMIImg->nBits = nBits;
    pMIImg->nImgH = nImgH;
    pMIImg->nImgW = nImgW;
    pMIImg->pData = pImgSrc;

    return 0;
}

int BmpInitWithData(SimpleBmpImage* pMIImg, unsigned char* pImgSrc, int nImgW, int nImgH, int nBits)
{
    if ( (!pMIImg) || (!pImgSrc) || (nImgW <= 0) || (nImgH <= 0) )
    {
        return -1;
    }
    if (true == pMIImg->bCreateMem)
    {
        if (pMIImg->nImgH != nImgH || pMIImg->nImgW != nImgW || pMIImg->nBits != nBits)
        {
            SafeFree(pMIImg->pData);
            pMIImg->bCreateMem = false;
        }
    }

    if (pMIImg->bCreateMem == false)
    {
        pMIImg->bValid = true;
        pMIImg->nBits = nBits;
        pMIImg->nImgH = nImgH;
        pMIImg->nImgW = nImgW;
        pMIImg->pData = (unsigned char*)malloc(sizeof(unsigned char) * nImgW * nImgH * (nBits >> 3));
        pMIImg->bCreateMem = true;
    }
    memcpy(pMIImg->pData, pImgSrc, sizeof(unsigned char) * nImgW * nImgH * (nBits >> 3));

    return 0;
}

/*!
    \fn     draw Rectangle in specified image
    \param  pImg, the image struct
    \param  rect, the rectangle area is to be draw
    \param  nThickness, the thickness of pen
    \param  r, the value of red
    \param  g, the value of green
    \param  b, the value of blue
    \return 0 successful, or else failed
 */
int BmpDrawRect(SimpleBmpImage* pImg, DRAW_RECT rect, int nThickness/*=1*/, unsigned char r/*=255*/, unsigned char g/*=0*/, unsigned char b/*=0*/)
{
    if (!BmpValidRect(rect, pImg->nImgW, pImg->nImgH))
    {
        return -1;
    }

    int ret = BmpDrawHLine(pImg, rect.left, rect.right, rect.top, nThickness, r, g, b) &
              BmpDrawHLine(pImg, rect.left, rect.right, rect.bottom, nThickness, r, g, b) &
              BmpDrawVLine(pImg, rect.top, rect.bottom, rect.left, nThickness, r, g, b) &
              BmpDrawVLine(pImg, rect.top, rect.bottom, rect.right, nThickness, r, g, b);
    if (ret != 0)
    {
        return -1;
    }

    return 0;
}

/*!
    \fn     whether the rectangle area is valid
    \param  rect, the rectangle area
    \param  nImgw, the width of image
    \param  nImgH, the height of image
    \return true is valid, or else is invalid
 */
bool BmpValidRect(DRAW_RECT rect, int nImgW, int nImgH)
{
    if (   (rect.left < 0) || (rect.left >= (nImgW - 1))
           || (rect.top < 0)  || (rect.top >= (nImgH - 1))
           || (rect.right <= 0) || (rect.right >= nImgW)
           || (rect.bottom <= 0) || (rect.bottom >= nImgH))
    {
        return false;
    }
    return true;
}

/*!
    \fn     initialize the image object with other image object
    \param  pDstImg, the destination image object
    \param  pSrcImg, the source image object
    \return 0 successful, or else failed
 */
int BmpImageClone(SimpleBmpImage* pDstImg, SimpleBmpImage* pSrcImg)
{
    if ( (!pDstImg) || (!pSrcImg) || (false == pSrcImg->bValid))
    {
        return -1;
    }

    if (true == pDstImg->bCreateMem)
    {
        SafeFree(pDstImg->pData);
    }
    pDstImg->nBits = pSrcImg->nBits;
    pDstImg->nImgH = pSrcImg->nImgH;
    pDstImg->nImgW = pSrcImg->nImgW;
    pDstImg->pData = (unsigned char*)malloc(sizeof(unsigned char) * pSrcImg->nImgW * pSrcImg->nImgH * (pSrcImg->nBits >> 3));
    if (!pDstImg->pData)
    {
        return -2;
    }
    pDstImg->bCreateMem = true;
    memcpy(pDstImg->pData, pSrcImg->pData, sizeof(unsigned char) * pSrcImg->nImgW * pSrcImg->nImgH * (pSrcImg->nBits >> 3));
    pDstImg->bValid = true;

    return 0;
}

/*!
    \fn     draw line in specifiedn image
    \param  row1, the begin row of line
    \param  row2, the end row of line
    \param  col, the column of line
    \param  nThickness, the thickness of pen
    \param  r, the value of red
    \param  g, the value of green
    \param  b, the value of blue
    \return 0 successful, or else failed
 */
int BmpDrawVLine(SimpleBmpImage* pMIImg, int row1, int row2, int col, int nThickness/*=1*/, unsigned char r/*=255*/, unsigned char g/*=0*/, unsigned char b/*=0*/)
{
    int nHalfThickness = nThickness / 2;
    int nCol = col;
    int nRow;
    int i;
    int nLinebyte = pMIImg->nImgW * (pMIImg->nBits >> 3);
    unsigned char* pData = pMIImg->pData;
    for (i = -nHalfThickness; i <= nHalfThickness; ++i)
    {
        nCol = col + i;
        for (nRow = row1; nRow <= row2; ++nRow)
        {
            switch (pMIImg->nBits)
            {
                case 8:
                    *(pData + nRow * nLinebyte + nCol) = 255;
                    break;
                case 24:
                    *(pData + nRow * nLinebyte + nCol * 3) = b;
                    *(pData + nRow * nLinebyte + nCol * 3 + 1) = g;
                    *(pData + nRow * nLinebyte + nCol * 3 + 2) = r;
                    break;
                default:
                    break;
            }
        }
    }

    return 0;
}

/*!
    \fn     draw horizontal line in specifiedn image
    \param  col1, the begin column of line
    \param  col2, the end column of line
    \param  row, the row of line
    \param  nThickness, the thickness of pen
    \param  r, the value of red
    \param  g, the value of green
    \param  b, the value of blue
    \return 0 successful, or else failed
 */
int BmpDrawHLine(SimpleBmpImage* pMIImg, int col1, int col2, int row, int nThickness, unsigned char r/*=255*/, unsigned char g/*=0*/, unsigned char b/*=0*/)
{
    int nHalfThickness = nThickness >> 1;
    int nCol;
    int nRow = row;
    int i = 0;
    int nLinebyte = pMIImg->nImgW * (pMIImg->nBits >> 3);
    unsigned char* pData = pMIImg->pData;
    for (i = -nHalfThickness; i <= nHalfThickness; ++i)
    {
        nRow = row + i;
        for (nCol = col1; nCol <= col2; ++nCol)
        {
            switch (pMIImg->nBits)
            {
                case 8:
                    *(pData + nRow * nLinebyte + nCol) = 255;
                    break;
                case 24:
                    *(pData + nRow * nLinebyte + nCol * 3) = b;
                    *(pData + nRow * nLinebyte + nCol * 3 + 1) = g;
                    *(pData + nRow * nLinebyte + nCol * 3 + 2) = r;
                    break;
                default:
                    break;
            }
        }
    }

    return 0;
}

/*!
    \fn     draw point in specifiedn image
    \param  col, the column of point
    \param  row, the row of point
    \param  nRadius, the radius of point
    \param  nThickness, the thickness of pen
    \param  r, the value of red
    \param  g, the value of green
    \param  b, the value of blue
    \return 0 successful, or else failed
 */
int BmpDrawPoint(SimpleBmpImage* pMIImg, int row, int col, int nRadius, int nThickness, unsigned char r, unsigned char g, unsigned char b)
{
    int lineLast = pMIImg->nImgW - 1;
    int rowLast = pMIImg->nImgH - 1;
    if (row < 0)
    {
        row = 0;
    }
    if (row > rowLast)
    {
        row = rowLast;
    }

    if (col < 0)
    {
        col = 0;
    }
    if (col > lineLast)
    {
        col = lineLast;
    }

    int nLeftCol = col - nRadius;
    int nRightCol = col + nRadius;
    int nTopRow = row - nRadius;
    int nBottomRow = row + nRadius;

    nLeftCol = max(nLeftCol, 0);
    nTopRow = max(nTopRow, 0);
    nRightCol = min(nRightCol, lineLast);
    nBottomRow = min(nBottomRow, rowLast);

    int ret = BmpDrawHLine(pMIImg, nLeftCol, nRightCol, row, nThickness, r, g, b) &
              BmpDrawVLine(pMIImg, nTopRow, nBottomRow, col, nThickness, r, g, b);
    if (ret != 0)
    {
        return -1;
    }

    return 0;
}

/*!
    \fn     convert color image to gram image
    \param  pDstImg, the destination gray image buffer
    \param  pSrcImg, the source color image buffer
    \param  nWidth, the width of image
    \param  nHeight, the height of image
    \return true is successful, or else is failed
 */
bool BGR2Gray(unsigned char* pDstImg, unsigned char* pSrcImg, int nWidth, int nHeight)
{
    int nSize = nWidth * nHeight;
    for (int i = 0, j = 0; i < nSize; ++i, j += 3)
    {
        pDstImg[i] = (unsigned char)(pSrcImg[j] * 0.114 + pSrcImg[j + 1] * 0.587 + pSrcImg[j + 2] * 0.299);
    }

    return true;
}

/*!
    \fn     flip image in vertical direction
    \param  pDestImg, the destination image data buffer
    \param  pSrcImg, the source image data buffer
    \param  nWidth, the width of image
    \param  nHeight, the height of image
    \param  nSrcChannel, the channel of source image
    \param  nDestChannel, the channel of destination image
    \return true successful, or else failed
 */
bool FlipImageVertical(unsigned char * pDesImg, unsigned char * pSrcImg, int nWidth, int nHeight, \
                       int nSrcChannel, int nDestChannel)
{
    bool bRet = true;
    if (pDesImg == NULL || pSrcImg == NULL || nSrcChannel != nDestChannel)
    {
        bRet = false;
    }
    else
    {
        if (nSrcChannel == 1 || nSrcChannel == 3)
        {
            for (int i = 0; i < nHeight; ++i)
                memcpy(pDesImg + i * nWidth * nSrcChannel, pSrcImg + (nHeight - 1 - i) *\
                       nWidth * nSrcChannel, nWidth * nSrcChannel);
        }
        else
        {
            bRet = false;
        }
    }
    return bRet;
}


inline unsigned char RgbLimit(int input)
{
    unsigned char output;
    output = (unsigned char)((input < 0) ? 0 : ((input > 255) ? 255 : input));
    return output;
}

void rgb_to_ycc(unsigned char r, unsigned char g, unsigned char b, unsigned char *yp, unsigned char *cb, unsigned char *cr)
{
    int Y, Cb, Cr;
    /*
        fY  = 0.299*r + 0.587*g + 0.114*b;
        fCb = -0.16874*r - 0.33126*g + 0.5*b + 128;
        fCr = 0.5*r - 0.41869*g - 0.08131*b + 128;
    */

    Y  = (306 * r + 601 * g + 117 * b) >> 10;
    Cb = ((512 * b - 173 * r - 339 * g) + 128 * 1024) >> 10;
    Cr = ((512 * r - 429 * g - 83 * b) + 128 * 1024) >> 10;

    *yp = RgbLimit(Y);
    *cb = RgbLimit(Cb);
    *cr = RgbLimit(Cr);
}
void ycc_to_rgb(unsigned char y, unsigned char cb, unsigned char cr, unsigned char *r, unsigned char *g, unsigned char *b)
{
    //  fR = Y + 1.402*(Cr-128);
    //  fG = Y - 0.34414*(Cb-128) - 0.71414*(Cr-128);
    //  fB = Y + 1.772*(Cb-128);

    int tmpR, tmpG, tmpB;

    // 10bit shift fixed coding
    tmpR = y - 179 + ((1436 * cr)           >> 10);
    tmpG = y + 135 + ((-352 * cb  - 731 * cr) >> 10);
    tmpB = y - 227 + ((1815 * cb)           >> 10);

    *r = RgbLimit(tmpR);
    *g = RgbLimit(tmpG);
    *b = RgbLimit(tmpB);
}

// mode 0 : CbCr, NV12, mode 1 : CrCb, NV21
void BGR24_to_YUV420SP(unsigned char *pBGR24, int wd, int ht, unsigned char* pYUV420, int mode)
{
    int i, j, ySize;

    unsigned char cb, cr;
    unsigned char cb_00, cb_01, cb_10, cb_11;
    unsigned char cr_00, cr_01, cr_10, cr_11;
    unsigned char y_00, y_01, y_10, y_11;
    unsigned char r_00, r_01, r_10, r_11;
    unsigned char g_00, g_01, g_10, g_11;
    unsigned char b_00, b_01, b_10, b_11;

    unsigned char  *pTmpY = NULL;
    unsigned short *pTmpC = NULL; // HTH
    unsigned short TmpVal; // HTH
    unsigned char  *pTmpBGR = NULL;

    int iOff1, iOff2, jOff1, jOff2;

    ySize = wd * ht;

    pTmpY = pYUV420;
    pTmpC = (unsigned short*)(pYUV420 + ySize);
    for (i = 0 ; i < (ht >> 1) ; i++)
    {
        iOff1 = (i * wd) << 1; // 2*i*Wd
        iOff2 = iOff1 + wd; // (2*i+1)*Wd

        for (j = 0 ; j < (wd >> 1) ; j++)
        {
            jOff1 = (j << 1); // 2*j
            jOff2 = jOff1 + 1; // 2*j + 1

            pTmpBGR = pBGR24 + 3 * (iOff1 + jOff1);
            b_00 = *pTmpBGR++;
            g_00 = *pTmpBGR++;
            r_00 = *pTmpBGR ;
            rgb_to_ycc(r_00, g_00, b_00, &y_00, &cb_00, &cr_00);

            pTmpBGR = pBGR24 + 3 * (iOff1 + jOff2);
            b_01 = *pTmpBGR++;
            g_01 = *pTmpBGR++;
            r_01 = *pTmpBGR ;
            rgb_to_ycc(r_01, g_01, b_01, &y_01, &cb_01, &cr_01);

            pTmpBGR = pBGR24 + 3 * (iOff2 + jOff1);
            b_10 = *pTmpBGR++;
            g_10 = *pTmpBGR++;
            r_10 = *pTmpBGR ;
            rgb_to_ycc(r_10, g_10, b_10, &y_10, &cb_10, &cr_10);

            pTmpBGR = pBGR24 + 3 * (iOff2 + jOff2);
            b_11 = *pTmpBGR++;
            g_11 = *pTmpBGR++;
            r_11 = *pTmpBGR ;
            rgb_to_ycc(r_11, g_11, b_11, &y_11, &cb_11, &cr_11);

            pTmpY[iOff1 + jOff1] = y_00;
            pTmpY[iOff1 + jOff2] = y_01;
            pTmpY[iOff2 + jOff1] = y_10;
            pTmpY[iOff2 + jOff2] = y_11;

            cb = ((cb_00 + cb_01 + cb_10 + cb_11) >> 2);
            cr = ((cr_00 + cr_01 + cr_10 + cr_11) >> 2);

            if (mode)  // YCrCb --> (msb) cb | cr (lsb)
            {
                TmpVal = (unsigned short)cb;
                TmpVal = TmpVal << 8;
                TmpVal = TmpVal + cr;
                //TmpVal = TmpVal | 0xFF;
                //TmpVal = TmpVal & cr;
            }
            else  // YCbCr --> (msb) cr | cb (lsb)
            {
                TmpVal = (unsigned short)cr;
                TmpVal = TmpVal << 8;
                TmpVal = TmpVal + cb;
                //TmpVal = TmpVal | 0xFF;
                //TmpVal = TmpVal & cb;
            }
            *pTmpC++ = TmpVal;
        }
    }
}

// mode 0 : CbCr, NV12, mode 1 : CrCb, NV21
void YUV420SP_to_BGR24(unsigned char *pYUV420, int wd, int ht, unsigned char* pBGR24, int mode)
{
    int i, j, ySize;
    unsigned char cb, cr;
    unsigned char y_00, y_01, y_10, y_11;
    unsigned char r_00, r_01, r_10, r_11;
    unsigned char g_00, g_01, g_10, g_11;
    unsigned char b_00, b_01, b_10, b_11;

    unsigned char  *pTmpY = NULL;
    unsigned short *pTmpC = NULL; // HTH
    unsigned short TmpVal; // HTH
    unsigned char  *pTmpBGR = NULL;

    int iOff1, iOff2, jOff1, jOff2;

    ySize = wd * ht;

    pTmpY = pYUV420;
    pTmpC = (unsigned short*)(pYUV420 + ySize);
    for (i = 0 ; i < (ht >> 1) ; i++)
    {

        iOff1 = (i * wd) << 1; // 2*i*Wd
        iOff2 = iOff1 + wd; // (2*i+1)*Wd

        for (j = 0 ; j < (wd >> 1) ; j++)
        {

            jOff1 = (j << 1); // 2*j
            jOff2 = jOff1 + 1; // 2*j + 1

            y_00 = pTmpY[iOff1 + jOff1];
            y_01 = pTmpY[iOff1 + jOff2];
            y_10 = pTmpY[iOff2 + jOff1];
            y_11 = pTmpY[iOff2 + jOff2];

            TmpVal = pTmpC[i * (wd >> 1) + j];

            if (mode)  // YCrCb
            {
                cr = TmpVal & 0xff;// LSB 8-bit
                cb = (TmpVal >> 8) & 0xff; // MSB 8-bit
            }
            else  // YCbCr
            {
                cb = TmpVal & 0xff;
                cr = (TmpVal >> 8) & 0xff;
            }

            ycc_to_rgb(y_00, cb, cr, &r_00, &g_00, &b_00);
            ycc_to_rgb(y_01, cb, cr, &r_01, &g_01, &b_01);
            ycc_to_rgb(y_10, cb, cr, &r_10, &g_10, &b_10);
            ycc_to_rgb(y_11, cb, cr, &r_11, &g_11, &b_11);

            pTmpBGR = pBGR24 + 3 * (iOff1 + jOff1);
            *pTmpBGR++  = b_00;
            *pTmpBGR++  = g_00;
            *pTmpBGR    = r_00;

            pTmpBGR = pBGR24 + 3 * (iOff1 + jOff2);
            *pTmpBGR++  = b_01;
            *pTmpBGR++  = g_01;
            *pTmpBGR    = r_01;

            pTmpBGR = pBGR24 + 3 * (iOff2 + jOff1);
            *pTmpBGR++  = b_10;
            *pTmpBGR++  = g_10;
            *pTmpBGR    = r_10;

            pTmpBGR = pBGR24 + 3 * (iOff2 + jOff2);
            *pTmpBGR++  = b_11;
            *pTmpBGR++  = g_11;
            *pTmpBGR    = r_11;
        }
    }
}

void ResetColorLineLash(unsigned char *pImage, unsigned char *pMask, int wd, int ht, 
    unsigned char in_r, unsigned char in_g, unsigned char in_b, int os_flag)
{
	unsigned char in_Y, in_U, in_V;	
    if(os_flag)
        rgb_to_ycc(in_r, in_g, in_b, &in_Y, &in_U, &in_V);
    else
        rgb_to_ycc(in_r, in_g, in_b, &in_Y, &in_V, &in_U);

    int size = wd*ht;
    int half_ht = ht/2;
    int half_wd = wd/2;

	for(int i= 0; i < size; i ++)
    {
        if(pMask[i]!=0)
        {
            pImage[i] = in_Y;
        }
    }

	for(int y = 0; y < half_ht; y ++)
    {
        for(int x = 0; x < half_wd; x ++)
        {
            int n = size +  y*wd + x*2;
            if(pMask[y*2*wd+x*2]!=0 || pMask[y*2*wd+x*2 + 1]!=0 || 
                pMask[(y*2+ 1)*wd+x*2]!=0 || pMask[(y*2+ 1)*wd+x*2 + 1]!=0)
            {
                pImage[n] =  in_U ;
                pImage[n+1] = in_V ;
            }
        }
    }

}

void ResetColor(unsigned char *pImage, unsigned char *pMask, int wd, int ht, 
    unsigned char in_r, unsigned char in_g, unsigned char in_b, int os_flag)
{
    unsigned char in_Y, in_U, in_V;	
    if(!os_flag)
        rgb_to_ycc(in_r, in_g, in_b, &in_Y, &in_U, &in_V);
    else
        rgb_to_ycc(in_r, in_g, in_b, &in_Y, &in_V, &in_U);

    int size = wd*ht;
    int half_ht = ht/2;
    int half_wd = wd/2;

    float avg_y = 0.0;
    int numy = 0;
    for(int i= 0; i < size; i ++)
    {
        if(pMask[i]!=0)
        {
            avg_y += (pImage[i]);
            numy++;
        }
    }
    avg_y /= numy;
    float disy = in_Y - avg_y;

    float gamma_value = log(in_Y/255.)/log(avg_y/255.);
    if(gamma_value > 2.0)	gamma_value = 3.0;
    if(gamma_value < 0.5)	gamma_value = 0.33;

    for(int i= 0; i < size; i ++)
    {
        if(pMask[i]!=0)
        {
            pImage[i] = pow(float(pImage[i]/255.), gamma_value)*255;
        }
    }

    for(int y = 0; y < half_ht; y ++)
    {
        for(int x = 0; x < half_wd; x ++)
        {
            int n = size +  y*wd + x*2;
            if(pMask[y*2*wd+x*2]!=0)
            {
                pImage[n] =  in_U ;
                pImage[n+1] = in_V ;
            }
        }
    }
}

void DrawPtOnSingleMask(unsigned char* mask, int wd, int ht, int ptx, int pty, unsigned char val, int thickness)
{
    if (mask == NULL){
        return;
    }
    if (ptx < 0 || ptx >= wd || pty < 0 || pty >= ht){
        return;
    }


    float radius = thickness * 0.5f;

    int left = max(ptx - radius + 0.5f, 0);
    int right = min(ptx + radius + 0.5f, wd - 1);
    int top = max(pty - radius + 0.5f, 0);
    int bottom = min(pty + radius + 0.5f, ht - 1);

    radius *= radius;
    int uv_step = wd;
    int uv_wd = wd >> 1;

    unsigned char* mask_y = mask + top * wd + left;
    for (int y = top; y <= bottom; y++, mask_y += wd){
        unsigned char* mask_x = mask_y;
        for (int x = left; x <= right; x++, mask_x++){
            int difx = x - ptx;
            int dify = y - pty;
            if (difx * difx + dify * dify <= radius){
                *mask_y = val;
            }
        }
    }
}

void DrawLineOnSingleMask(unsigned char* mask, int wd, int ht, int start_x, int start_y, int end_x, int end_y, unsigned char val, int thickness)
{
    if (start_x == end_x && start_y == end_y){
        DrawPtOnSingleMask(mask, wd, ht, start_x, start_y, val, thickness);
        return;
    }

    int dx = end_x - start_x;
    int dy = end_y - start_y;

    if (abs(dx) > abs(dy)){
        float stepy = fabs((dy + 0.f) / dx);
        if (dy < 0){
            stepy = -stepy;
        }

        float cur_y = start_y;
        int stepx = 1;
        if (dx < 0){
            stepx = -1;
        }
        if (dx > 0){
            for (int x = start_x; x <= end_x; x += stepx, cur_y += stepy)
            {
                int xx = x;
                int yy = cur_y + 0.5f;
                DrawPtOnSingleMask(mask, wd, ht, xx, yy, val, thickness);
            }
        }
        else{
            for (int x = start_x; x >= end_x; x += stepx, cur_y += stepy)
            {
                int xx = x;
                int yy = cur_y + 0.5f;
                DrawPtOnSingleMask(mask, wd, ht, xx, yy, val, thickness);
            }
        }

    }
    else{

        float stepx = fabs((dx + 0.f) / dy);
        if (dx < 0){
            stepx = -stepx;
        }

        float cur_x = start_x;
        int stepy = 1;
        if (dy < 0){
            stepy = -1;
        }
        if (dy > 0){
            for (int y = start_y; y <= end_y; y += stepy, cur_x += stepx)
            {
                int xx = cur_x + 0.5;
                int yy = y;
                DrawPtOnSingleMask(mask, wd, ht, xx, yy, val, thickness);
            }
        }
        else{
            for (int y = start_y; y >= end_y; y += stepy, cur_x += stepx)
            {
                int xx = cur_x + 0.5;
                int yy = y;
                DrawPtOnSingleMask(mask, wd, ht, xx, yy, val, thickness);
            }
        }

    }
}
