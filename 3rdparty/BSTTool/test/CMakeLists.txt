cmake_minimum_required(VERSION 2.8.12)
project(BSTToolTest)
set(name BSTToolTest)
# set(CMAKE_CXX_FLAGS "-std=c++11 ${CMAKE_CXX_FLAGS}")

include_directories(
    ${CMAKE_CURRENT_SOURCE_DIR}/../src
    ${CMAKE_CURRENT_SOURCE_DIR}/../../Module
)

set(target_lib
    BSTTool
)
set(opencv_lib 
    opencv_core
    opencv_highgui
    opencv_imgproc
    opencv_imgcodecs
)

# opencv lib
if(ANDROID)
	set(OpenCV_DIR "${OpenCVPath}/sdk/native/jni")
	find_package(OpenCV REQUIRED)
	include_directories(${OpenCVPath}/sdk/native/jni/include)
	link_directories(${OpenCVPath}/sdk/native/staticlibs/${ANDROID_ABI})
else()
	set(OpenCV_DIR "/usr/local/share/OpenCV")
	find_package(OpenCV REQUIRED)
endif()

# target
file(GLOB_RECURSE source ${CMAKE_CURRENT_SOURCE_DIR}/*.cpp)
add_executable(${name} ${source} ../../../Module/Utils.cpp)
target_link_libraries(${name} ${target_lib} ${opencv_lib})
set_target_properties(${name} PROPERTIES LINK_FLAGS "-Wl,-unresolved-symbols=ignore-in-shared-libs")
       
# Install
if(ANDROID)
    install(TARGETS ${name} DESTINATION ${CMAKE_INSTALL_PREFIX}/${ANDROID_ABI}/bin)
else()
    install(TARGETS ${name} DESTINATION ${CMAKE_INSTALL_PREFIX}/x64/bin)
endif()