// This file is part of OpenCV project.
// It is subject to the license terms in the LICENSE file found in the top-level directory
// of this distribution and at http://opencv.org/license.html.
//
// Tencent is pleased to support the open source community by making WeChat QRCode available.
// Copyright (C) 2020 THL A29 Limited, a Tencent company. All rights reserved.
#include "precomp.hpp"
#include "decodermgr.hpp"
//#include "opencv2/opencv.hpp"
#include "../Util/TimeStatistic.h"
#include "BSTTool.h"
#include "morph.h"
extern int gBdecodeReturn;
using zxing::ArrayRef;
using zxing::BinaryBitmap;
using zxing::DecodeHints;
using zxing::ErrorHandler;
using zxing::LuminanceSource;
using zxing::Ref;
using zxing::Result;
using zxing::UnicomBlock;
namespace cv {
namespace qrcode_imp {
#if 0
int DecoderMgr::decodeImage(cv::Mat src, bool use_nn_detector, DecodeResult &decode_result, const params &paramsValue) {
    int width = src.cols;
    int height = src.rows;
    if (width <= 20 || height <= 20)
        return -1;  // image data is not enough for providing reliable results

    Ref<zxing::Result> zx_result;
    m_util.reset();
    decode_hints_.setUseNNDetector(use_nn_detector);
    TimerOutThld = paramsValue.decodeTimerOut;
    Ref<ImgSource> open_source;
    Ref<ImgSource> closed_source;
    qbarUicomBlock_ = new UnicomBlock(width, height);

    // Four Binarizers
    int tryBinarizeTime = paramsValue.numBinarizer;
    int kernelSize[] = {0,5,3};
    int tryKernelSize = paramsValue.numMorph;
    float during = 0.0f;
    for (int tb = 0; tb < tryBinarizeTime; tb++) {
        for (int tk = 0; tk < tryKernelSize; tk++) {
            m_util.reset();
            cv::Mat morphed_open_src(src.rows,src.cols,CV_8UC1);
            cv::Mat morphed_closed_src(src.rows, src.cols, CV_8UC1);
            if (kernelSize[tk] != 0){
                LOGI("openclose src w h =%d %d", src.cols, src.rows);
                //cv::Mat kernel = cv::getStructuringElement(cv::MORPH_RECT, cv::Size(kernelSize[tk], kernelSize[tk]));
                //cv::morphologyEx(src, morphed_open_src, cv::MORPH_OPEN, kernel);
                //cv::morphologyEx(src, morphed_closed_src, cv::MORPH_CLOSE, kernel);
                bstOpen(src.data, morphed_open_src.data, src.cols, src.rows, kernelSize[tk]);
                bstClose(src.data, morphed_closed_src.data, src.cols, src.rows, kernelSize[tk]);

            } else {
                src.copyTo(morphed_open_src);
                src.copyTo(morphed_closed_src);
            }
          //  if (gBdecodeReturn) break;
            pthread_mutex_lock(&qrcode_imp_mutex);
            std::vector<uint8_t> scaled_open_img_data(morphed_open_src.data, morphed_open_src.data + width * height);
            zxing::ArrayRef<uint8_t> scaled_open_img_zx =
                zxing::ArrayRef<uint8_t>(new zxing::Array<uint8_t>(scaled_open_img_data));
            
            std::vector<uint8_t> scaled_closed_img_data(morphed_closed_src.data, morphed_closed_src.data + width * height);
            zxing::ArrayRef<uint8_t> scaled_closed_img_zx =
                zxing::ArrayRef<uint8_t>(new zxing::Array<uint8_t>(scaled_closed_img_data));

            if (open_source == NULL || height * width > open_source->getMaxSize()) {
                open_source = ImgSource::create(scaled_open_img_zx.data(), width, height);
            } else {
                open_source->reset(scaled_open_img_zx.data(), width, height);
            }
            if (closed_source == NULL || height * width > closed_source->getMaxSize()) {
                closed_source = ImgSource::create(scaled_closed_img_zx.data(), width, height);
            } else {
                closed_source->reset(scaled_closed_img_zx.data(), width, height);
            }
            TimeUtils t1;
            t1.reset();
            int ret = TryDecode(open_source, closed_source, zx_result, paramsValue);
            if (!ret || zx_result!=0) {
                decode_result.corner_points.clear();
                if (zx_result->getResultPoints()->size() < 8)
                {
                    pthread_mutex_unlock(&qrcode_imp_mutex);
                    break;
                }
                int isErrorPoxy = 0;
                for (int p = 0; p < 8; p++)
                {
                    auto resultPoints = zx_result->getResultPoints()[p];
                    if (resultPoints == 0)break;
                    float px = (zx_result->getResultPoints()[p]->getX());
                    float py = zx_result->getResultPoints()[p]->getY();
                    // subtract back the padding size
                    px -= paramsValue.whitePaddingWidth;
                    py -= paramsValue.whitePaddingWidth;
                    // TODO: modify
                    if (px < 0 || py < 0)
                    {
                        isErrorPoxy = 1;
                        break;
                    }
                    
                    PointXY tmp_point(px, py);
                    decode_result.corner_points.push_back(tmp_point);
                    // float radius = (src.cols + src.rows)/(2*50);
                    // cv::circle(img_show, Point(px, py), radius, Scalar(0, 255, 0), -1);
                }
                // BstCE::BSTTool::SaveImagePNG("/sdcard/finder_pattern_point", img_show.data, img_show.cols, img_show.rows, img_show.channels());
                // imwrite("/sdcard/finder_pattern_point.jpg", img_show);
                // imshow("point", img_show);
                // waitKey(0);
                if (isErrorPoxy == 0)
                {
                    decode_result.decode_text = zx_result->getText()->getText();
                    decode_result.module_size = zx_result->getModuleSize();
                    decode_result.decode_score = zx_result->getDecodeScore();
                    decode_result.qrcode_version = zx_result->getQRCodeVersion();
                    // LOGI("module size = %f \n", decode_result.module_size);
                    decode_result.binarize_idx = tb;
                    decode_result.morph_kernel_idx = tk;
                    pthread_mutex_unlock(&qrcode_imp_mutex);
                    return ret;
                }
                else
                    ret = 1;
            }
            during += m_util.get_time();
            pthread_mutex_unlock(&qrcode_imp_mutex);
            if (during >= TimerOutThld)
            {
                LOGE("during = %f,decode Timer Out !!,time out = %f",during, TimerOutThld);
                //LOGE("decode timer : %f", t1.get_time());
                return  -1;
            }
            // try different binarizers
        }
        binarizer_mgr_.SwitchBinarizer();
    }
    return -1;
}
#endif
int DecoderMgr::decodeImage(CGrayImage* src, bool use_nn_detector, DecodeResult& decode_result, const params& paramsValue) {
    int width = src->GetWidth();
    int height = src->GetHeight();
    if (width <= 20 || height <= 20) {
        return -1; // image data is not enough for providing reliable results
    }

    Ref<zxing::Result> zx_result;
    m_util.reset();
    decode_hints_.setUseNNDetector(use_nn_detector);
    TimerOutThld = paramsValue.decodeTimerOut;
    Ref<ImgSource> open_source;
    Ref<ImgSource> closed_source;
    qbarUicomBlock_ = new UnicomBlock(width, height);

    // Four Binarizers
    int tryBinarizeTime = paramsValue.numBinarizer;
    int kernelSize[] = { 0,5,3 };
    int tryKernelSize = paramsValue.numMorph;
    float during = 0.0f;
    for (int tb = 0; tb < tryBinarizeTime; tb++) {
        for (int tk = 0; tk < tryKernelSize; tk++) {
            m_util.reset();
            CGrayImage morphed_open_src;
            morphed_open_src.Create(width, height);
            CGrayImage morphed_closed_src;
            morphed_closed_src.Create(width, height);
            if (kernelSize[tk] != 0) {
                bstOpen(src->GetImageData(), morphed_open_src.GetImageData(), width, height, kernelSize[tk]);
                bstClose(src->GetImageData(), morphed_closed_src.GetImageData(), width, height, kernelSize[tk]);
            }
            else {
                memcpy(morphed_open_src.GetImageData(), src->GetImageData(), width * height);
                memcpy(morphed_closed_src.GetImageData(), src->GetImageData(), width * height);
            }
           // AutoLock locker(gsample_auto_mutex);
            pthread_mutex_lock(&qrcode_imp_mutex);
            std::vector<uint8_t> scaled_open_img_data(morphed_open_src.GetImageData(), morphed_open_src.GetImageData() + width * height);
            zxing::ArrayRef<uint8_t> scaled_open_img_zx =
                zxing::ArrayRef<uint8_t>(new zxing::Array<uint8_t>(scaled_open_img_data));

            std::vector<uint8_t> scaled_closed_img_data(morphed_closed_src.GetImageData(), morphed_closed_src.GetImageData() + width * height);
            zxing::ArrayRef<uint8_t> scaled_closed_img_zx =
                zxing::ArrayRef<uint8_t>(new zxing::Array<uint8_t>(scaled_closed_img_data));

            if (open_source == NULL || height * width > open_source->getMaxSize()) {
                open_source = ImgSource::create(scaled_open_img_zx.data(), width, height);
            }
            else {
                open_source->reset(scaled_open_img_zx.data(), width, height);
            }
            if (closed_source == NULL || height * width > closed_source->getMaxSize()) {
                closed_source = ImgSource::create(scaled_closed_img_zx.data(), width, height);
            }
            else {
                closed_source->reset(scaled_closed_img_zx.data(), width, height);
            }
            TimeUtils t1;
            t1.reset();
            int ret = TryDecode(open_source, closed_source, zx_result, paramsValue);
            if (!ret || zx_result != 0) {
                decode_result.corner_points.clear();
                if (zx_result->getResultPoints()->size() < 8) {
                    pthread_mutex_unlock(&qrcode_imp_mutex);
                    break;
                }
                int isErrorPoxy = 0;
                for (int p = 0; p < 8; p++) {
                    auto resultPoints = zx_result->getResultPoints()[p];
                    if (resultPoints == 0)break;
                    float px = zx_result->getResultPoints()[p]->getX();
                    float py = zx_result->getResultPoints()[p]->getY();
                    // subtract back the padding size
                    px -= paramsValue.whitePaddingWidth;
                    py -= paramsValue.whitePaddingWidth;

                    PointXY tmp_point(px, py);
                    decode_result.corner_points.push_back(tmp_point);
                }
                if (isErrorPoxy == 0) {
                    decode_result.decode_text = zx_result->getText()->getText();
                    decode_result.module_size = zx_result->getModuleSize();
                    decode_result.decode_score = zx_result->getDecodeScore();
                    decode_result.qrcode_version = zx_result->getQRCodeVersion();
                    decode_result.binarize_idx = tb;
                    decode_result.morph_kernel_idx = tk;
                    pthread_mutex_unlock(&qrcode_imp_mutex);
                    return ret;
                }
                else
                    ret = 1;
            }
            during += m_util.get_time();
            pthread_mutex_unlock(&qrcode_imp_mutex);
            if (during >= TimerOutThld) {
                // LOGE("during = %f,decode Timer Out !!,time out = %f", during, TimerOutThld);
                // LOGE("decode timer : %f", t1.get_time());
                return  -1;
            }
            // try different binarizers
        }
        binarizer_mgr_.SwitchBinarizer();
    }
    return -1;
}

#if defined(WIN32)
#include <windows.h>
#endif
int DecoderMgr::TryDecode(Ref<LuminanceSource> open_source, Ref<LuminanceSource> closed_source, 
    Ref<Result>& result, const params &paramsValue) {
    int res = -1;
    // get binarizer
    zxing::Ref<zxing::Binarizer> open_binarizer = binarizer_mgr_.Binarize(open_source);
    zxing::Ref<zxing::BinaryBitmap> open_binary_bitmap(new BinaryBitmap(open_binarizer));
    open_binary_bitmap->m_poUnicomBlock = qbarUicomBlock_;
    // get binarizer
    zxing::Ref<zxing::Binarizer> closed_binarizer = binarizer_mgr_.Binarize(closed_source);
    zxing::Ref<zxing::BinaryBitmap> closed_binary_bitmap(new BinaryBitmap(closed_binarizer));
    closed_binary_bitmap->m_poUnicomBlock = qbarUicomBlock_;

    result = Decode(open_binary_bitmap, closed_binary_bitmap, decode_hints_, paramsValue);
    res = (result == NULL) ? 1 : 0;
    if (res == 0) {
        result->setBinaryMethod(int(binarizer_mgr_.GetCurBinarizer()));
    }
    return res;
}

Ref<Result> DecoderMgr::Decode(Ref<BinaryBitmap> open_image, Ref<BinaryBitmap> closed_image, DecodeHints hints, const params &paramsValue) {
    return reader_->decode(open_image, closed_image, hints, paramsValue);
}
}  // namespace qrcode_imp
}  // namespace cv