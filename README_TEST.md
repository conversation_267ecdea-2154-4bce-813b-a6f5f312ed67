# QR Code Scanner Test Documentation

## 概述

本文档描述了为 `test_wechat_linux.cpp` 创建的测试脚本和代码优化。

## 文件说明

### 1. 测试脚本 (`test_qrcode.sh`)

一个全功能的 shell 测试脚本，提供以下功能：

- **自动环境检查**: 验证可执行文件是否存在
- **测试数据管理**: 自动创建测试目录和配置文件
- **彩色输出**: 使用颜色区分不同类型的消息
- **错误处理**: 完善的错误检查和用户友好的错误消息
- **帮助系统**: 详细的使用说明和示例

#### 使用方法

```bash
# 显示帮助信息
./test_qrcode.sh help

# 测试文件夹中的图片
./test_qrcode.sh folder

# 测试单张图片（未实现）
./test_qrcode.sh single
```

### 2. 优化后的测试程序 (`test_wechat_linux.cpp`)

对原始测试程序进行了大幅优化：

#### 新增功能

- **详细的日志输出**: 包含时间戳和分类的日志信息
- **参数验证**: 完整的输入参数验证
- **错误处理**: 异常捕获和资源清理
- **性能监控**: 处理时间统计和性能分析
- **配置文件搜索**: 多路径配置文件查找
- **内存管理**: 改进的内存分配和释放
- **帮助系统**: 内置的帮助和使用说明

#### 输出示例

```
[INFO] QR Code Scanner Test Program
[INFO] Build time: Aug 20 2025 13:41:16
[INFO] Start time: 2025-08-20 13:43:03
[INFO] Parameters:
  Input path: ./test_images
  Input type: folder
  Dimensions: 0x0
  Image type: jpg
[INFO] Parameter validation passed
[INFO] 2025-08-20 13:43:03 - Starting folder demo scanner
[INFO] Image directory: ./test_images
[INFO] Image type: jpg
[INFO] Found 5 files
[INFO] Using config file: ../config/config.cfg
[INFO] QR scanner initialized successfully
[PROGRESS] 1/5 Processing: ./test_images/qr1.jpg
[SUCCESS] Found 1 QR code(s) in 45ms:
  QR 1: "Hello World"
    Position: (100,50) to (200,150)
============================================================
[SUMMARY] QR Code Detection Results
============================================================
Total files processed: 5/5
Successful detections: 3
Success ratio: 60.00%
Total processing time: 234ms
Average time per image: 46ms
Timestamp: 2025-08-20 13:43:04
============================================================
```

## 项目结构

```
qrcode_sdk/
├── test_qrcode.sh          # 测试脚本
├── test/
│   └── test_wechat_linux.cpp  # 优化后的测试程序
├── build/
│   └── QRCodeExc           # 编译后的可执行文件
├── config/
│   └── config.cfg          # 配置文件（自动生成）
├── test_data/              # 测试图片目录（自动创建）
│   └── README.txt          # 测试数据说明
└── README_TEST.md          # 本文档
```

## 使用步骤

### 1. 构建项目

```bash
cd qrcode_sdk
mkdir -p build
cd build
../build_pc.sh
make -j$(nproc)
```

### 2. 准备测试数据

将 QR 码图片放入 `test_data/` 目录：

```bash
# 创建测试目录（脚本会自动创建）
mkdir -p test_data

# 添加测试图片
cp your_qr_images/*.jpg test_data/
```

### 3. 运行测试

```bash
# 使用测试脚本（推荐）
./test_qrcode.sh folder

# 或直接运行程序
cd build
./QRCodeExc ../test_data folder 0 0 jpg
```

## 配置文件

程序会在以下位置搜索配置文件：

1. `../Data/config/qr.cfg`
2. `../config/config.cfg`
3. `./config/config.cfg`
4. `config.cfg`

如果找不到配置文件，测试脚本会自动创建一个基本的配置文件。

## 支持的图片格式

- **JPG/JPEG**: 推荐格式，自动检测尺寸
- **Gray**: 原始灰度二进制文件，需要指定宽度和高度

## 错误处理

程序包含完善的错误处理：

- 文件/目录不存在检查
- 内存分配失败处理
- 图片加载失败处理
- 参数验证
- 异常捕获

## 性能特性

- 多线程支持（通过 OpenMP）
- 内存池管理
- 处理时间统计
- 批量处理优化

## 故障排除

### 常见问题

1. **可执行文件不存在**
   ```
   [ERROR] Executable not found: /path/to/QRCodeExc
   ```
   解决方案：重新构建项目

2. **配置文件未找到**
   ```
   [WARNING] Config file not found
   ```
   解决方案：运行测试脚本会自动创建配置文件

3. **没有测试图片**
   ```
   [ERROR] No test images available
   ```
   解决方案：将 QR 码图片添加到 `test_data/` 目录

### 调试模式

可以通过修改代码中的日志级别来获得更详细的调试信息。

## 扩展功能

### 计划中的功能

- 单张图片处理模式
- 摄像头实时处理模式
- 批量处理报告生成
- 性能基准测试
- 配置文件热重载

### 自定义配置

可以修改 `config/config.cfg` 文件来调整检测参数：

```ini
# 检测阈值
detection_threshold=0.5

# QR码尺寸范围
min_qr_size=20
max_qr_size=1000

# 超时设置
decode_timeout=5000
```

## 贡献

如需改进测试脚本或程序，请：

1. 保持代码风格一致
2. 添加适当的错误处理
3. 更新文档
4. 测试所有功能

## 许可证

遵循项目原有许可证。
