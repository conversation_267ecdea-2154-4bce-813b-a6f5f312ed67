#include <iostream>
#include <chrono>
#include <iomanip>
#include <sys/stat.h>

#include "utils.h"
#include "bst_QR_scanner.h"
#include "opencv2/opencv.hpp"

using namespace std;
using namespace cv;
using namespace BstCE;

// Utility functions
bool fileExists(const string& filename) {
    struct stat buffer;
    return (stat(filename.c_str(), &buffer) == 0);
}

bool directoryExists(const string& dirname) {
    struct stat buffer;
    return (stat(dirname.c_str(), &buffer) == 0 && S_ISDIR(buffer.st_mode));
}

string getCurrentTimestamp() {
    auto now = chrono::system_clock::now();
    auto time_t = chrono::system_clock::to_time_t(now);
    stringstream ss;
    ss << put_time(localtime(&time_t), "%Y-%m-%d %H:%M:%S");
    return ss.str();
}

// Read raw grayscale image data from binary file
unsigned char* readGrayImageBinary(const char* imgpath, int width, int height) {
    if (!fileExists(imgpath)) {
        cout << "[ERROR] Gray image file not found: " << imgpath << endl;
        return nullptr;
    }
    
    unsigned char* srcImg = (unsigned char*)malloc(width * height);
    if (!srcImg) {
        cout << "[ERROR] Failed to allocate memory for image" << endl;
        return nullptr;
    }
    
    FILE* fp = fopen(imgpath, "rb");
    if (!fp) {
        cout << "[ERROR] Failed to open gray image file: " << imgpath << endl;
        free(srcImg);
        return nullptr;
    }
    
    size_t bytesRead = fread(srcImg, 1, width * height, fp);
    fclose(fp);
    
    if (bytesRead != (size_t)(width * height)) {
        cout << "[WARNING] Expected " << (width * height) << " bytes, but read " << bytesRead << " bytes" << endl;
    }
    
    return srcImg;
}

// Find config file
string findConfigFile() {
    vector<string> paths = {
        "../Data/config/qr.cfg",
        "../config/config.cfg", 
        "./config/config.cfg",
        "config.cfg"
    };
    
    for (const string& path : paths) {
        if (fileExists(path)) {
            cout << "[INFO] Using config file: " << path << endl;
            return path;
        }
    }
    
    cout << "[WARNING] Config file not found. Using default: " << paths[0] << endl;
    return paths[0];
}

// Main folder scanning function
void folderDemoScanner(const string& imgDir, int width, int height, const string& imgType) {
    cout << "[INFO] " << getCurrentTimestamp() << " - Starting folder demo scanner" << endl;
    cout << "[INFO] Image directory: " << imgDir << endl;
    cout << "[INFO] Image type: " << imgType << endl;
    
    // Validate directory
    if (!directoryExists(imgDir)) {
        cout << "[ERROR] Directory does not exist: " << imgDir << endl;
        return;
    }
    
    // Get file list
    vector<string> filenames;
    FileUtils::GetFileNames(imgDir, filenames);
    
    if (filenames.empty()) {
        cout << "[ERROR] No files found in directory: " << imgDir << endl;
        return;
    }
    
    cout << "[INFO] Found " << filenames.size() << " files" << endl;
    
    // Initialize scanner
    string configFile = findConfigFile();
    BST_QR_SCANNER* scanner = nullptr;
    
    try {
        scanner = new BST_QR_SCANNER(configFile.c_str());
        cout << "[INFO] QR scanner initialized successfully" << endl;
    } catch (...) {
        cout << "[ERROR] Failed to initialize QR scanner" << endl;
        return;
    }

    // Process images
    int successCount = 0;
    auto startTime = chrono::high_resolution_clock::now();
    
    for (size_t i = 0; i < filenames.size(); ++i) {
        cout << "[PROGRESS] " << (i + 1) << "/" << filenames.size() << " Processing: " << filenames[i] << endl;
        
        bst_QR_img_t qrImg = {0};
        bool imageLoaded = false;
        
        // Load image based on type
        if (imgType == "jpg") {
            Mat img = imread(filenames[i]);
            if (img.empty()) {
                cout << "[ERROR] Failed to load image: " << filenames[i] << endl;
                continue;
            }
            
            if (img.channels() == 3) {
                cvtColor(img, img, COLOR_BGR2GRAY);
            }

            qrImg.pimage = (unsigned char*)malloc(img.cols * img.rows);
            if (!qrImg.pimage) {
                cout << "[ERROR] Failed to allocate memory for image: " << filenames[i] << endl;
                continue;
            }
            
            memcpy(qrImg.pimage, img.data, img.cols * img.rows);
            qrImg.width = img.cols;
            qrImg.height = img.rows;
            qrImg.stride = img.cols;
            imageLoaded = true;
            
        } else if (imgType == "gray") {
            qrImg.pimage = readGrayImageBinary(filenames[i].c_str(), width, height);
            if (!qrImg.pimage) {
                cout << "[ERROR] Failed to load gray image: " << filenames[i] << endl;
                continue;
            }
            
            qrImg.width = width;
            qrImg.height = height;
            qrImg.stride = width;
            imageLoaded = true;
        } else {
            cout << "[ERROR] Unsupported image type: " << imgType << endl;
            continue;
        }

        if (!imageLoaded) {
            continue;
        }

        // Scan for QR codes
        bst_QR_input_t qrInput;
        qrInput.image = qrImg;

        vector<bst_QR_output_t> qrOutputs;
        auto scanStart = chrono::high_resolution_clock::now();
        int retVal = scanner->scan(&qrInput, qrOutputs);
        auto scanEnd = chrono::high_resolution_clock::now();
        
        auto scanDuration = chrono::duration_cast<chrono::milliseconds>(scanEnd - scanStart);
        
        // Process results
        if (!qrOutputs.empty()) {
            successCount++;
            cout << "[SUCCESS] Found " << qrOutputs.size() << " QR code(s) in " << scanDuration.count() << "ms:" << endl;
            
            for (size_t j = 0; j < qrOutputs.size(); ++j) {
                cout << "  QR " << (j + 1) << ": \"" << qrOutputs[j].decode_result << "\"" << endl;
                cout << "    Position: (" << qrOutputs[j].xmin << "," << qrOutputs[j].ymin 
                     << ") to (" << qrOutputs[j].xmax << "," << qrOutputs[j].ymax << ")" << endl;
            }
        } else {
            cout << "[INFO] No QR codes found in " << scanDuration.count() << "ms (return code: " << retVal << ")" << endl;
        }
        
        // Clean up
        if (qrImg.pimage) {
            free(qrImg.pimage);
            qrImg.pimage = nullptr;
        }
    }

    // Print summary
    auto endTime = chrono::high_resolution_clock::now();
    auto totalDuration = chrono::duration_cast<chrono::milliseconds>(endTime - startTime);
    
    cout << "\n" << string(60, '=') << endl;
    cout << "[SUMMARY] QR Code Detection Results" << endl;
    cout << string(60, '=') << endl;
    cout << "Total files processed: " << filenames.size() << endl;
    cout << "Successful detections: " << successCount << endl;
    cout << "Success ratio: " << fixed << setprecision(2) 
         << (filenames.size() > 0 ? (successCount * 100.0 / filenames.size()) : 0.0) << "%" << endl;
    cout << "Total processing time: " << totalDuration.count() << "ms" << endl;
    cout << "Average time per image: " 
         << (filenames.size() > 0 ? totalDuration.count() / filenames.size() : 0) << "ms" << endl;
    cout << "Timestamp: " << getCurrentTimestamp() << endl;
    cout << string(60, '=') << endl;

    // Clean up
    if (scanner) {
        delete scanner;
        cout << "[INFO] QR scanner cleaned up" << endl;
    }
}

// Show usage information
void showUsage(const char* programName) {
    cout << "\n" << string(60, '=') << endl;
    cout << "QR Code Scanner Test Program" << endl;
    cout << string(60, '=') << endl;
    cout << "Usage: " << programName << " <input_path> <input_type> <width> <height> <img_type>" << endl;
    cout << "\nParameters:" << endl;
    cout << "  input_path  - Path to input (file or directory)" << endl;
    cout << "  input_type  - Type of input: 'folder', 'camera', 'img'" << endl;
    cout << "  width       - Image width (for gray images, 0 for auto)" << endl;
    cout << "  height      - Image height (for gray images, 0 for auto)" << endl;
    cout << "  img_type    - Image format: 'jpg', 'gray'" << endl;
    cout << "\nExamples:" << endl;
    cout << "  " << programName << " ./test_images folder 0 0 jpg" << endl;
    cout << "  " << programName << " ./gray_data folder 640 480 gray" << endl;
    cout << string(60, '=') << endl;
}

// Validate parameters
bool validateParameters(const string& inputPath, const string& inputType, 
                       int width, int height, const string& imgType) {
    if (inputType != "folder" && inputType != "camera" && inputType != "img") {
        cout << "[ERROR] Invalid input_type: " << inputType << endl;
        return false;
    }
    
    if (imgType != "jpg" && imgType != "gray") {
        cout << "[ERROR] Invalid img_type: " << imgType << endl;
        return false;
    }
    
    if (imgType == "gray" && (width <= 0 || height <= 0)) {
        cout << "[ERROR] For gray images, width and height must be positive" << endl;
        return false;
    }
    
    if (inputType == "folder" && !directoryExists(inputPath)) {
        cout << "[ERROR] Directory does not exist: " << inputPath << endl;
        return false;
    }
    
    return true;
}

// Main function
int main(int argc, char** argv) {
    cout << "[INFO] QR Code Scanner Test Program" << endl;
    cout << "[INFO] Build time: " << __DATE__ << " " << __TIME__ << endl;
    cout << "[INFO] Start time: " << getCurrentTimestamp() << endl;
    
    if (argc == 2 && (string(argv[1]) == "-h" || string(argv[1]) == "--help")) {
        showUsage(argv[0]);
        return 0;
    }
    
    if (argc != 6) {
        cout << "[ERROR] Invalid number of arguments. Expected 5, got " << (argc - 1) << endl;
        showUsage(argv[0]);
        return -1;
    }

    string inputPath = argv[1];
    string inputType = argv[2];
    int width = atoi(argv[3]);
    int height = atoi(argv[4]);
    string imgType = argv[5];
    
    cout << "[INFO] Parameters:" << endl;
    cout << "  Input path: " << inputPath << endl;
    cout << "  Input type: " << inputType << endl;
    cout << "  Dimensions: " << width << "x" << height << endl;
    cout << "  Image type: " << imgType << endl;
    
    if (!validateParameters(inputPath, inputType, width, height, imgType)) {
        cout << "[ERROR] Parameter validation failed" << endl;
        showUsage(argv[0]);
        return -1;
    }

    cout << "[INFO] Parameter validation passed" << endl;

    try {
        if (inputType == "folder") {
            folderDemoScanner(inputPath, width, height, imgType);
        } else {
            cout << "[ERROR] Only folder input type is implemented" << endl;
            return -1;
        }
    } catch (...) {
        cout << "[ERROR] Exception occurred during processing" << endl;
        return -1;
    }

    cout << "[INFO] Program completed successfully at " << getCurrentTimestamp() << endl;
    return 0;
}
