// This file is part of OpenCV project.
// It is subject to the license terms in the LICENSE file found in the top-level directory
// of this distribution and at http://opencv.org/license.html.
//
// <PERSON><PERSON> is pleased to support the open source community by making WeChat QRCode available.
// Copyright (C) 2020 THL A29 Limited, a Tencent company. All rights reserved.

#ifndef __OPENCV_WECHAT_QRCODE_HPP__
#define __OPENCV_WECHAT_QRCODE_HPP__
#include "params_type.hpp"
#include "pthread.h"
#include "../ImgLib/GrayImage.h"
#include "../mathlib/Matrix.h"
#include "bst_std_wrapper.hpp"
#if defined(WIN32)
#include <windows.h>
#include <io.h>
#else
#include <unistd.h>
#define Sleep(MS) usleep((MS)*1000)
#include <dirent.h>
#endif
#if defined(ANDROID)
#include "lib_yuv/include/libyuv.h"
#endif
/** @defgroup qrcode_imp WeChat QR code detector for detecting and parsing QR code.
 */
namespace cv {
namespace qrcode_imp {

#if defined(ANDROID)|| defined(WIN32)
    typedef struct _tagInOutputParam {
        void* qrCode=nullptr;
        std::vector<float> scale;
        CGrayImage cInputImg;
        CMatrix cpoint;
        int track_id;
        int async_decode_verify_complete;
        params paramsValue;
        DecodeResult decodeResult;
        DecodeResult predecodeResult;
    }InOutputParam;

    typedef struct _tagInOutputParamDet {
        void* qrCode=nullptr;
        CGrayImage cimg;
        std::vector<CMatrix> cresult;
        int det_size;
        int openVerifyGammaCorrection;
    }InOutputParamDet;
#endif

//! @addtogroup qrcode_imp
//! @{
/**
 * @brief  WeChat QRCode includes two CNN-based models:
 * A object detection model and a super resolution model.
 * Object detection model is applied to detect QRCode with the bounding box.
 * super resolution model is applied to zoom in QRCode when it is small.
 *
 */
class CV_EXPORTS_W BstAIQRCode {
public:
    /**
     * @brief Initialize the BstAIQRCode.
     * It includes two models, which are packaged with caffe format.
     * Therefore, there are prototxt and caffe models (In total, four paramenters).
     *
     * @param detector_prototxt_path prototxt file path for the detector
     * @param detector_caffe_model_path caffe model file path for the detector
     * @param super_resolution_prototxt_path prototxt file path for the super resolution model
     * @param super_resolution_caffe_model_path caffe file path for the super resolution model
     */

    BstAIQRCode(const char* detector_prototxt_buffer,
                         size_t detector_prototxt_len,
                         const char* detector_caffe_model_buffer,
                         size_t detector_model_len,
                         const char* super_resolution_prototxt_buffer,
                         size_t super_resolution_prototxt_len,
                         const char* super_resolution_caffe_model_buffer,
                         size_t super_resolution_model_len,
                         const params &paramsValue = params(),
                         const char* modeDir=nullptr);

    CV_WRAP BstAIQRCode(const std::string& detector_prototxt_path = "",
        const std::string& detector_caffe_model_path = "",
        const std::string& super_resolution_prototxt_path = "",
        const std::string& super_resolution_caffe_model_path = "",
        const params& paramsValue = params());
    ~BstAIQRCode(){
#if defined(ANDROID)|| defined(WIN32)
        if (m_paramsValue.bUseAsyncDecode)
        {
            m_bThreadAliving = false;
#if defined(WIN32)
            Sleep(20);
#else
            usleep(20000);
#endif
            pthread_mutex_lock(&m_mutex);
            pthread_cond_signal(&m_cond);
            pthread_mutex_unlock(&m_mutex);

            pthread_join(m_thread, NULL);

            pthread_mutex_destroy(&m_mutex);
            pthread_cond_destroy(&m_cond);
        }
        if (m_paramsValue.bUseAsyncDet)
        {
            m_bThreadAliving_det = false;
#if defined(WIN32)
            Sleep(20);
#else
            usleep(20000);
#endif
            pthread_mutex_lock(&m_mutex_det);
            pthread_cond_signal(&m_cond_det);
            pthread_mutex_unlock(&m_mutex_det);

            pthread_join(m_thread_det, NULL);

            pthread_mutex_destroy(&m_mutex_det);
            pthread_cond_destroy(&m_cond_det);
        }
#endif
    };
    void reset()
    {
    #if defined(ANDROID)|| defined(WIN32)
        if (m_paramsValue.bUseAsyncDet)
        {
            m_bThreadAliving_det = false;
        }
        if (m_paramsValue.bUseAsyncDecode)
        {
            m_bThreadAliving = false;
        }
    #endif
    }
    /**
     * @brief  Both detects and decodes QR code.
     * To simplify the usage, there is a only API: detectAndDecode
     *
     * @param img supports grayscale or color (BGR) image.
     * @param points optional output array of vertices of the found QR code quadrangle. Will be
     * empty if not found.
     * @return list of decoded string.
     */
    CV_WRAP void setDetectInputSize(float size);
    // CV_WRAP std::vector<cv::Mat> detect(InputArray img, int det_size=400, int openVerifyGammaCorrection=0);
    CV_WRAP void setFrameNumber(int frame_number);
    CV_WRAP std::vector<CMatrix> detect(CGrayImage &img, int det_size=400, int openVerifyGammaCorrection=0);

    //CV_WRAP DecodeResult decode_v2(InputArray img, cv::Mat point, const params &paramsValue = params());
    CV_WRAP DecodeResult decode_v2(CGrayImage* img, CMatrix point, const params &paramsValue = params());

#if defined(ANDROID)||defined(WIN32)
   // CV_WRAP DecodeResult decode_v2_async(InputArray img, cv::Mat point, AsyncParam async_param, const params &paramsValue = params());
    CV_WRAP DecodeResult decode_v2_async(CGrayImage* img, CMatrix point, AsyncParam async_param, const params &paramsValue = params());
    CV_WRAP std::vector<CMatrix> detect_async(CGrayImage & img, int det_size = 400, int openVerifyGammaCorrection = 0);
    friend void* thread_decode_v2(void* para);
    friend void* thread_detect(void* para);
    bool  m_bThreadAliving=false;
    bool m_nInComputing = false;
    pthread_t m_thread;
    pthread_cond_t m_cond;
    pthread_mutex_t m_mutex;
    InOutputParam m_threadParam;

    bool  m_bThreadAliving_det = false;
    bool m_nInComputing_det = false;
    pthread_t m_thread_det;
    pthread_cond_t m_cond_det;
    pthread_mutex_t m_mutex_det;
    InOutputParamDet m_threadParam_det;
    int m_ErrorCode = 0;
    params m_paramsValue;
#endif
    char m_model_dir[256];
protected:
    class Impl;
    Ptr<Impl> p;
};
//! @}
}  // namespace qrcode_imp
}  // namespace cv
#endif  // __OPENCV_WECHAT_QRCODE_HPP__
