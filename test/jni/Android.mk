# Copyright (C) 2009 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

LOCAL_PATH := $(call my-dir)
SOLUTION_PATH := $(LOCAL_PATH)/../../

include $(CLEAR_VARS)
LOCAL_MODULE := bstQRCode 
ifeq ($(TARGET_ARCH_ABI),armeabi-v7a)
LOCAL_SRC_FILES := ../../src/libs/armeabi-v7a/libbstQRCode.so 
else ifeq ($(TARGET_ARCH_ABI),arm64-v8a)
LOCAL_SRC_FILES := ../../src/libs/arm64-v8a/libbstQRCode.so 
endif
include $(PREBUILT_SHARED_LIBRARY)

include $(CLEAR_VARS)
LOCAL_MODULE:=  opencv_shared
ifeq ($(TARGET_ARCH_ABI),armeabi-v7a)
LOCAL_SRC_FILES:= $(SOLUTION_PATH)/3rdparty/opencv_android/4.5.3/sdk/native/libs/armeabi-v7a/libopencv_java4.so
else ifeq ($(TARGET_ARCH_ABI),arm64-v8a)
LOCAL_SRC_FILES:= $(SOLUTION_PATH)/3rdparty/opencv_android/4.5.3/sdk/native/libs/arm64-v8a/libopencv_java4.so
endif
include $(PREBUILT_SHARED_LIBRARY)

TFLITE_PATH:= $(SOLUTION_PATH)/3rdparty/tensorflow2.10
include $(CLEAR_VARS)
LOCAL_MODULE:=  tensorflowlite
ifeq ($(TARGET_ARCH_ABI),armeabi-v7a)
LOCAL_SRC_FILES:= $(TFLITE_PATH)/libs/armeabi-v7a/libtensorflowlite.so
else ifeq ($(TARGET_ARCH_ABI),arm64-v8a)
LOCAL_SRC_FILES:= $(TFLITE_PATH)/libs/arm64-v8a/libtensorflowlite.so
endif
include $(PREBUILT_SHARED_LIBRARY)
#include $(SOLUTION_PATH)/3rdparty/opencv_android/4.5.3/sdk/native/jni/OpenCV_STATIC.mk
include $(CLEAR_VARS)
LOCAL_MODULE:=  bstbase
ifeq ($(TARGET_ARCH_ABI),armeabi-v7a)
LOCAL_SRC_FILES:= $(SOLUTION_PATH)/3rdparty/BSTbase/libs/armeabi-v7a/libBSTbase_share.so
else ifeq ($(TARGET_ARCH_ABI),arm64-v8a)
LOCAL_SRC_FILES:= $(SOLUTION_PATH)/3rdparty/BSTbase/libs/arm64-v8a/libBSTbase_share.so
endif
include $(PREBUILT_SHARED_LIBRARY)

include $(CLEAR_VARS)
LOCAL_MODULE     := Test
LOCAL_ARM_MODE := arm
EXCLUDE_FILES    := 
MY_PREFIX        := $(LOCAL_PATH)
ALL_SOURCES      := $(LOCAL_PATH)/../test_wechat.cpp
EXCLUDE_SRCS     := $(foreach filex, $(EXCLUDE_FILES), $(MY_PREFIX)/../$(filex))
MY_SOURCES       := $(filter-out $(EXCLUDE_SRCS), $(ALL_SOURCES))  
LOCAL_SRC_FILES  := $(MY_SOURCES:$(MY_PREFIX)/%=%)

LOCAL_CFLAGS     := -Wno-write-strings -fopenmp
LOCAL_CXXFLAGS   := -fopenmp

$(warning "TARGET_ARCH_ABI = $(TARGET_ARCH_ABI)")
ifeq ($(TARGET_ARCH_ABI),armeabi-v7a)
LOCAL_CFLAGS    += -mfloat-abi=softfp -mfpu=neon -march=armv7
LOCAL_LDLIBS := -llog 
else ifeq ($(TARGET_ARCH_ABI),arm64-v8a)
LOCAL_CFLAGS    += -march=armv8-a 
endif							
LOCAL_C_INCLUDES := $(SOLUTION_PATH) $(SOLUTION_PATH)/3rdparty 
LOCAL_C_INCLUDES += $(SOLUTION_PATH)/src/ $(SOLUTION_PATH)/3rdparty/opencv_android/4.5.3/sdk/native/jni/include #$(OPENCV_LOCAL_C_INCLUDES)
LOCAL_STATIC_LIBRARIES := #$(OPENCV_LOCAL_STATIC_LIBS) 
LOCAL_LDLIBS  +=  -static-libstdc++ 
LOCAL_SHARED_LIBRARIES := bstQRCode opencv_shared tensorflowlite #bstbase

APP_STL := c++_shared 
APP_CFLAGS := -fsanitize=address -fno-omit-frame-pointer
APP_LDFLAGS := -fsanitize=address
LOCAL_CLANG := true

#LOCAL_STATIC_LIBRARIES := libc
LOCAL_CFLAGS = -pie -fPIE  -fopenmp -DANDROID # $(OPENCV_LOCAL_CFLAGS)
LOCAL_LDFLAGS += -pie -fPIE
#LOCAL_CFLAGS += -DCL_USE_DEPRECATED_OPENCL_1_2_APIS  -DCL_USE_DEPRECATED_OPENCL_1_1_APIS
LOCAL_LDLIBS    += -llog -landroid -lz -pthread $(OPENCV_LOCAL_LDLIBS)
#LOCAL_LDLIBS    +=  -L$(SOLUTION_PATH)/OpenCL/arm64 -lOpenCL
#LOCAL_LDLIBS    += -fuse-ld=gold
include $(BUILD_EXECUTABLE)

