#include "bstQRCode.h"
#include "bst_QR_scanner.h"
#include "bstImgProc.h"
#include "../Util/TimeStatistic.h"
#include <iostream>
#include <vector>

#ifdef __ANDROID__
#include "sys/system_properties.h"
#include <string.h>
#endif
using namespace std;
#ifdef RUN_LINUX
int gBdecodeReturn = 0;
#else
extern int gBdecodeReturn ;
#endif
/*
(scale) 0 1 2  : 0.5, 1.0, 2.0
(binary)3 4 5 6: 0, 1, 2, 3
(morph) 7 8 9  : 0, 1, 2
*/
static int try_method_count[10] = {0};

BST_QR_SCANNER::BST_QR_SCANNER(const char* config_path):mBusy(false){
#ifdef __ANDROID__
    std::string model_dir = "/sdcard/";
#else
    std::string model_dir = "../Data/model/";
#endif
    printf("model_dir: %s\n", model_dir.c_str());
    mParams.qrDetectType = 1;
    if (mParams.qrDetectType){
        scanner = new qrcode_imp::BstAIQRCode(
            model_dir + "detect.prototxt",
            model_dir + "detect_v3_384.tflite",
            model_dir + "sr.prototxt",
            model_dir + "sr.caffemodel",
            mParams
        );
    }else{
        scanner = new qrcode_imp::BstAIQRCode(
            model_dir + "detect.prototxt",
            model_dir + "detect.caffemodel",
            model_dir + "sr.prototxt",
            model_dir + "sr.caffemodel",
            mParams
        );
    }
    scanner->setDetectInputSize(384.0);

#ifdef __ANDROID__
    char prop[50];
    memset(prop, 0, sizeof(prop));
    int ret = 0;
    ret = __system_property_get("persist.camera.algo.bst.qrcode.bypass", prop);

    if (ret != 0){
        string tmp_prop = prop;
        if(tmp_prop == "true"){
            mByPass = true;
        }else{
            mByPass = false;
        }
    }else{
        mByPass = false;
    }

    ret = __system_property_get("persist.camera.algo.bst.qrcode.dump", prop);
    if (ret != 0){
        string tmp_prop = prop;
        if (tmp_prop == "true"){
            mDump = true;
        }else{
            mDump = false;
        }
    }
#endif
}

BST_QR_SCANNER::~BST_QR_SCANNER(){
    if (scanner){
        delete scanner;
        scanner = NULL;
    }
}

int BST_QR_SCANNER::scan(bst_QR_input_t *input, std::vector<bst_QR_output_t> &result){
    if(scanner == NULL){
        return BST_QR_INVALID_SCANNER;
    }
    if (gBdecodeReturn){
        return BST_QR_NOT_FOUND;
    }
    result.clear();
    int ret = BST_QR_NOT_FOUND;
   // Mat img(input->image.height, input->image.width, CV_8UC1, input->image.pimage);
    //vector<Mat> pts;
    vector<CMatrix> cpts;
    TimeUtils time_utils;
    time_utils.reset();
    CGrayImage cimg;
    cimg.ShadowCreate(input->image.pimage, input->image.width, input->image.height);
    cpts = scanner->detect(cimg);
    for (int i = 0; i < (int)cpts.size(); i++){
        bst_QR_output_t out;

        if (1){
            DecodeResult decode_result = scanner->decode_v2(&cimg, cpts[i], mParams);
            if (decode_result.decode_text.size() == 0){
                continue;
            }
            float xmin = input->image.width - 1, ymin = input->image.height - 1, xmax = 0, ymax = 0;
            for (int p = 0; p < 4; p++){
                xmin = std::min(decode_result.corner_points[p].x, xmin);
                ymin = std::min(decode_result.corner_points[p].y, ymin);
                xmax = std::max(decode_result.corner_points[p].x, xmax);
                ymax = std::max(decode_result.corner_points[p].y, ymax);
            }
           // Point center((xmin+xmax)/2.0, (ymin+ymax)/2.0);
            out.QR_location_x = (xmin + xmax) / 2.0;// center.x;
            out.QR_location_y = (ymin + ymax) / 2.0;// center.y;
            out.xmin = xmin;
            out.ymin = ymin;
            out.xmax = xmax;
            out.ymax = ymax;
            out.decode_result = decode_result.decode_text;
        }

        if (0){
            out.xmin = cpts[i][0][0];
            out.ymin = cpts[i][0][1];
            out.xmax = cpts[i][2][0];
            out.ymax = cpts[i][2][1];
            out.decode_result = "det";
        }

        result.push_back(out);
        ret = BST_QR_OK;
    }

    double cost_time = time_utils.get_time();

    printf("detect and decode num = %d \n", (int)result.size());

    int n = cpts.size();
    if(n == result.size() && n > 0)
        return BST_QR_OK;
    else
        return BST_QR_NOT_FOUND;
}