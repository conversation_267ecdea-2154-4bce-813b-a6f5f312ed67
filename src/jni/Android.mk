LOCAL_PATH := $(call my-dir)
SOLUTION_PATH := $(LOCAL_PATH)/../../
include $(SOLUTION_PATH)/Util/jni/Android.mk
include $(SOLUTION_PATH)/mathlib/jni/Android.mk
include $(SOLUTION_PATH)/ImgLib/jni/Android.mk
include $(SOLUTION_PATH)/OCLNative/Android.mk
#include $(SOLUTION_PATH)/ImgLib/jni/Android.mk
include $(SOLUTION_PATH)/3rdparty/BSTTool/Android.mk
#include $(SOLUTION_PATH)/3rdparty/opencv_android/4.5.3/sdk/native/jni/OpenCV_STATIC.mk
# $(OPENCV_LOCAL_CFLAGS)
# $(OPENCV_LOCAL_LDLIBS)
# $(OPENCV_LOCAL_C_INCLUDES)
# $(OPENCV_LOCAL_STATIC_LIBS)
TFLITE_PATH:= $(SOLUTION_PATH)/3rdparty/tensorflow2.10
include $(CLEAR_VARS)
LOCAL_MODULE:=  tensorflowlite
ifeq ($(TARGET_ARCH_ABI),armeabi-v7a)
LOCAL_SRC_FILES:= $(TFLITE_PATH)/libs/armeabi-v7a/libtensorflow-lite.a
else ifeq ($(TARGET_ARCH_ABI),arm64-v8a)
LOCAL_SRC_FILES:= $(TFLITE_PATH)/libs/arm64-v8a/libtensorflow-lite.a
endif
include $(PREBUILT_STATIC_LIBRARY)

include $(CLEAR_VARS)
LOCAL_MODULE:=  pthreadpool
ifeq ($(TARGET_ARCH_ABI),armeabi-v7a)
LOCAL_SRC_FILES:= $(TFLITE_PATH)/libs//armeabi-v7a/libpthreadpool.a
else ifeq ($(TARGET_ARCH_ABI),arm64-v8a)
LOCAL_SRC_FILES:= $(TFLITE_PATH)/libs/arm64-v8a/libpthreadpool.a
endif
include $(PREBUILT_STATIC_LIBRARY)

include $(CLEAR_VARS)
LOCAL_MODULE:=  tensorflowlitedeps
ifeq ($(TARGET_ARCH_ABI),armeabi-v7a)
LOCAL_SRC_FILES:= $(TFLITE_PATH)/libs/armeabi-v7a/libtensorflow-lite-deps.a
else ifeq ($(TARGET_ARCH_ABI),arm64-v8a)
LOCAL_SRC_FILES:= $(TFLITE_PATH)/libs/arm64-v8a/libtensorflow-lite-deps.a
endif
include $(PREBUILT_STATIC_LIBRARY)

include $(CLEAR_VARS)
LOCAL_MODULE:=  cppstatic
ifeq ($(TARGET_ARCH_ABI),armeabi-v7a)
LOCAL_SRC_FILES:= $(TFLITE_PATH)/libs/armeabi-v7a/libc++_static.a
else ifeq ($(TARGET_ARCH_ABI),arm64-v8a)
LOCAL_SRC_FILES:= $(TFLITE_PATH)/libs/arm64-v8a/libc++_static.a
endif
include $(PREBUILT_STATIC_LIBRARY)

include $(CLEAR_VARS)
LOCAL_MODULE:=  libyuv
ifeq ($(TARGET_ARCH_ABI),armeabi-v7a)
LOCAL_SRC_FILES:= $(SOLUTION_PATH)/3rdparty/lib_yuv/libs/armeabi-v7a/libbst_yuv_static.a
else ifeq ($(TARGET_ARCH_ABI),arm64-v8a)
LOCAL_SRC_FILES:= $(SOLUTION_PATH)/3rdparty/lib_yuv/libs/arm64-v8a/libbst_yuv_static.a
endif
include $(PREBUILT_STATIC_LIBRARY)

include $(CLEAR_VARS)
LOCAL_MODULE:=  fastcv
ifeq ($(TARGET_ARCH_ABI),armeabi-v7a)
LOCAL_SRC_FILES:= $(SOLUTION_PATH)/3rdparty/FastCV/lib/Android/armeabi-v7a/libfastcv.a
else ifeq ($(TARGET_ARCH_ABI),arm64-v8a)
LOCAL_SRC_FILES:= $(SOLUTION_PATH)/3rdparty/FastCV/lib/Android/arm64-v8a/libfastcv.a
endif
include $(PREBUILT_STATIC_LIBRARY)

include $(CLEAR_VARS)
LOCAL_MODULE     := bstQRCode
EXCLUDE_FILES    := 
MY_PREFIX        := $(LOCAL_PATH)
ALL_SOURCES      := $(wildcard $(SOLUTION_PATH)/src/*.cpp)
ALL_SOURCES      += $(wildcard $(SOLUTION_PATH)/src/*.cc)
ALL_SOURCES      += $(wildcard $(SOLUTION_PATH)/src/*.c)
#ALL_SOURCES      += $(wildcard $(SOLUTION_PATH)/OCLNative/src/*.cc)
#ALL_SOURCES      += $(wildcard $(SOLUTION_PATH)/OCLNative/src/*.cpp)
ALL_SOURCES      += $(wildcard $(SOLUTION_PATH)/src/bstai_qrcode/*.cpp)
ALL_SOURCES      += $(wildcard $(SOLUTION_PATH)/src/bstai_qrcode/detector/*.cpp)
ALL_SOURCES      += $(wildcard $(SOLUTION_PATH)/src/bstai_qrcode/scale/*.cpp)
ALL_SOURCES      += $(wildcard $(SOLUTION_PATH)/src/bstai_qrcode/zxing/*.cpp)
ALL_SOURCES      += $(wildcard $(SOLUTION_PATH)/src/bstai_qrcode/zxing/*.hpp)
ALL_SOURCES      += $(wildcard $(SOLUTION_PATH)/src/bstai_qrcode/zxing/common/*.cpp)
ALL_SOURCES      += $(wildcard $(SOLUTION_PATH)/src/bstai_qrcode/zxing/common/binarizer/*.cpp)
ALL_SOURCES      += $(wildcard $(SOLUTION_PATH)/src/bstai_qrcode/zxing/common/reedsolomon/*.cpp)
ALL_SOURCES      += $(wildcard $(SOLUTION_PATH)/src/bstai_qrcode/zxing/qrcode/*.cpp)
ALL_SOURCES      += $(wildcard $(SOLUTION_PATH)/src/bstai_qrcode/zxing/qrcode/decoder/*.cpp)
ALL_SOURCES      += $(wildcard $(SOLUTION_PATH)/src/bstai_qrcode/zxing/qrcode/detector/*.cpp)

LOCAL_SRC_FILES  := $(ALL_SOURCES)
LOCAL_CFLAGS     := -Wno-write-strings -fopenmp -Wl,-z,max-page-size=16384
ifeq ($(TARGET_ARCH_ABI),armeabi-v7a)
LOCAL_CFLAGS    += -D__ARM__ -mfloat-abi=softfp -mfpu=neon
LOCAL_CFLAGS    += -march=armv7-a -mtune=cortex-a8
LOCAL_ARM_MODE := arm
else ifeq ($(TARGET_ARCH_ABI),arm64-v8a)
LOCAL_CFLAGS    += -D__ARM__ -march=armv8-a
else ifeq ($(TARGET_ARCH_ABI),x86)
LOCAL_CFLAGS    += -D__X86__
else ifeq ($(TARGET_ARCH_ABI),x86_64)
LOCAL_CFLAGS    += -D__X86__
endif
LOCAL_CXXFLAGS   := -fopenmp
LOCAL_C_INCLUDES := $(SOLUTION_PATH)/Util/include $(SOLUTION_PATH)/OCLNative/include $(SOLUTION_PATH)/OCLNative/src $(SOLUTION_PATH)/3rdparty  $(SOLUTION_PATH)/tensorflow/ $(TFLITE_PATH)/include $(OPENCV_LOCAL_C_INCLUDES) #$(SOLUTION_PATH)/3rdparty/opencv_android/4.5.3/sdk/native/jni/include $(OPENCV_LOCAL_C_INCLUDES)
LOCAL_C_INCLUDES += $(SOLUTION_PATH)/src $(SOLUTION_PATH)/src/bstai_qrcode/detector $(SOLUTION_PATH)/src/bstai_qrcode/scale $(SOLUTION_PATH)/src/bstai_qrcode/zxing $(SOLUTION_PATH)/src/bstai_qrcode/zxing/common $(SOLUTION_PATH)/src/bstai_qrcode/zxing/qrcode 
LOCAL_C_INCLUDES += $(SOLUTION_PATH)/3rdparty/BSTTool/src
LOCAL_C_INCLUDES += $(SOLUTION_PATH)/3rdparty/BSTTool
LOCAL_C_INCLUDES += $(SOLUTION_PATH)/3rdparty/FastCV/inc
LOCAL_C_INCLUDES += $(SOLUTION_PATH)/3rdparty/mipp
#LOCAL_C_INCLUDES += $(SOLUTION_PATH)/OCLNative/src
#LOCAL_C_INCLUDES += $(SOLUTION_PATH)/OCLNative/include
LOCAL_C_INCLUDES += $(SOLUTION_PATH)/src/bstai_qrcode
LOCAL_C_INCLUDES += $(SOLUTION_PATH)/3rdparty/lib_yuv/include
#LOCAL_SHARED_LIBRARIES := opencv_shared
LOCAL_STATIC_LIBRARIES := libyuv BSTTool fastcv libUtil tensorflowlite tensorflowlitedeps pthreadpool cppstatic $(OPENCV_LOCAL_STATIC_LIBS) libBSTOCL math ImgLib
#LOCAL_LDLIBS += $(OpenCV_LDLIBS)
LOCAL_LDLIBS  += -llog -fopenmp -lEGL -lGLESv3
LOCAL_LDLIBS  +=  -static-libstdc++ 
LOCAL_CXXFLAGS   += -Wl,-z,max-page-size=16384
LOCAL_LDLIBS += -llog -fopenmp -lEGL -lGLESv3 $(OPENCV_LOCAL_LDLIBS)
LOCAL_CFLAGS +=  -DOpenGLES -DANDROID -DCPU_ONLY -DMOTION_TRACK -DUSE_NEON -DFASTCV -DUSE_BSTTOOL -D__ANDROID__
include $(BUILD_SHARED_LIBRARY)                                                        

