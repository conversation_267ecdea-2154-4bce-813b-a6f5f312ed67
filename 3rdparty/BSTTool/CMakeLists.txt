cmake_minimum_required(VERSION 2.8.12)
project(BSTTool)
# SET(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -std=c++0x")

include_directories(
	${CMAKE_CURRENT_SOURCE_DIR}/src
)

set(SRC_FILE
    ${CMAKE_CURRENT_SOURCE_DIR}/src/BSTTool.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/src/bmp.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/src/exif.cpp
    ${CMAKE_CURRENT_SOURCE_DIR}/src/kanna_rotate.cpp
)

add_library(${PROJECT_NAME}  ${SRC_FILE})

# if(BUILD_TEST)
#     add_subdirectory(test)
# endif()

# Install
if(ANDROID)
    install(TARGETS ${PROJECT_NAME} DESTINATION ${CMAKE_INSTALL_PREFIX}/${ANDROID_ABI}/lib)
else()
    install(TARGETS ${PROJECT_NAME} DESTINATION ${CMAKE_INSTALL_PREFIX}/x64/lib)
endif()

