#include <iostream>
#include <chrono>
#include <iomanip>
#include <memory>
#include <vector>
#include <string>
#include <sys/stat.h>

// Project includes
#include "utils.h"
#include "bst_QR_scanner.h"
#include "opencv2/opencv.hpp"

using namespace std;
using namespace cv;
using namespace BstCE;

// Constants
namespace {
    const string SEPARATOR_LINE = string(60, '=');
    const string INFO_PREFIX = "[INFO] ";
    const string ERROR_PREFIX = "[ERROR] ";
    const string WARNING_PREFIX = "[WARNING] ";
    const string SUCCESS_PREFIX = "[SUCCESS] ";
    const string PROGRESS_PREFIX = "[PROGRESS] ";

    const vector<string> CONFIG_SEARCH_PATHS = {
        "../Data/config/qr.cfg",
        "../config/config.cfg",
        "./config/config.cfg",
        "config.cfg"
    };
}

/**
 * @brief Utility functions for file system operations and logging
 */
namespace Utils {

    /**
     * @brief Check if a file exists
     * @param filename Path to the file
     * @return true if file exists, false otherwise
     */
    bool fileExists(const string& filename) {
        struct stat buffer;
        return (stat(filename.c_str(), &buffer) == 0);
    }

    /**
     * @brief Check if a directory exists
     * @param dirname Path to the directory
     * @return true if directory exists, false otherwise
     */
    bool directoryExists(const string& dirname) {
        struct stat buffer;
        return (stat(dirname.c_str(), &buffer) == 0 && S_ISDIR(buffer.st_mode));
    }

    /**
     * @brief Get current timestamp as formatted string
     * @return Timestamp string in format "YYYY-MM-DD HH:MM:SS"
     */
    string getCurrentTimestamp() {
        auto now = chrono::system_clock::now();
        auto time_t = chrono::system_clock::to_time_t(now);
        stringstream ss;
        ss << put_time(localtime(&time_t), "%Y-%m-%d %H:%M:%S");
        return ss.str();
    }

    /**
     * @brief Print formatted log message with timestamp
     * @param level Log level (INFO, ERROR, WARNING, SUCCESS, PROGRESS)
     * @param message Log message
     */
    void logMessage(const string& level, const string& message) {
        cout << level << message << endl;
    }

    /**
     * @brief Print info message
     */
    void logInfo(const string& message) {
        logMessage(INFO_PREFIX, message);
    }

    /**
     * @brief Print error message
     */
    void logError(const string& message) {
        logMessage(ERROR_PREFIX, message);
    }

    /**
     * @brief Print warning message
     */
    void logWarning(const string& message) {
        logMessage(WARNING_PREFIX, message);
    }

    /**
     * @brief Print success message
     */
    void logSuccess(const string& message) {
        logMessage(SUCCESS_PREFIX, message);
    }

    /**
     * @brief Print progress message
     */
    void logProgress(const string& message) {
        logMessage(PROGRESS_PREFIX, message);
    }
}

/**
 * @brief Read raw grayscale image data from binary file
 * @param imgpath Path to the binary image file
 * @param width Expected image width
 * @param height Expected image height
 * @return Pointer to allocated image data, or nullptr on failure
 */
unsigned char* readGrayImageBinary(const char* imgpath, int width, int height)
{
    if (!Utils::fileExists(imgpath)) {
        Utils::logError("Gray image file not found: " + string(imgpath));
        return nullptr;
    }

    const size_t imageSize = width * height;
    unsigned char* srcImg = static_cast<unsigned char*>(malloc(imageSize));
    if (!srcImg) {
        Utils::logError("Failed to allocate memory for image (" + to_string(imageSize) + " bytes)");
        return nullptr;
    }

    FILE* fp = fopen(imgpath, "rb");
    if (!fp) {
        Utils::logError("Failed to open gray image file: " + string(imgpath));
        free(srcImg);
        return nullptr;
    }

    size_t bytesRead = fread(srcImg, 1, imageSize, fp);
    fclose(fp);

    if (bytesRead != imageSize) {
        Utils::logWarning("Expected " + to_string(imageSize) + " bytes, but read " +
                         to_string(bytesRead) + " bytes from " + string(imgpath));
    }

    return srcImg;
}

/**
 * @brief Configuration manager for QR scanner
 */
class ConfigManager {
public:
    /**
     * @brief Find and return the first available config file path
     * @return Config file path, or empty string if none found
     */
    static string findConfigFile() {
        for (const string& path : CONFIG_SEARCH_PATHS) {
            if (Utils::fileExists(path)) {
                Utils::logInfo("Using config file: " + path);
                return path;
            }
        }

        Utils::logWarning("Config file not found. Trying default path: " + CONFIG_SEARCH_PATHS[0]);
        return CONFIG_SEARCH_PATHS[0];
    }
};

/**
 * @brief Image processing utilities for QR scanning
 */
class ImageProcessor {
public:
    /**
     * @brief Load JPG image and convert to grayscale
     * @param filepath Path to the image file
     * @param qrImg Output QR image structure
     * @return true if successful, false otherwise
     */
    static bool loadJpgImage(const string& filepath, bst_QR_img_t& qrImg) {
        Mat img = imread(filepath);
        if (img.empty()) {
            Utils::logError("Failed to load image: " + filepath);
            return false;
        }

        // Convert to grayscale if needed
        if (img.channels() == 3) {
            cvtColor(img, img, COLOR_BGR2GRAY);
        }

        // Allocate memory for QR image
        const size_t imageSize = img.cols * img.rows;
        qrImg.pimage = static_cast<unsigned char*>(malloc(imageSize));
        if (!qrImg.pimage) {
            Utils::logError("Failed to allocate memory for image: " + filepath);
            return false;
        }

        // Copy image data
        memcpy(qrImg.pimage, img.data, imageSize);
        qrImg.width = img.cols;
        qrImg.height = img.rows;
        qrImg.stride = img.cols;

        return true;
    }

    /**
     * @brief Load raw grayscale image
     * @param filepath Path to the binary file
     * @param width Image width
     * @param height Image height
     * @param qrImg Output QR image structure
     * @return true if successful, false otherwise
     */
    static bool loadGrayImage(const string& filepath, int width, int height, bst_QR_img_t& qrImg) {
        qrImg.pimage = readGrayImageBinary(filepath.c_str(), width, height);
        if (!qrImg.pimage) {
            Utils::logError("Failed to load gray image: " + filepath);
            return false;
        }

        qrImg.width = width;
        qrImg.height = height;
        qrImg.stride = width;

        return true;
    }

    /**
     * @brief Clean up QR image memory
     * @param qrImg QR image structure to clean up
     */
    static void cleanupImage(bst_QR_img_t& qrImg) {
        if (qrImg.pimage) {
            free(qrImg.pimage);
            qrImg.pimage = nullptr;
        }
    }
};

/**
 * @brief Performance statistics tracker
 */
class PerformanceTracker {
private:
    chrono::high_resolution_clock::time_point startTime;
    int successCount = 0;
    int processedCount = 0;

public:
    void start() {
        startTime = chrono::high_resolution_clock::now();
        successCount = 0;
        processedCount = 0;
    }

    void recordSuccess() {
        successCount++;
        processedCount++;
    }

    void recordFailure() {
        processedCount++;
    }

    void printSummary(int totalFiles) const {
        auto endTime = chrono::high_resolution_clock::now();
        auto totalDuration = chrono::duration_cast<chrono::milliseconds>(endTime - startTime);

        cout << "\n" << SEPARATOR_LINE << endl;
        cout << "[SUMMARY] QR Code Detection Results" << endl;
        cout << SEPARATOR_LINE << endl;
        cout << "Total files processed: " << processedCount << "/" << totalFiles << endl;
        cout << "Successful detections: " << successCount << endl;
        cout << "Success ratio: " << fixed << setprecision(2)
             << (processedCount > 0 ? (successCount * 100.0 / processedCount) : 0.0) << "%" << endl;
        cout << "Total processing time: " << totalDuration.count() << "ms" << endl;
        cout << "Average time per image: "
             << (processedCount > 0 ? totalDuration.count() / processedCount : 0) << "ms" << endl;
        cout << "Timestamp: " << Utils::getCurrentTimestamp() << endl;
        cout << SEPARATOR_LINE << endl;
    }
};

/**
 * @brief Main folder scanning function with improved structure
 * @param imgDir Directory containing images to scan
 * @param width Image width (for gray images)
 * @param height Image height (for gray images)
 * @param imgType Image type ("jpg" or "gray")
 */
void folderDemoScanner(const string& imgDir, int width, int height, const string& imgType) {
    Utils::logInfo(Utils::getCurrentTimestamp() + " - Starting folder demo scanner");
    Utils::logInfo("Image directory: " + imgDir);
    Utils::logInfo("Image type: " + imgType);

    // Validate directory
    if (!Utils::directoryExists(imgDir)) {
        Utils::logError("Directory does not exist: " + imgDir);
        return;
    }

    // Get file list
    vector<string> filenames;
    FileUtils::GetFileNames(imgDir, filenames);

    if (filenames.empty()) {
        Utils::logError("No files found in directory: " + imgDir);
        return;
    }

    Utils::logInfo("Found " + to_string(filenames.size()) + " files");

    // Initialize scanner
    string configFile = ConfigManager::findConfigFile();
    unique_ptr<BST_QR_SCANNER> scanner;

    try {
        scanner = make_unique<BST_QR_SCANNER>(configFile.c_str());
        Utils::logInfo("QR scanner initialized successfully");
    } catch (const exception& e) {
        Utils::logError("Failed to initialize QR scanner: " + string(e.what()));
        return;
    } catch (...) {
        Utils::logError("Failed to initialize QR scanner: Unknown error");
        return;
    }

    // Process images
    PerformanceTracker tracker;
    tracker.start();

    for (size_t i = 0; i < filenames.size(); ++i) {
        Utils::logProgress(to_string(i + 1) + "/" + to_string(filenames.size()) +
                          " Processing: " + filenames[i]);

        bst_QR_img_t qrImg = {0};
        bool imageLoaded = false;

        // Load image based on type
        if (imgType == "jpg") {
            imageLoaded = ImageProcessor::loadJpgImage(filenames[i], qrImg);
        } else if (imgType == "gray") {
            imageLoaded = ImageProcessor::loadGrayImage(filenames[i], width, height, qrImg);
        } else {
            Utils::logError("Unsupported image type: " + imgType);
            continue;
        }

        if (!imageLoaded) {
            continue;
        }

        // Scan for QR codes
        bst_QR_input_t qrInput;
        qrInput.image = qrImg;

        vector<bst_QR_output_t> qrOutputs;
        auto scanStart = chrono::high_resolution_clock::now();
        int retVal = scanner->scan(&qrInput, qrOutputs);
        auto scanEnd = chrono::high_resolution_clock::now();

        auto scanDuration = chrono::duration_cast<chrono::milliseconds>(scanEnd - scanStart);

        // Process results
        if (!qrOutputs.empty()) {
            tracker.recordSuccess();
            Utils::logSuccess("Found " + to_string(qrOutputs.size()) + " QR code(s) in " +
                             to_string(scanDuration.count()) + "ms:");

            for (size_t j = 0; j < qrOutputs.size(); ++j) {
                cout << "  QR " << (j + 1) << ": \"" << qrOutputs[j].decode_result << "\"" << endl;
                cout << "    Position: (" << qrOutputs[j].xmin << "," << qrOutputs[j].ymin
                     << ") to (" << qrOutputs[j].xmax << "," << qrOutputs[j].ymax << ")" << endl;
            }
        } else {
            tracker.recordFailure();
            Utils::logInfo("No QR codes found in " + to_string(scanDuration.count()) +
                          "ms (return code: " + to_string(retVal) + ")");
        }

        // Clean up
        ImageProcessor::cleanupImage(qrImg);
    }

    // Print summary
    tracker.printSummary(filenames.size());
    Utils::logInfo("QR scanner cleaned up");
}

/*
void folder_demo_bst(string img_dir, int width, int height, string img_type)
{
    vector<string> filenames;
    FileUtils::GetFileNames(img_dir, filenames);

    int retVal = -1;
    bst_QR_config_t config;
    config.config_path = "../config/bstQRCode_1.1.2.1_Ton_Coff_20100101-20231231.cfg";
    retVal = bst_QR_init(&config);
    printf("bst_QR_init return %d\n", retVal);

    int success_num = 0;

    for (int n = 0; n < (int)filenames.size(); n++)
    {
        cout<<n<<"/"<<filenames.size()<<" "<<filenames[n]<<endl;
        bst_QR_img_t QRImg;
        Mat img;
        
        if (img_type == "jpg")
        {
            img = imread(filenames[n]);
            cvtColor(img, img, COLOR_BGR2GRAY);

            QRImg.pimage = (unsigned char*)malloc(img.cols*img.rows);
            memcpy(QRImg.pimage, img.data, img.cols*img.rows);
            QRImg.width = img.cols;
            QRImg.height = img.rows;
            QRImg.stride = img.cols;
        }
        else if (img_type == "gray")
        {
            QRImg.pimage = readbin_GRAY(filenames[n].c_str(), width, height);
            QRImg.width = width;
            QRImg.height = height;
            QRImg.stride = width;
            Mat gray(height, width, CV_8UC1, QRImg.pimage);
            img = gray.clone();
        }

        bst_QR_input_t QRInput;
        QRInput.image = QRImg;

        std::vector<bst_QR_output_t> vQROutput;
        BstCE::TimeUtils time_utils;
        time_utils.reset();
        retVal = bst_QR_scan(&QRInput, vQROutput);
        double cost_time = time_utils.get_time();

        if (vQROutput.size() > 0)
        {
            success_num += 1;
        }

        for (int i = 0; i<vQROutput.size(); i++)
        {
            Point pt1 = Point(vQROutput[i].xmin, vQROutput[i].ymin);
            Point pt2 = Point(vQROutput[i].xmax, vQROutput[i].ymax);
            Rect rect{pt1, pt2};
            Point center = (pt1 + pt2) / 2;
            rectangle(img, rect, {0, 0, 255}, 2);
            circle(img, center, rect.height/50, {0, 0, 255}, -1);
            putText(img, vQROutput[i].decode_result, {pt1.x, pt2.y + 16}, 1, 1, {0, 0, 255});
        }
        putText(img, to_string(cost_time), Point(10, 10), 1, 1, Scalar(0, 0, 255));
        
        // imshow("QR code", img);
        // waitKey(0);
        if (QRImg.pimage)
        {
            free(QRImg.pimage);
            QRImg.pimage = NULL;
        }
    }

    printf("det/decode success ratio = %f(%d/%d) \n", success_num*1.0/filenames.size(), success_num, filenames.size());
}


void img_demo_bst(string &img_path, string &img_type, int width, int height)
{
    Mat gray;
    if (img_type=="jpg"){
        Mat img = imread(img_path);
        cvtColor(img, gray, COLOR_BGR2GRAY);
    } else if (img_type=="gray"){
        gray = Mat(height,width,CV_8UC1,readbin_GRAY(img_path.c_str(),width,height));
    }
    
    int retVal = -1;
    bst_QR_config_t config;
    config.config_path = "../bstQRCode_1.1.2.1_Ton_Coff_20100101-20231231.cfg";
    retVal = bst_QR_init(&config);
    printf("bst_QR_init return %d\n", retVal);
    bst_QR_img_t QRImg;
    QRImg.pimage = gray.data;
    QRImg.width = gray.cols;
    QRImg.height = gray.rows;
    QRImg.stride = gray.cols;

    bst_QR_input_t QRInput;
    QRInput.image = QRImg;

    std::vector<bst_QR_output_t> vQROutput;
    BstCE::TimeUtils time_utils;
    time_utils.reset();
    retVal = bst_QR_scan(&QRInput, vQROutput);
    double cost_time = time_utils.get_time();

    printf("bst_QR_scan return %d\n", retVal);

    for (int i = 0; i<vQROutput.size(); i++)
    {
        Point pt1 = Point(vQROutput[i].xmin, vQROutput[i].ymin);
        Point pt2 = Point(vQROutput[i].xmax, vQROutput[i].ymax);
        Rect rect{pt1, pt2};
        Point center = (pt1 + pt2) / 2;
        rectangle(gray, rect, {0, 0, 255}, 2);
        circle(gray, center, rect.height/50, {0, 0, 255}, -1);
        putText(gray, vQROutput[i].decode_result, {pt1.x, pt2.y + 16}, 1, 1, {0, 0, 255});
    }
    putText(gray, to_string(cost_time), Point(10, 10), 1, 1, Scalar(0, 0, 255));
    
    namedWindow("QR code", WINDOW_NORMAL);
    imshow("QR code", gray);
    waitKey(0);
}

void camera_demo(string img_dir)
{
    int retVal = -1;
    bst_QR_config_t config;
    config.config_path = "../config/bstQRCode_1.1.2.1_Ton_Coff_20100101-20231231.cfg";
    retVal = bst_QR_init(&config);
    printf("bst_QR_init return %d\n", retVal);

    int det_success_num = 0;

    BstCE::TimeUtils time_utils;
    int number = 0;
    VideoCapture cap(img_dir);
    if (!cap.isOpened()) {
        std::cerr << "Could not open the video file: " << img_dir << std::endl;
    }
    VideoWriter video_writer(img_dir.substr(img_dir.find_last_of("/\\") + 1), cv::VideoWriter::fourcc('M', 'J', 'P', 'G'),
        30, cv::Size((int)cap.get(cv::CAP_PROP_FRAME_WIDTH), (int)cap.get(cv::CAP_PROP_FRAME_HEIGHT)));
    while (true)
    {
        Mat img, orig_img;
        bst_QR_img_t QRImg;
        cap >> orig_img;
        if (orig_img.empty()) break; 
        cvtColor(orig_img, img, COLOR_BGR2GRAY);
        QRImg.pimage = (unsigned char*)malloc(img.cols*img.rows);
        memcpy(QRImg.pimage, img.data, img.cols*img.rows);
        QRImg.width = img.cols;
        QRImg.height = img.rows;
        QRImg.stride = img.cols;

        bst_QR_input_t QRInput;
        QRInput.image = QRImg;

        std::vector<bst_QR_output_t> vQROutput;


        time_utils.reset();
        retVal = bst_QR_scan(&QRInput, vQROutput);
        double cost_time = time_utils.get_time();

        if (vQROutput.size() > 0)
        {
            det_success_num += 1;
        }

        for (int i = 0; i<vQROutput.size(); i++)
        {
            Point pt1 = Point(vQROutput[i].xmin, vQROutput[i].ymin);
            Point pt2 = Point(vQROutput[i].xmax, vQROutput[i].ymax);
            Rect rect{pt1, pt2};
            Point center = (pt1 + pt2) / 2;
            rectangle(orig_img, rect, {0, 0, 255}, 2);
            circle(orig_img, center, rect.height/50, {0, 0, 255}, -1);
            putText(orig_img, vQROutput[i].decode_result, {pt1.x, pt2.y + 16}, 1, 1, {0, 0, 255});
        }
        putText(orig_img, to_string(cost_time), Point(10, 10), 1, 1, Scalar(0, 0, 255));
        
        video_writer.write(orig_img);
        number += 1;
    }
}
*/

void show_usage(const char* program_name) {
    cout << "\n" << string(60, '=') << endl;
    cout << "QR Code Scanner Test Program" << endl;
    cout << string(60, '=') << endl;
    cout << "Usage: " << program_name << " <input_path> <input_type> <width> <height> <img_type>" << endl;
    cout << "\nParameters:" << endl;
    cout << "  input_path  - Path to input (file or directory)" << endl;
    cout << "  input_type  - Type of input: 'folder', 'camera', 'img'" << endl;
    cout << "  width       - Image width (for gray images, 0 for auto)" << endl;
    cout << "  height      - Image height (for gray images, 0 for auto)" << endl;
    cout << "  img_type    - Image format: 'jpg', 'gray'" << endl;
    cout << "\nSupported input types:" << endl;
    cout << "  folder      - Process all images in a directory" << endl;
    cout << "  camera      - Process video from camera (not implemented)" << endl;
    cout << "  img         - Process single image (not implemented)" << endl;
    cout << "\nSupported image types:" << endl;
    cout << "  jpg         - JPEG/JPG images (recommended)" << endl;
    cout << "  gray        - Raw grayscale binary files" << endl;
    cout << "\nExamples:" << endl;
    cout << "  " << program_name << " ./test_images folder 0 0 jpg" << endl;
    cout << "  " << program_name << " ./gray_data folder 640 480 gray" << endl;
    cout << "\nNotes:" << endl;
    cout << "  - For 'jpg' type, width and height are ignored (auto-detected)" << endl;
    cout << "  - For 'gray' type, width and height must be specified" << endl;
    cout << "  - Config file will be searched in multiple locations" << endl;
    cout << string(60, '=') << endl;
}

/**
 * @brief Validate command line parameters
 * @param inputPath Path to input file or directory
 * @param inputType Type of input ("folder", "camera", "img")
 * @param width Image width (for gray images)
 * @param height Image height (for gray images)
 * @param imgType Image format ("jpg", "gray")
 * @return true if parameters are valid, false otherwise
 */
bool validateParameters(const string& inputPath, const string& inputType,
                       int width, int height, const string& imgType) {
    // Validate input_type
    if (inputType != "folder" && inputType != "camera" && inputType != "img") {
        Utils::logError("Invalid input_type: " + inputType);
        Utils::logError("Must be one of: folder, camera, img");
        return false;
    }

    // Validate img_type
    if (imgType != "jpg" && imgType != "gray") {
        Utils::logError("Invalid img_type: " + imgType);
        Utils::logError("Must be one of: jpg, gray");
        return false;
    }

    // Validate dimensions for gray images
    if (imgType == "gray") {
        if (width <= 0 || height <= 0) {
            Utils::logError("For gray images, width and height must be positive integers");
            Utils::logError("Got width=" + to_string(width) + ", height=" + to_string(height));
            return false;
        }
    }

    // Validate input path exists
    if (inputType == "folder") {
        if (!Utils::directoryExists(inputPath)) {
            Utils::logError("Directory does not exist: " + inputPath);
            return false;
        }
    } else if (inputType == "img") {
        if (!Utils::fileExists(inputPath)) {
            Utils::logError("File does not exist: " + inputPath);
            return false;
        }
    }

    return true;
}

/**
 * @brief Main function with improved error handling and logging
 */
int main(int argc, char** argv)
{
    Utils::logInfo("QR Code Scanner Test Program");
    Utils::logInfo("Build time: " + string(__DATE__) + " " + string(__TIME__));
    Utils::logInfo("Start time: " + Utils::getCurrentTimestamp());

    // Handle help request
    if (argc == 2 && (string(argv[1]) == "-h" || string(argv[1]) == "--help")) {
        show_usage(argv[0]);
        return 0;
    }

    // Validate argument count
    if (argc != 6) {
        Utils::logError("Invalid number of arguments. Expected 5, got " + to_string(argc - 1));
        show_usage(argv[0]);
        return -1;
    }

    // Parse arguments
    string inputPath = argv[1];
    string inputType = argv[2];
    int width = atoi(argv[3]);
    int height = atoi(argv[4]);
    string imgType = argv[5];

    // Display parameters
    Utils::logInfo("Parameters:");
    cout << "  Input path: " << inputPath << endl;
    cout << "  Input type: " << inputType << endl;
    cout << "  Dimensions: " << width << "x" << height << endl;
    cout << "  Image type: " << imgType << endl;

    // Validate parameters
    if (!validateParameters(inputPath, inputType, width, height, imgType)) {
        Utils::logError("Parameter validation failed");
        show_usage(argv[0]);
        return -1;
    }

    Utils::logInfo("Parameter validation passed");

    // Execute based on input type
    try {
        if (inputType == "folder") {
            folderDemoScanner(inputPath, width, height, imgType);
        } else if (inputType == "camera") {
            Utils::logError("Camera input type is not implemented yet");
            return -1;
        } else if (inputType == "img") {
            Utils::logError("Single image input type is not implemented yet");
            return -1;
        }
    } catch (const exception& e) {
        Utils::logError("Exception occurred: " + string(e.what()));
        return -1;
    } catch (...) {
        Utils::logError("Unknown exception occurred");
        return -1;
    }

    Utils::logInfo("Program completed successfully at " + Utils::getCurrentTimestamp());
    return 0;
}