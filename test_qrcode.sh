#!/bin/bash

# QR Code Scanner Test Script
# Usage: ./test_qrcode.sh [test_type]
# test_type: folder, single, help

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$SCRIPT_DIR"
BUILD_DIR="$PROJECT_ROOT/build"
EXECUTABLE="$BUILD_DIR/QRCodeExc"

# Test directories and files
TEST_DATA_DIR="$PROJECT_ROOT/test/test_data"
CONFIG_DIR="$PROJECT_ROOT/test/config"
CONFIG_FILE="$CONFIG_DIR/config.cfg"

# Function to print colored output
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
show_usage() {
    echo "QR Code Scanner Test Script"
    echo ""
    echo "Usage: $0 [test_type]"
    echo ""
    echo "Test types:"
    echo "  folder    - Test with a folder of images"
    echo "  single    - Test with a single image"
    echo "  help      - Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 folder"
    echo "  $0 single"
    echo ""
    echo "Environment setup:"
    echo "  - Executable: $EXECUTABLE"
    echo "  - Test data: $TEST_DATA_DIR"
    echo "  - Config: $CONFIG_FILE"
}

# Function to check prerequisites
check_prerequisites() {
    print_info "Checking prerequisites..."
    
    # Check if executable exists
    if [ ! -f "$EXECUTABLE" ]; then
        print_error "Executable not found: $EXECUTABLE"
        print_info "Please build the project first by running:"
        print_info "  cd $PROJECT_ROOT && mkdir -p build && cd build && ../build_pc.sh && make -j\$(nproc)"
        exit 1
    fi
    
    # Check if executable is executable
    if [ ! -x "$EXECUTABLE" ]; then
        print_error "File is not executable: $EXECUTABLE"
        chmod +x "$EXECUTABLE"
        print_info "Made executable: $EXECUTABLE"
    fi
    
    print_success "Prerequisites check passed"
}

# Function to setup test environment
setup_test_environment() {
    print_info "Setting up test environment..."
    
    # Create test data directory if it doesn't exist
    mkdir -p "$TEST_DATA_DIR"
    
    # Create config directory if it doesn't exist
    mkdir -p "$CONFIG_DIR"
    
    # Create a basic config file if it doesn't exist
    if [ ! -f "$CONFIG_FILE" ]; then
        print_info "Creating basic config file: $CONFIG_FILE"
        cat > "$CONFIG_FILE" << EOF
# QR Code Scanner Configuration
# Basic configuration for QR code detection and decoding

# Detection parameters
detection_threshold=0.5
min_qr_size=20
max_qr_size=1000

# Decoding parameters
decode_timeout=5000
max_decode_attempts=3

# Image processing parameters
gaussian_blur_kernel=3
adaptive_threshold_block_size=11
adaptive_threshold_c=2
EOF
    fi
    
    print_success "Test environment setup completed"
}

# Function to create sample test images (if needed)
create_sample_images() {
    print_info "Checking for test images..."
    
    if [ ! "$(ls -A $TEST_DATA_DIR 2>/dev/null)" ]; then
        print_warning "No test images found in $TEST_DATA_DIR"
        print_info "Please add some QR code images to test with:"
        print_info "  - JPG images: $TEST_DATA_DIR/*.jpg"
        print_info "  - PNG images: $TEST_DATA_DIR/*.png"
        print_info ""
        print_info "You can download sample QR code images or create your own."
        
        # Create a simple text file with instructions
        cat > "$TEST_DATA_DIR/README.txt" << EOF
Test Images Directory
====================

Please place your QR code test images in this directory.

Supported formats:
- JPG/JPEG files
- PNG files (will be converted to JPG for testing)

Example test images you can create:
1. Generate QR codes online (e.g., qr-code-generator.com)
2. Save them as JPG files in this directory
3. Run the test script again

Sample QR code content to test:
- "Hello World"
- "https://www.example.com"
- "Contact info: John Doe, <EMAIL>"
EOF
        
        return 1
    fi
    
    return 0
}

# Function to test with folder
test_folder() {
    print_info "Running folder test..."
    
    if ! create_sample_images; then
        print_error "No test images available. Please add images to $TEST_DATA_DIR"
        return 1
    fi
    
    # Count image files
    jpg_count=$(find "$TEST_DATA_DIR" -name "*.jpg" -o -name "*.jpeg" | wc -l)
    png_count=$(find "$TEST_DATA_DIR" -name "*.png" | wc -l)
    
    if [ $jpg_count -eq 0 ] && [ $png_count -eq 0 ]; then
        print_error "No image files found in $TEST_DATA_DIR"
        return 1
    fi
    
    print_info "Found $jpg_count JPG files and $png_count PNG files"
    
    # Convert PNG to JPG if needed
    if [ $png_count -gt 0 ]; then
        print_info "Converting PNG files to JPG..."
        for png_file in "$TEST_DATA_DIR"/*.png; do
            if [ -f "$png_file" ]; then
                jpg_file="${png_file%.png}.jpg"
                if command -v convert >/dev/null 2>&1; then
                    convert "$png_file" "$jpg_file"
                    print_info "Converted: $(basename "$png_file") -> $(basename "$jpg_file")"
                else
                    print_warning "ImageMagick 'convert' not found. Cannot convert PNG files."
                    break
                fi
            fi
        done
    fi
    
    # Run the test
    print_info "Running QR code scanner on folder: $TEST_DATA_DIR"
    print_info "Command: $EXECUTABLE \"$TEST_DATA_DIR\" folder 0 0 jpg"
    
    cd "$BUILD_DIR"
    "$EXECUTABLE" "$TEST_DATA_DIR" folder 0 0 jpg
    
    print_success "Folder test completed"
}

# Function to test with single image
test_single() {
    print_info "Running single image test..."
    
    # Find the first JPG file
    first_jpg=$(find "$TEST_DATA_DIR" -name "*.jpg" -o -name "*.jpeg" | head -n 1)
    
    if [ -z "$first_jpg" ]; then
        print_error "No JPG files found in $TEST_DATA_DIR"
        return 1
    fi
    
    print_info "Testing with image: $first_jpg"
    
    # Get image dimensions (if identify command is available)
    if command -v identify >/dev/null 2>&1; then
        dimensions=$(identify -format "%wx%h" "$first_jpg" 2>/dev/null || echo "unknown")
        print_info "Image dimensions: $dimensions"
    fi
    
    # Run the test (Note: single image test is commented out in the original code)
    print_warning "Single image test is not fully implemented in the current version"
    print_info "The test program currently only supports folder testing"
    
    # You could still try to run it, but it won't do anything
    # print_info "Command: $EXECUTABLE \"$first_jpg\" img 0 0 jpg"
    # cd "$BUILD_DIR"
    # "$EXECUTABLE" "$first_jpg" img 0 0 jpg
}

# Main function
main() {
    local test_type="${1:-help}"
    
    case "$test_type" in
        "folder")
            check_prerequisites
            setup_test_environment
            test_folder
            ;;
        "single")
            check_prerequisites
            setup_test_environment
            test_single
            ;;
        "help"|"-h"|"--help")
            show_usage
            ;;
        *)
            print_error "Unknown test type: $test_type"
            echo ""
            show_usage
            exit 1
            ;;
    esac
}

# Run main function with all arguments
main "$@"
