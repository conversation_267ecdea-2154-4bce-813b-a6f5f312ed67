<!-- Creator     : groff version 1.22.3 -->
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
"http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta name="generator" content="groff -Thtml, see www.gnu.org">
<meta http-equiv="Content-Type" content="text/html; charset=US-ASCII">
<meta name="Content-Style" content="text/css">
<style type="text/css">
       p       { margin-top: 0; margin-bottom: 0; vertical-align: top }
       pre     { margin-top: 0; margin-bottom: 0; vertical-align: top }
       table   { margin-top: 0; margin-bottom: 0; vertical-align: top }
       h1      { text-align: center }
</style>
<title>ICONVCTL</title>

</head>
<body>

<h1 align="center">ICONVCTL</h1>

<a href="#NAME">NAME</a><br>
<a href="#SYNOPSIS">SYNOPSIS</a><br>
<a href="#DESCRIPTION">DESCRIPTION</a><br>
<a href="#REQUEST VALUES">REQUEST VALUES</a><br>
<a href="#RETURN VALUE">RETURN VALUE</a><br>
<a href="#ERRORS">ERRORS</a><br>
<a href="#CONFORMING TO">CONFORMING TO</a><br>
<a href="#SEE ALSO">SEE ALSO</a><br>

<hr>


<h2>NAME
<a name="NAME"></a>
</h2>


<p style="margin-left:11%; margin-top: 1em">iconvctl
&minus; control iconv behavior</p>

<h2>SYNOPSIS
<a name="SYNOPSIS"></a>
</h2>


<p style="margin-left:11%; margin-top: 1em"><b>#include
&lt;iconv.h&gt;</b></p>

<p style="margin-left:11%; margin-top: 1em"><b>int iconvctl
(iconv_t</b> <i>cd</i> <b>, int</b> <i>request</i><b>, void
*</b> <i>argument</i><b>);</b></p>

<h2>DESCRIPTION
<a name="DESCRIPTION"></a>
</h2>


<p style="margin-left:11%; margin-top: 1em">The argument
<i>cd</i> must be a conversion descriptor created using the
function <b>iconv_open</b>.</p>


<p style="margin-left:11%; margin-top: 1em"><b>iconvctl</b>
queries or adjusts the behavior of the <b>iconv</b>
function, when invoked with the specified conversion
descriptor, depending on the request value.</p>

<h2>REQUEST VALUES
<a name="REQUEST VALUES"></a>
</h2>


<p style="margin-left:11%; margin-top: 1em">The following
are permissible values for the <i>request</i> parameter.
<b><br>
ICONV_TRIVIALP</b></p>

<p style="margin-left:22%;"><i>argument</i> should be an
<b>int *</b> which will receive 1 if the conversion is
trivial, or 0 otherwise.</p>


<p style="margin-left:11%;"><b>ICONV_GET_TRANSLITERATE</b></p>

<p style="margin-left:22%;"><i>argument</i> should be an
<b>int *</b> which will receive 1 if transliteration is
enabled in the conversion, or 0 otherwise.</p>


<p style="margin-left:11%;"><b>ICONV_SET_TRANSLITERATE</b></p>

<p style="margin-left:22%;"><i>argument</i> should be a
<b>const int *</b>, pointing to an <b>int</b> value. A
non-zero value is used to enable transliteration in the
conversion. A zero value disables it.</p>


<p style="margin-left:11%;"><b>ICONV_GET_DISCARD_ILSEQ</b></p>

<p style="margin-left:22%;"><i>argument</i> should be an
<b>int *</b> which will receive 1 if &quot;illegal sequence
discard and continue&quot; is enabled in the conversion, or
0 otherwise.</p>


<p style="margin-left:11%;"><b>ICONV_SET_DISCARD_ILSEQ</b></p>

<p style="margin-left:22%;"><i>argument</i> should be a
<b>const int *</b>, pointing to an <b>int</b> value. A
non-zero value is used to enable &quot;illegal sequence
discard and continue&quot; in the conversion. A zero value
disables it.</p>

<h2>RETURN VALUE
<a name="RETURN VALUE"></a>
</h2>


<p style="margin-left:11%; margin-top: 1em">The
<b>iconvctl</b> function returns 0 if it succeeds. In case
of error, it sets <b>errno</b> and returns &minus;1.</p>

<h2>ERRORS
<a name="ERRORS"></a>
</h2>


<p style="margin-left:11%; margin-top: 1em">The following
errors can occur, among others:</p>

<table width="100%" border="0" rules="none" frame="void"
       cellspacing="0" cellpadding="0">
<tr valign="top" align="left">
<td width="11%"></td>
<td width="9%">


<p><b>EINVAL</b></p></td>
<td width="2%"></td>
<td width="35%">


<p>The request is invalid.</p></td>
<td width="43%">
</td></tr>
</table>

<h2>CONFORMING TO
<a name="CONFORMING TO"></a>
</h2>


<p style="margin-left:11%; margin-top: 1em">This function
is implemented only in GNU libiconv and not in other
<b>iconv</b> implementations. It is not backed by a
standard. You can test for its presence through
<b>(_LIBICONV_VERSION &gt;= 0x0108)</b>.</p>

<h2>SEE ALSO
<a name="SEE ALSO"></a>
</h2>



<p style="margin-left:11%; margin-top: 1em"><b>iconv_open</b>(3)
<b>iconv</b>(3)</p>
<hr>
</body>
</html>
