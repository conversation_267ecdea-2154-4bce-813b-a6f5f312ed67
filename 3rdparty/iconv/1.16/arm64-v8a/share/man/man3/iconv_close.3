.\" Copyright (c) Free Software Foundation, Inc.
.\"
.\" This is free documentation; you can redistribute it and/or
.\" modify it under the terms of the GNU General Public License as
.\" published by the Free Software Foundation; either version 3 of
.\" the License, or (at your option) any later version.
.\"
.\" References consulted:
.\"   GNU glibc-2 source code and manual
.\"   OpenGroup's Single Unix specification http://www.UNIX-systems.org/online.html
.\"
.TH ICONV_CLOSE 3  "March 31, 2007" "GNU" "Linux Programmer's Manual"
.SH NAME
iconv_close \- deallocate descriptor for character set conversion
.SH SYNOPSIS
.nf
.B #include <iconv.h>
.sp
.BI "int iconv_close (iconv_t " cd );
.fi
.SH DESCRIPTION
The \fBiconv_close\fP function deallocates a conversion descriptor \fIcd\fP
previously allocated using \fBiconv_open\fP.
.SH "RETURN VALUE"
When successful, the \fBiconv_close\fP function returns 0. In case of error,
it sets \fBerrno\fP and returns \-1.
.SH "CONFORMING TO"
POSIX:2001
.SH "SEE ALSO"
.BR iconv_open (3)
.BR iconv (3)
