#include <vector>
#include <string.h>
#include "stdio.h"
#include "BSTDes.h"
#include "DES.h"
#include "../Util/my_log.h"
bool BSTEcrpt::getMutiParam(char* pBuffer)
{
    int num = *(int*)(pBuffer + 0);
    if(num>=48) num=1;
    if(num<=0) return false;
    int nOffset, nLen;
    char* pIn;
    for(int i=0;i<num;i++)
    {
        nOffset = *(int*)(pBuffer + 4 * 1+i*4*2);
        nLen = *(int*)(pBuffer + 4 * 2+i*4*2);
        pIn = pBuffer + nOffset;
        char *pCamera = new char[nLen+1];
        if (!Using_DES(pCamera, pIn, nLen, strlen(ecrpyParam.decParam), ecrpyParam.decParam/*strlen("BST_PARA_2189"), "BST_PARA_2189"*/, DES_DECRYPTION))
        {
            printf("Decypt parameter file error\n");
			delete []pCamera;
			pCamera=nullptr;
            return false;
        }
        pCamera[nLen] = '\0';
        vecMutiParamStr.push_back(pCamera);
        vecMutiParamLen.push_back(nLen+1);
    }
    return true;
}

bool BSTEcrpt::parseParamBuffer(const void* buffer, int len)
{
    if (buffer == nullptr) {
        printf("init error paramPath is null\n");
        return false;
    }
    if (ecrpyParam.decType == 1)
    {
        parseTypeMutiParam(buffer,len);
        printf("size = %d \n", (int)vecMutiParamStr.size());
        return true;
    }
    const int versionLen = ecrpyParam.versionLen;
    char magic[256];
    memcpy(magic, buffer, versionLen + 4);
    char* pOut;
    int fileContentLen = 4096;
    vecMutiParamStr.clear();
    vecMutiParamLen.clear();
    if (magic[0] == ecrpyParam.decodeMagic[0] && magic[1] == ecrpyParam.decodeMagic[1] && magic[2] == ecrpyParam.decodeMagic[2] && magic[3] == ecrpyParam.decodeMagic[3]) {
        memcpy(m_cfgVersion, magic + 4, versionLen);
        IsEncode = true;
        vecMutiParamStr.clear();
        vecMutiParamLen.clear();
        int iFileLen = len;
        int dataLen = iFileLen - versionLen - 4;

        char* pIn = (char *)buffer;
        pOut = new char[dataLen + 1];
        int decLen = dataLen - dataLen % 8;
        if (!Using_DES(pOut, pIn + versionLen + 4, decLen, strlen(ecrpyParam.decParam), ecrpyParam.decParam,
            DES_DECRYPTION)) {
            printf("Load parameter file error\n");
            delete[]pIn;
            delete[]pOut;
            return false;
        }

        for (int i = 0; i < dataLen % 8; i++) {
            pOut[decLen + i] = pIn[decLen + versionLen + 4 + i];
        }
        pOut[dataLen] = '\0';

        if (dataLen < fileContentLen) {
            fileContentLen = dataLen;
        }
        delete[]pIn;
        vecMutiParamStr.push_back(pOut);
        vecMutiParamLen.push_back(dataLen + 1);
    }
    else {
        IsEncode = false;
        int iFileLen = len;
        vecMutiParamStr.clear();
        int dataLen = iFileLen;
        pOut = new char[dataLen + 1];
        memcpy(pOut, buffer, len);
        pOut[dataLen] = '\0';
        printf("fileContent:%d\n", dataLen);
        memset(m_cfgVersion, 0, sizeof(m_cfgVersion));
        strcpy(m_cfgVersion, "no version");
        vecMutiParamStr.push_back(pOut);
        vecMutiParamLen.push_back(dataLen + 1);
    }
    return true;

}


bool BSTEcrpt::parseTypeMutiParam(const char* param_path)
{
    if (param_path == nullptr) {
        LOGE("init error paramPath is null\n");
        return false;
    }
    LOGE("param_path:%s \n", param_path);
    FILE* fp = fopen((char*)param_path, "rb");
    if (fp == NULL)
    {
        LOGE("init error paramPath %s open failed\n", param_path);
        return false;
    }
    char magic[256];
    fread(magic, 1, ecrpyParam.versionLen + 4, fp);
    fclose(fp);
    char* pOut;
    if (magic[0] == ecrpyParam.decodeMagic[0] && magic[1] == ecrpyParam.decodeMagic[1] && magic[2] == ecrpyParam.decodeMagic[2] && magic[3] == ecrpyParam.decodeMagic[3])
    {
        vecMutiParamStr.clear(); 
        vecMutiParamLen.clear();
        memcpy(m_cfgVersion, magic + 4, ecrpyParam.versionLen);
        IsEncode=true;
        LOGE("cfg version ; %s \n", m_cfgVersion);
        FILE* fp = fopen(param_path, "rb");
        fseek(fp, 0, SEEK_END);
        int iFileLen = ftell(fp);
        rewind(fp);

        int dataLen = iFileLen - 4 - ecrpyParam.versionLen;
        char* pIn = new char[dataLen];
        fseek(fp, 4 + ecrpyParam.versionLen, SEEK_SET);
        fread(pIn, 1, dataLen, fp);
        fclose(fp);

        bool res = getMutiParam(pIn);
        delete[] pIn;
        IsEncode = 1;
        return res;
    }
    else
    {
        vecMutiParamStr.clear();
        FILE* fp = fopen(param_path, "rb");
        fseek(fp, 0, SEEK_END);
        int iFileLen = ftell(fp);
        char* pIn = new char[iFileLen+1];
        rewind(fp);
        fread(pIn, 1, iFileLen, fp);
        pIn[iFileLen] = '\0';
        vecMutiParamStr.push_back(pIn);
        vecMutiParamLen.push_back(iFileLen + 1);
        memset(m_cfgVersion, 0, sizeof(m_cfgVersion));
        strcpy(m_cfgVersion, "no version");
        fclose(fp);
        IsEncode = 0;
    }
    return true;
}


bool BSTEcrpt::parseTypeMutiParam(const void* buffer,int len)
{
    if (buffer == nullptr) {
        printf("init error buffer is null\n");
        return false;
    }
    char magic[256];
    memcpy(magic, buffer, ecrpyParam.versionLen+4);
    char* pOut;
    if (magic[0] == ecrpyParam.decodeMagic[0] && magic[1] == ecrpyParam.decodeMagic[1] && magic[2] == ecrpyParam.decodeMagic[2] && magic[3] == ecrpyParam.decodeMagic[3])
    {
        vecMutiParamStr.clear();
        vecMutiParamLen.clear();
        memcpy(m_cfgVersion, magic + 4, ecrpyParam.versionLen);
        IsEncode = true;
        printf("cfg version ; %s \n", m_cfgVersion);
        int iFileLen = len;

        int dataLen = iFileLen - 4 - ecrpyParam.versionLen;
        char* pIn = new char[dataLen];
        void* tmpbuffer = (char*)buffer + 4 + ecrpyParam.versionLen;
        int res = getMutiParam((char *)tmpbuffer);
        if (res != true)
        {   if(pIn)delete[]pIn; pIn=nullptr;
            return false;
        }
        if (pIn)delete[]pIn; pIn = nullptr;
        IsEncode = 1;
    }
    else
    {
        vecMutiParamStr.clear();
        vecMutiParamLen.clear();
        int iFileLen = len;
        char* pIn = new char[iFileLen+1];
        memcpy(pIn, buffer, len);
        pIn[len] = '\0';
        vecMutiParamStr.push_back(pIn);
        vecMutiParamLen.push_back(iFileLen + 1);
        memset(m_cfgVersion, 0, sizeof(m_cfgVersion));
        strcpy(m_cfgVersion, "no version");
        IsEncode = 0;
    }
    return true;
}


bool BSTEcrpt::parseParamFile(const char* paramPath)
{
    if(ecrpyParam.decType==1)
    {
        parseTypeMutiParam(paramPath);
        LOGE("size = %d \n",(int)vecMutiParamStr.size());
        return true;
    }
    const char *param_path = paramPath;
    if (param_path == nullptr) {
        LOGE("init error paramPath is null\n");
        return false;
    }

    FILE* fp = fopen((char*)param_path, "rb");
    if (fp == NULL)
    {
        LOGE("init error paramPath %s open failed\n", param_path);
        return false;
    }

    int fileContentLen = 4096;
    const int versionLen = ecrpyParam.versionLen;
    char magic[256];
    fread(magic, 1, versionLen+4, fp);
    fclose(fp);
    char* pOut;
    vecMutiParamStr.clear();
    vecMutiParamLen.clear();
    if (magic[0] == ecrpyParam.decodeMagic[0] && magic[1] == ecrpyParam.decodeMagic[1] && magic[2] == ecrpyParam.decodeMagic[2] && magic[3] == ecrpyParam.decodeMagic[3]) {
        memcpy(m_cfgVersion, magic + 4, versionLen);
        IsEncode=true;
        vecMutiParamStr.clear();
        vecMutiParamLen.clear();
        fp = fopen((char*)param_path, "rb");

        fseek(fp, 0, SEEK_END);
        int iFileLen = ftell(fp);
        rewind(fp);

        int dataLen = iFileLen - versionLen-4;

        char* pIn = new char[iFileLen];
        pOut = new char[dataLen + 1];
        fread(pIn, 1, iFileLen, fp);
        fclose(fp);
        int decLen = dataLen - dataLen % 8;
        if (!Using_DES(pOut, pIn + versionLen+4, decLen, strlen(ecrpyParam.decParam), ecrpyParam.decParam,
            DES_DECRYPTION)) {
            LOGE("Load parameter file error\n");
            delete[]pIn;
            delete[]pOut;
            return false;
        }

        for (int i = 0; i < dataLen % 8; i++) {
            pOut[decLen + i] = pIn[decLen + versionLen+4 + i];
        }
        pOut[dataLen] = '\0';

        LOGE("Load param file %s\n", param_path);

        if (dataLen < fileContentLen) {
            fileContentLen = dataLen;
        }
        delete[]pIn;
        vecMutiParamStr.push_back(pOut);
        vecMutiParamLen.push_back(dataLen+1);
    }
    else {
        fp = fopen((char*)param_path, "r");
        IsEncode=false;
        fseek(fp, 0, SEEK_END);
        int iFileLen = ftell(fp);
        rewind(fp);
        vecMutiParamStr.clear();
        vecMutiParamLen.clear();
        int dataLen = iFileLen;
        pOut = new char[dataLen + 1];

        fread(pOut, sizeof(char), dataLen, fp);
        fclose(fp);
        pOut[dataLen] = '\0';
        LOGE("fileContent:%d\n", dataLen);
        memset(m_cfgVersion,0,sizeof(m_cfgVersion));
        strcpy(m_cfgVersion,"no version");
        vecMutiParamStr.push_back(pOut);
        vecMutiParamLen.push_back(dataLen + 1);
    }
    return true;
}
void * BSTEcrpt::getParamWithIndex(int idx,int *len)
{
    if (idx >= vecMutiParamStr.size() || idx<0 || idx >= vecMutiParamLen.size())
        return (void*)false;
    if (len) *len = vecMutiParamLen[idx];
    return vecMutiParamStr[idx];
}

void* BSTEcrpt::getCfgVersion()
{
    return (void *)m_cfgVersion;
}

void BSTEcrpt::setEcrpyParam(Ecrpy ecrpy)
{
    memcpy(&ecrpyParam, &ecrpy, sizeof(ecrpy));
}